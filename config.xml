<?xml version='1.0' encoding='utf-8'?>
<widget id="tech.vernity.thainy" version="0.0.2" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>ThaiNy</name>
    <description>
        A sample Apache Cordova application that responds to the deviceready event.
    </description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <platform name="android">
        <allow-intent href="market:*" />
        <splash density="port-hdpi" src="res/screen/android/splash-port-hdpi.png" />
        <splash density="port-ldpi" src="res/screen/android/splash-port-ldpi.png" />
        <splash density="port-mdpi" src="res/screen/android/splash-port-mdpi.png" />
        <splash density="port-xhdpi" src="res/screen/android/splash-port-xhdpi.png" />
    </platform>
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
    </platform>
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="Orientation" value="portrait" />
    <engine name="android" spec="^6.2.3" />
    <plugin name="com.telerik.plugins.nativepagetransitions" spec="^0.6.5" />
    <plugin name="cordova-plugin-fingerprint-aio" spec="^1.5.0" />
    <plugin name="cordova-plugin-inappbrowser" spec="^3.0.0" />
    <plugin name="cordova-plugin-splashscreen" spec="^5.0.2" />
    <plugin name="cordova-plugin-taptic-engine" spec="^2.1.0" />
    <plugin name="cordova-plugin-vibration" spec="^3.1.0" />
    <plugin name="cordova-plugin-whitelist" spec="^1.3.3" />
    <plugin name="onesignal-cordova-plugin" spec="^2.4.4" />
</widget>
