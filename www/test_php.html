<!DOCTYPE html>
<html>
<head>
    <title>PHP Test</title>
    <script src="js/jquery-3.1.1.min.js"></script>
</head>
<body>
    <h1>PHP Connection Test</h1>
    <button id="test-btn">Test PHP Connection</button>
    <div id="result"></div>

    <script>
        $("#test-btn").click(function() {
            $.ajax({
                type: "POST",
                url: "php/check_token.php",
                data: {"token": "test_token"},
                success: function (data) {
                    $("#result").html("<h3>Success:</h3><pre>" + data + "</pre>");
                },
                error: function(xhr, status, error) {
                    $("#result").html("<h3>Error:</h3><pre>" + xhr.responseText + "</pre>");
                }
            });
        });
    </script>
</body>
</html>
