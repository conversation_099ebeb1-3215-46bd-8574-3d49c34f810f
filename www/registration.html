<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <link rel="stylesheet" type="text/css" href="css/registration.css">
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patientlist.html" onclick="transition_page_next('patientlist.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Registration</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        
                        <div class="choose_hospital">Infant (patient) information </div>

                        <div class="head-input">Infant’s ID Number</div>
                        <input class="input-register margin-btm" type="number" placeholder="Infant’s ID Number"  id = "ID" onchange="checkvalue()">
                        <div class="info_box">
                            <label  class="container">Permanent
                                <input type="checkbox"  id="Permanent" >
                                <span id="Permanentid" class="checkmark"></span>
                            </label>
                            <div id="info1" class="box-image-icon2"><div class="content_box"><img src="img/info.svg"></div></div>
                        </div>
                        
                        <div class="head-input">Date of birth <span class="Optional">- Optional</span></div>
                        <input class="input-register" type="text" placeholder="Date of birth"  id = "date" ontouchend="setting_input('date',this);" data-input="date" >

                        <div class="head-input">Gender <span class="Optional">- Optional</span></div> 
                        <div class="choose-sex">
                            <div class="choose-fm" id = "M1"   onclick="choose('male')"><span style="line-height: 44px;">Male</span></div>
                            <div class="unchoose-fm" id = "F1" onclick="choose('female')"><span style="line-height: 44px;">Female</span></div>
                        </div>
 
                        <div class="head-input">Name <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="text" placeholder="Name" id = "name">

                        <div class="head-input">Hospital Number (HN) <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="number" placeholder="Hospital Number (HN)"  id = "HN">

                      
                      
                        <div class="head-input">Ethnic <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="text" placeholder="Ethnic" id ="ethnic">


                        <!-- ------------------------ -->
                        <div class="choose_hospital">Mother information</div>

                        <div class="head-input">Mother’s name <span class="Optional">- Optional</span></div>
                        <div class="choose_sd">Thai or English ชื่อตาม ID</div>
                        <input class="input-register margin-btm" type="text" placeholder="Mother’s name" id ="mothername">

                        <div class="head-input">Mother’s ID number <span class="Optional">- Optional</span></div>
                        <div class="choose_sd">ไทยใช้เลขบัตรประชาชน ต่างชาติใช้เลขพาสปอร์ต</div>
                        <!--  check id card -->
                        <label  class="container">ID card
                            <input type="checkbox"  id="idcard" >
                            <span id="boxid" class="checkmark"></span>
                        </label>
                        <input id="check-idcard" class="input-register margin-btm" type="number" placeholder="ID card"  hidden>
                        <!--  check passport -->
                        <label class="container">Passport No
                            <input type="checkbox"  id="idpass" >
                            <span id="boxpass" class="checkmark"></span>
                        </label>
                        <input id="check-passport" class="input-register margin-btm" type="text" placeholder="Passport No" hidden>

                        <div class="head-input">Address <span class="Optional">- Optional</span></div>
                        <textarea placeholder="Address" id = "address"></textarea>

                        <div class="head-input">Telephone number <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="number" placeholder="Telephone number " id = "tel">
                        <!-- ------------------------ -->
                        <div class="choose_hospital">Contact person </div>
                        <div class="head-input">Name <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="text" placeholder="Name" id ="contactName">

                        <div class="head-input">Relationship <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="text" placeholder="Relationship " id ="contactRelate">

                        <div class="head-input">Telephone number <span class="Optional">- Optional</span></div>
                        <input class="input-register margin-btm" type="number" placeholder="Telephone number" id ="contactTel">

                        <div class="head-input">Other contact <span class="Optional">- Optional</span></div>
                        <div class="choose_sd">eg. email, line ID</div>
                        <input class="input-register margin-btm" type="text" placeholder="eg. email, line ID" id ="contactOther">
 
                        <!-- btn -->
                        <div class="save-btn" onclick = "checkandSend()">
                            <span class="set_center" >Save and Generate TNR No.</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
   
    <script src="js/push_noti.js"></script>
    <script src="js/registration.js"></script>
    
  
</body>


</html>