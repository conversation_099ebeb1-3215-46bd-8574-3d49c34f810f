<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                        <section id="title_header" class="title-main">กลับ</section>
                    </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Diagnosis </div>
                        </div>
                        <!-- a- -->
                        <div id="a_box" hidden>
                            <div class="section-head-noline margin-top">
                                <div>a. RDS</div><img id="info1" src="img/info.svg">
                            </div>
                            <label class="container_radio">Yes
                                <input type="radio" name="RDS" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">No
                                <input type="radio" name="RDS" value="No">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>


                        <!-- b- -->
                        <div id="b_box" hidden>
                            <div class="section-head-noline margin-top">
                                <div>b. Pulmonary air leak syndrome</div>
                            </div>
                            <label class="container_radio">Yes
                                <input type="radio" name="pulmonary_air_leak" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                        

                            <div class="inside-box" id="pop1" hidden>
                                <div class="choose_sd padding-left">เลือกได้มากกว่า 1 ข้อ</div>
                                <label class="container">Pneumothorax
                                    <input type="checkbox" name="pneumothorax" value="Pneumothorax">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">PIE
                                    <input type="checkbox" name="PIE" value="PIE">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Pneumomediastinum
                                    <input type="checkbox" name="pneumomediastinum" value="Pneumomediastinum">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Other
                                    <input type="checkbox" name="other" value="Other">
                                    <span class="checkmark"></span>
                                </label>
                                <input class="input-section" type="text" placeholder="Please specify" name="other_detail" hidden>
                            </div>
                            <label class="container_radio">No
                                <input type="radio" name="pulmonary_air_leak" value="No">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>

                        <!-- c- -->
                        <div class="section-head-noline margin-top">
                            <div>c. BPD</div><img id="info2" src="img/info.svg">
                        </div>
                        <div id="BPD_text" class="choose_sd_black">GA > 32 weeks</div>
                        <div class="BPD_box" hidden>
                            <label class="container_radio">Alive
                                <input type="radio" name="death" value="Alive">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">Death
                                <input type="radio" name="death" value="Dead">
                                <span class="checkmark_radio"></span>
                            </label>
                            <div class="inside-box death_box" hidden>
                                <div class="txt-bold-left">Step 1</div>
                                <div class="section-head-reg2">Death Date</div>
                                <input class="input-section" type="text" placeholder="Date of death" name = "death_date"
                                aria-placeholder="Date of death" data-date-format="D MMMM YYYY" ontouchend="setting_input('date',this);" data-input="date" >

                                <div class="section-head-reg">Death at age</div>
                                <div class="choose_sd">คำนวณอัตโนมัติ</div>
                                <input class="input-section margin-btm" type="text" placeholder="Date at - PNA" name = "death_day" disabled>
                                <input class="input-section margin-btm" type="text" placeholder="Date at - PMA" name = "death_day2" disabled>

                                <div class="inside-in2-with-pad death_reason_box" hidden>
                                        <label class="container_radio">From persistent parenchymal lung
                                            disease+RS failure
                                            <input type="radio" name="death_reason" value="From persistent parenchymal lung disease+RS failure">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <div class="info_box">
                                            <label class="container_radio">From other morbidities
                                                <input type="radio" name="death_reason" value="From other morbidities">
                                                <span class="checkmark_radio"></span>
                                            </label>
                                            <div id="info3" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                                        </div>
                                    </div>
                            </div>

                            <div class="inside-box PNA_box" hidden>
                                    <div class="txt-bold-left">Step 2 : Respiratory support + O<sub>2</sub> supplement</div>
                                    <div class="section-head-reg">Date at PNA 28 days</div>
                                    <div class="choose_sd">คำนวณอัตโนมัติ</div>
                                    <input class="input-section" type="date" data-input="date"  placeholder="เลือกวัน" name="PNA_start_date" disabled>
                                    <!--
                                    <div class="section-head-reg">Date at PNA 28 days (+3 days)</div>
                                    <div class="choose_sd">คำนวณอัตโนมัติ</div>
                                    -->
                                    <input class="input-section margin-bottom8" type="date" data-input="date"  placeholder="เลือกวัน" name="PNA_end_date" disabled hidden>

                                    <div class="info_box">
                                        <label class="container_radio margin-top8">Room air 
                                            <input type="radio" name="PNA_diagnosis" value="Room air">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <img id="info4" class="img_info" src="img/info.svg">
                                    </div>
                                    <label class="container_radio ">Intubation + ventilator
                                        <input type="radio" name="PNA_diagnosis" value="Intubation + ventilator">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <label class="container_radio margin-top8">Non-invasive positive pressure
                                        support (CPAP, BiPAP, NIPPV,
                                        HFNC ≥ 3 LPM)
                                        <input type="radio" name="PNA_diagnosis" value="Non-invasive positive pressure support (CPAP, BiPAP, NIPPV, HFNC ≥ 3 LPM)">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <label class="container_radio margin-top8">Nasal canula < 3 LPM <input type="radio" name="PNA_diagnosis"
                                            value="Nasal canula < 3 LPM">
                                            <span class="checkmark_radio"></span>
                                    </label>
                                    <div class="inside-in2-with-pad PNA_nasal_box" hidden>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.21
                                            <input type="radio" name="PNA_nasal" value="FiO2 = 0.21">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> ≥ 0.22
                                            <input type="radio" name="PNA_nasal" value="FiO2 ≥ 0.22">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    </div>
                                    <label class="container_radio margin-top8">O<sub>2</sub> box / O<sub>2</sub> flow (FiO<sub>2</sub> ≥ 0.22)
                                        <input type="radio" name="PNA_diagnosis" value="O2 box / O2 flow  (FiO2 ≥ 0.22)">
                                        <span class="checkmark_radio"></span>
                                    </label>
        
        
                            </div>
                                
                            <div class="inside-box PMA_box" hidden>
                                    <div class="txt-bold-left">Step 3 : Respiratory support + O<sub>2</sub> supplement</div>
                                    <div class="section-head-reg">Date at PMA 36 weeks</div>
                                    <div class="choose_sd">คำนวณอัตโนมัติ</div>
                                    <input class="input-section" type="date" data-input="date"  placeholder="เลือกวัน" name="PMA_start_date" disabled>
                                    <div class="section-head-reg">Date at PMA 36 weeks (+2 days)</div>
                                    <div class="choose_sd">คำนวณอัตโนมัติ</div>
                                    <input class="input-section margin-bottom8" type="date" data-input="date"  placeholder="เลือกวัน" name="PMA_end_date" disabled>

                                    <div class="info_box">
                                        <label class="container_radio margin-top8">Room air 
                                            <input type="radio" name="PMA_diagnosis" value="Room air">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <img id="info5" class="img_info" src="img/info.svg">
                                    </div>
                                    <label class="container_radio">Intubation + ventilator
                                        <input type="radio" name="PMA_diagnosis" value="Intubation + ventilator">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <div class="inside-in2-with-pad PMA_intubation_box" hidden>
                                        <label class="container_radio">Fi<sub>2</sub> = 0.21
                                            <input type="radio" name="PMA_intubation" value="FiO2 = 0.21">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.22 - 0.29
                                            <input type="radio" name="PMA_intubation" value="FiO2 = 0.22 - 0.29">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.3 - 0.7
                                            <input type="radio" name="PMA_intubation" value="FiO2 = 0.3 - 0.7">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> ≥ 0.71
                                            <input type="radio" name="PMA_intubation" value="FiO2 ≥ 0.71">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    </div>
                                    <label class="container_radio margin-top8">CPAP / BiPAP / NIPPV/ HFNC ≥ 3 LPM
                                        <input type="radio" name="PMA_diagnosis" value="CPAP / BiPAP / NIPPV/ HFNC ≥ 3 LPM">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <div class="inside-in2-with-pad PMA_CPAP_box" hidden>
                                        <label class="container_radio ">FiO<sub>2</sub> = 0.21
                                            <input type="radio" name="PMA_CPAP" value="FiO2 = 0.21">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.22 - 0.29
                                            <input type="radio" name="PMA_CPAP" value="FiO2 = 0.22 - 0.29">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.3 - 0.7
                                            <input type="radio" name="PMA_CPAP" value="FiO2 = 0.3 - 0.7">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> ≥ 0.71
                                            <input type="radio" name="PMA_CPAP" value="FiO2 ≥ 0.71">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    </div>
                                    <label class="container_radio margin-top8">NC 1.0 - 2.99 LPM
                                        <input type="radio" name="PMA_diagnosis" value="NC 1.0 - 2.99 LPM">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <div class="inside-in2-with-pad PMA_NC_1_box" hidden>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.21
                                            <input type="radio" name="PMA_NC_1" value="FiO2 = 0.21">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.22 - 0.29
                                            <input type="radio" name="PMA_NC_1" value="FiO2 = 0.22 - 0.29">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.3 - 0.7
                                            <input type="radio" name="PMA_NC_1" value="FiO2 = 0.3 - 0.7">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> ≥ 0.71
                                            <input type="radio" name="PMA_NC_1" value="FiO2 ≥ 0.71">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    </div>
                                    <label class="container_radio margin-top8">NC ≤ 0.99 LPM
                                        <input type="radio" name="PMA_diagnosis" value="NC ≤ 0.99 LPM">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <div class="inside-in2-with-pad PMA_NC_2_box" hidden>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.21
                                            <input type="radio" name="PMA_NC_2" value="FiO2 = 0.21">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.22 - 0.29
                                            <input type="radio" name="PMA_NC_2" value="FiO2 = 0.22 - 0.29">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.3 - 0.7
                                            <input type="radio" name="PMA_NC_2" value="FiO2 = 0.3 - 0.7">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> ≥ 0.71
                                            <input type="radio" name="PMA_NC_2" value="FiO2 ≥ 0.71">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    </div>
                                    <label class="container_radio margin-top8">O<sub>2</sub> hood / O<sub>2</sub> box
                                        <input type="radio" name="PMA_diagnosis" value="O2 hood / O2 box">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                    <div class="inside-in2-with-pad PMA_O2_box"  hidden>
                                        <label class="container_radio">FiO<sub>2</sub> = 0.22 - 0.29
                                            <input type="radio" name="PMA_O2" value="FiO2 = 0.22 - 0.29">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <label class="container_radio">FiO<sub>2</sub> ≥ 0.3
                                            <input type="radio" name="PMA_O2" value="FiO2 ≥ 0.3">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    </div> 
                                </div>

                                <div class="inside-box">
                                    <div class="txt-bold-left">BPD (NIH definition)</div>
                                    <div name="old_definition" class="section-head-reg margin-btm">-</div>
                                    <div class="txt-bold-left">BPD (Workshop 2018 definition)</div>
                                    <div name="new_definition" class="section-head-reg margin-btm">-</div>
                                </div>
                        </div>
                       

                        <!-- d- -->
                        <div class="section-head-noline margin-top">
                            <div>d. PPHN </div>
                        </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="PPHN" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id="pop3" hidden>
                            <label class="container_radio">Require pulmonary vasodilator
                                <input type="radio" name="PPHN_require_pulmonary_vasodilator" value="Require pulmonary vasodilator">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">Not require pulmonary vasodilator
                                <input type="radio" name="PPHN_require_pulmonary_vasodilator" value="Not Require pulmonary vasodilator">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>
                        <label class="container_radio">No
                            <input type="radio" name="PPHN" value="No">
                            <span class="checkmark_radio"></span>
                        </label>


                        <!-- e- -->
                        <div id="e_box" hidden>
                            <div class="section-head-noline margin-top">
                                <div>e. PDA </div><img id="info6" src="img/info.svg">
                            </div>
                            <label class="container_radio">Yes
                                <input type="radio" name="PDA" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                            <div class="inside-box" id="pop4" hidden>
                                <div class="info_box">
                                    <label class="container">Require medical ligation 
                                        <input type="checkbox" name="PDA_require_medical_ligation" value="Require Medical ligation">
                                        <span class="checkmark"></span>
                                    </label>
                                    <div id="info7" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                                </div>
                                <label class="container">Require surgical ligation
                                    <input type="checkbox" name="PDA_require_surgical_ligation" value="Require Surgical ligation ">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Require supportive care
                                    <input type="checkbox" name="PDA_require_support_care" value="Require supportive care	">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <label class="container_radio">No
                                <input type="radio" name="PDA" value="No">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>

                        <!-- -f -->
                        <div class="section-head-noline margin-top">
                            <div>f. NEC</div> <img id="info8" src="img/info.svg">
                        </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="NEC" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id="pop5" hidden>
                            <label class="container_radio">Require surgical treatment
                                <input type="radio" name="NEC_require_surgical_treatment" value="Require surgical treatment">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">Not require surgical treatment
                                <input type="radio" name="NEC_require_surgical_treatment" value="Not require surgical treatment">
                                <span class="checkmark_radio"></span>
                            </label>

                        </div>
                        <label class="container_radio">No NEC
                            <input type="radio" name="NEC" value="No">
                            <span class="checkmark_radio"></span>
                        </label>



                        <!-- G- -->
                        <div id="g_box" hidden>
                            <div class="section-head-noline margin-top">
                                <div>g. ROP</div>
                            </div>
                            <label class="container_radio">Yes
                                <input type="radio" name="ROP" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                            <div class="inside-box" id="pop6" hidden>
                                <div class="">Maximum staging </div>
                                <label class="container_radio">1
                                    <input type="radio" name="maximum_staging" value="1 ">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">2
                                    <input type="radio" name="maximum_staging" value="2">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">3
                                    <input type="radio" name="maximum_staging" value="3">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">4
                                    <input type="radio" name="maximum_staging" value="4">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">5
                                    <input type="radio" name="maximum_staging" value="5">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div class="margin-top8">Treatment</div>
                                <label class="container">Laser
                                    <input type="checkbox" name="treatment_laser" value="Laser">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Cryotherapy
                                    <input type="checkbox" name="treatment_cryotherapy" value="Cryotherapy">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Anti-VEGF
                                    <input type="checkbox" name="treatment_anti_VEGF" value="Anti-VEGF">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">No treatment
                                    <input type="checkbox" name="treatment_no" value="No treatment">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <label class="container_radio">No ROP
                                <input type="radio" name="ROP" value="No">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">Dead before timing diagnosis
                                <input type="radio" name="ROP" value="Dead before timing diagnosis">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">No available data / No ROP screening
                                <input type="radio" name="ROP" value="No available data / No ROP screening ">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>

                        <!-- - -->
                        <div id="h_box" hidden>
                            <div class="section-head-noline margin-top">
                                <div>h. IVH</div>
                            </div>
                            <label class="container_radio">Yes
                                <input type="radio" name="IVH" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                            <div class="inside-box" id="pop7" hidden>
                                <div class="">Maximum grading</div>
                                <label class="container_radio">1
                                    <input type="radio" name="maximum_grading" value="1 ">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">2
                                    <input type="radio" name="maximum_grading" value="2">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">3
                                    <input type="radio" name="maximum_grading" value="3">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">4
                                    <input type="radio" name="maximum_grading" value="4">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div class="margin-top8">Require shunt/reservoir</div>
                                <label class="container_radio">Yes
                                    <input type="radio" name="require_shunt" value="Yes">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <label class="container_radio">No
                                    <input type="radio" name="require_shunt" value="No">
                                    <span class="checkmark_radio"></span>
                                </label>

                            </div>
                            <label class="container_radio">No IVH
                                <input type="radio" name="IVH" value="No IVH">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">No data available / No U/S screening
                                <input type="radio" name="IVH" value="No data available / No U/S screening">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>

                        <!-- - -->
                        <div id="i_box" hidden>
                            <div class="section-head-noline margin-top">
                                <div>i. PVL</div> <img id="info9" src="img/info.svg">
                            </div>
                            <label class="container_radio">Yes
                                <input type="radio" name="PVL" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">No PVL
                                <input type="radio" name="PVL" value="No PVL">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">No data available / No U/S screening
                                <input type="radio" name="PVL" value="No data available / No U/S screening">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>

                        <!-- btn -->
                        <div class="save-btn" onclick="save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src = "js/concurance.js"></script><script src="js/main.js"></script> 
    <script src="js/section5_9.js"></script>
</body>

</html>