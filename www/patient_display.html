<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/patient_display.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body>



    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patientlist.html" onclick="transition_page_back('patientlist.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section" >
                        <div class="option_icon cancel-refer" onclick = "cancel()" id = "cancel" hidden>ยกเลิก Refer</div>
                        <a href="refer_send1.html" onclick="transition_page_next('refer_send1.html')">
                            <img class="option_icon" src="img/return.svg" id = "refer" hidden>
                        </a>
                        <!-- <img class="option_icon" src="img/icon2.svg"> -->

                    </section>
                </div>
            </section>
                <section class="title" id = "name">เด็กชายสมมุติ ร่างกายอ่อนแอ</section>
            <div class="tn-box">
                <div  id = "TNR">TN#: 192-456-2456</div>
                <div id = "HN">HN</div>
                <div id = "mom">Mother</div>
            </div>
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding select_hospital">
                <div class="content_box">
                    <div class="list-box">

                        <div class="progress-box">
                            <div class="progress-texxt">
                                <div>ความคืบหน้าในการบันทึก</div>
                                <div id = "percent">0%</div>
                            </div>
                            <div class="progress-bar">
                                <div id="progress_bar" class="progress-line"></div>
                            </div>
                        </div>
                        <div class="choose_hospital">แบบบันทึกข้อมูลลงทะเบียน</div>
                        <div id="registration" class="fillter-each">
                                <div><img src="img/correct-section.svg" id = "register"></div>
                                <img src="img/next3.svg">Registration
                            <img src="img/next3.svg">

                        </div>
                        <div class="choose_hospital">แบบบันทึกข้อมูลทางการแพทย์</div>
                        <div id="section1" class="fillter-each " >
                            <div><img src="img/correct-section.svg" id = "section1x"></div>
                            <img src="img/next3.svg">
                            Section 1: Admission data

                        </div>
                        <div id="section2" class="fillter-each">
                            <div><img src="img/correct-section.svg"  id = "section2x"></div>
                            <img src="img/next3.svg">
                            Section 2: Maternal data

                        </div>
                        <div id="section3" class="fillter-each">
                            <div><img src="img/correct-section.svg"  id = "section3x"></div>
                            <img src="img/next3.svg">
                            Section 3: Perinatal data

                        </div>
                        <div id="section4" class="fillter-each">
                            <div><img src="img/correct-section.svg"  id = "section4x"></div>
                            <img src="img/next3.svg">
                            Section 4: Neonatal data

                        </div>
                        <div id="section5" class="fillter-each">
                            <div><img src="img/correct-section.svg"  id = "section5x"></div>
                            <img src="img/next3.svg">
                            Section 5: Hospital course

                        </div>
                        <div id="section6" class="fillter-each">
                            <div><img src="img/correct-section.svg"  id = "section6x"></div>
                            <img src="img/next3.svg">
                            Section 6: Short-term outcomes

                        </div>

                      <div class="app-btn" id = "submit" onclick = "submit()" hidden>
                            <span class="set_center">Submit</span>
                        </div>
                          <!-- <div class="reject-btn">
                            <span class="set_center">Reject</span>
                        </div> -->
                    </div>

                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
   
   
    <script src="js/patient_display.js"></script>
</body>

</html>