<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/patient_display.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body>



    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="search_patient_result.html" onclick="transition_page_back('search_patient_result.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section" hidden>
                        <img class="option_icon" src="img/return.svg">
                        <!-- <img class="option_icon" src="img/icon2.svg"> -->

                    </section>
                </div>
            </section>
                <section class="title" id = "2">เด็กชายสมมุติ ร่างกายอ่อนแอ</section>
            <div class="tn-box">
                <div>TN#: <span id = "1">************</span></div>
                <div>HN: <span id = "3">7327934</span></div>
                <div>มารดา: <span id= "4">นางอริศยา ร่างกายอ่อนแอ</span></div>
            </div>
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding select_hospital">
                <div class="content_box">
                 

                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
    
    <script src = "js/search_patient_show_patient.js"></script>
</body>

</html>