<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden >
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            
                            <div>HIE</div><img id = "info1"  src="img/info.svg">
                        </div>

                        <!-- a -->
                        
                        <div>a. Severity within 6 hr (NICHD protocol)<div class="box-image-icon" ><div class="content_box"><img id = "info2" src="img/info.svg"></div></div></div>
                        <label class="container_radio">Mild
                            <input type="radio" name="severity_within_6_hr" value="Mild">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Moderate
                            <input type="radio" name="severity_within_6_hr" value="Moderate">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Severe
                            <input type="radio" name="severity_within_6_hr" value="Severe ">
                            <span class="checkmark_radio"></span>
                        </label>

                        <!-- b -->
                        <div>b. Severity (the worst) (NICHD protocol)<div class="box-image-icon"><div class="content_box"><img id = "info3"src="img/info.svg"></div></div> </div> 
                        <label class="container_radio">Mild
                            <input type="radio" name="severity_the_worst" value="Mild">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Moderate
                            <input type="radio" name="severity_the_worst" value="Moderate">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Severe
                            <input type="radio" name="severity_the_worst" value="Severe ">
                            <span class="checkmark_radio"></span>
                        </label>

                        <!-- c -->
                        <div>c. Receive TH<div class="box-image-icon"><div class="content_box"><img id = "info4"src="img/info.svg"></div></div></div>
                        <label class="container_radio">Yes
                            <input type="radio" name="receive_TH" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>

                        <div class="inside-box-with-pad" id="pop1" hidden>
                            <div>Cooling start at date</div>
                            <input class="input-section" name="cooling_start_at_date" type="text" placeholder="เลือกวัน" ontouchend="setting_input('date',this);" data-input="date" 
                                id="date">
                            <div class="margin-top8">Cooling start at time</div>
                            <input class="input-section" name="cooling_start_at_time" type="text" placeholder="00:00" id="date" ontouchend="setting_input('time',this);">

                            <div class="margin-top8"><span class="">Start at </span><input class="input-small margin-left15" type="" placeholder="N/A" name="after_birth" disabled> <span class="margin-left15"> hr:min after birth</span></div>
                            <div class="margin-top8">Temperature at start cooling: </div>
                            <div><input class="input-small" type="number" placeholder="-" name="temperature"> <span class="margin-left15"> ᵒC</span></div>
                            
                            <div class="margin-top8">Complete cooling </div>
                            <label class="container_radio">Yes (72 h)
                                <input type="radio" name="complete_cooling" value="Yes">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">No
                                <input type="radio" name="complete_cooling" value="No">
                                <span class="checkmark_radio"></span>
                            </label>
                            <div class="inside-in-with-pad" id="pop2" hidden>
                                <div class="margin-top8">
                                    <span class="">Duration of cooling </span><input class="input-small margin-left15" type="number" placeholder="00" name="duration"> <span class="margin-left15"> h</span></div>
                                <div class="margin-top8">Reason of incomplete cooling</div>
                                <input  class="input-section" type="text" placeholder="Please specify reason" name="specify">
                            </div>

                        </div>
                        <label class="container_radio">No
                            <input type="radio" name="receive_TH" value="No">
                            <span class="checkmark_radio"></span>
                        </label>


                        <!-- d -->
                        <div>d. Seizure</div>
                        <label class="container_radio">Yes
                            <input type="radio" name="seizure" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box-with-pad" id = "pop3" hidden>
                            <div>Receive anticonvulsant <span class="choose_sd ">เลือกได้มากกว่า 1 ข้อ</span></div>
                            <label class="container">Phenobarbital
                                <input type="checkbox" name="seizure_phenobarbital" value="Phenobarbital">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Phenytoin
                                <input type="checkbox" name="seizure_phenytoin" value="Phenytoin">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Midazolam
                                <input type="checkbox" name="seizure_midazolam" value="Midazolam">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Levetiracetam
                                <input type="checkbox" name="seizure_levetiracetam" value="Levetiracetam">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Topiramate
                                <input type="checkbox" name="seizure_topiramate" value="Topiramate">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Diazepam
                                <input type="checkbox" name="seizure_diazepam" value="Diazepam">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Other
                                <input type="checkbox" name="seizure_other_main" value="Other">
                                <span class="checkmark"></span>
                            </label>
                           
                            <div class="inside-in-with-pad" name = "other_list" hidden>
                                <div id = "list" >
                                <div class="bar-delete-list-input"><div class="content_box">
                                    <input class="input-section margin-btm" type="text" placeholder="ชื่อยา" name="otherx1" >
                                    <div class="circle-red" onclick = "del(this)"><img src="img/delete.svg"></div>
                                </div></div>
                               
                                </div>
                                <div class="plus-box" onclick = "add()">
                                    <div class="plus">
                                        <div class="content_box">
                                            <img src="img/add.svg">
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                           
                            <label class="container">None
                                <input type="checkbox" name="seizure_none" value="None">
                                <span class="checkmark"></span>
                            </label>
                            <div class="margin-top">Total anticonvulsant use</div>
                            <input class="input-small" type="" placeholder="N/A" name="start_anticonvulsant" disabled>
                        </div>
                        <label class="container_radio">No
                            <input type="radio" name="seizure" value="No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <!-- e -->
                        <div>e. aEEG / EEG done </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="aEEG_or_EGG_done" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        
                        <div class="inside-box-with-pad" id="pop4" hidden>
                            <div class="choose_sd">เลือกได้มากกว่า 1 ข้อ</div>
                            <div>Result</div>
                            <label class="container">Normal background
                                <input type="checkbox" name="normal_background" value="Normal background">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Abnormal background
                                <input type="checkbox" name="abnormal" value="Abnormal">
                                <span class="checkmark"></span>
                            </label>
                            
                            <label class="container">Seizure
                                <input type="checkbox" name="aEEG_seizure" value="seizure">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Other
                                <input type="checkbox" name="result_other_main" value="Other_aEEG">
                                <span class="checkmark"></span>
                            </label>
                            <input class="input-section" type="text" placeholder="Please specify" name="result_other" hidden>
                        </div>
                        
                        <label class="container_radio">No
                                <input type="radio" name="aEEG_or_EGG_done" value="No">
                                <span class="checkmark_radio"></span>
                            </label>
                        <!-- btn -->
                        <div class="save-btn" onclick="save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5_12.js"></script>
</body>

</html>