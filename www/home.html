<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/home.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>
        <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body style="background-color: #fff" hidden>


        <section class="main_section_nopadding">
                <div class="content_box_flow">
                        <div class="frame_view_padding">
                                <div class="content_box">
                                        <div class="head-home">
                                                <img src="img/profile.svg">
                                                <div class="name-box">
                                                        <div class="name-person" id = "fullname"></div>
                                                        <div class="name-hospital" id = "hospital"></div>
                                                </div>
                                                <div class="rank-box">
                                                        <span class="rank" id = "status"></span>
                                                </div>

                                        </div>
                                        <div class="list-content">
                                                <div id="patient_list" class="list-home margin-i1tem">
                                                        <img src="img/list.svg">
                                                        <div><span>รายการผู้ป่วย / เพิ่มผู้ป่วยใหม่</span><img src="img/next3.svg"></div>
                                                </div>
                                                <div id="track_tn" class="list-home" hidden>
                                                        <img src="img/search2.svg">
                                                        <div><span>ค้นหาหมายเลข TN</span><img src="img/next3.svg"></div>
                                                </div>
                                                <div id="export" class="list-home">
                                                        <img src="img/export.svg">
                                                        <div><span>นำออกข้อมูล</span><img src="img/next3.svg"></div>
                                                </div>
                                                <div id="help" class="list-home">
                                                        <img src="img/help.svg">
                                                        <div><span>ช่วยเหลือและสนับสนุน</span><img src="img/next3.svg"></div>
                                                </div>
                                                <div id="approve" class="list-home" hidden>
                                                        <img src="img/approve2.svg">
                                                        <div><span>อนุมัติบัญชีผู้ใช้</span><img src="img/next3.svg"></div>
                                                </div>
                                                <div id="setting" class="list-home pagging-2item">
                                                        <img src="img/setting.svg">
                                                        <div><span>ตั้งค่า</span><img src="img/next3.svg"></div>
                                                </div>
                                                <div id="logout" class="list-home pagging-2item">
                                                        <img src="img/logout.svg">
                                                        <div><span>ออกจากระบบ</span><img src="img/next3.svg"></div>
                                                </div>
                                        </div>

                                </div>
                        </div>
                </div>
        </section>

        <script src="cordova.js"></script>
        <script src="js/transition.js"></script>
        <script src="js/main.js"></script>
        <!-- <script src="js/push_noti.js"></script> -->
        <script src="js/home.js"></script>
        
        <script>
         //       window.onload = setname("นพ.","พสิษฐ์","พงศ์พจน์เกษม","รามา","หัวหน้า");
        </script>
</body>

</html>