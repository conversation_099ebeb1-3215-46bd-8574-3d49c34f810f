<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/patientlist.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script src="js/jquery.transit.min.js"></script>
    

</head>

<body>
    <div class="popup-login-patientlist" id="popup-filter" hidden>
        <div class="content-box">
            <div class="ipd-popup" id="popup-filter-box">
                <div class="popup-box-patientlist2">
                    <img src="img/triangle.svg">
                    <div class="height-filter-list"><div class="content_box_flow">
                        <div class="fillter-head">วันที่ Admit</div>
                        <div class="fillter-content">
                            <div class="fillter-each">
                                <label class="container_radio">ทั้งหมด
                                    <input id="filter-all" type="radio" checked="checked" name ="date" id = "date" value = "all" >
                                    <span class="checkmark_radio"></span>
                                </label>
                            </div>
                            <div class="fillter-each">
                                <label class="container_radio">ช่วงวันที่ Admit
                                    <input id="filter-dateAdmit" type="radio"  name ="date" id = "date" value = "not all">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div class="head-in">วันเริ่มต้น</div>
                                <input class="input-section" type="text" placeholder="เลือกวันที่เริ่มต้น" ontouchend="setting_input('date',this);" data-input="date" data-validate="l" id = "date_start" disabled>
                                <div class="margin-top8 head-in ">วันสิ้นสุด</div>
                                <input class="input-section margin-btm" type="text" placeholder="เลือกวันที่สิ้นสุด" ontouchend="setting_input('date',this);" data-input="date" data-validate="r" id = "date_stop" disabled>
                            </div>

                        </div>
                        <div class="fillter-head">Criteria</div>
                        <div class="fillter-content">
                            <div class="fillter-each">
                                <label class="container">GA < 32 weeks GA
                                    <input type="checkbox" id="GA" value="">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="fillter-each">
                                <label class="container">BW < 1,500g
                                    <input type="checkbox" id="BW" value="">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="fillter-each">
                                <label class="container">HIE
                                    <input type="checkbox" id="HIE" value="">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="fillter-each">
                                <label class="container">Major anomalies
                                    <input type="checkbox" id="major" value="">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                        </div>

                        <div class="fillter-head">Complete Status</div>
                        <div class="fillter-content margin-btm">
                            <div class="fillter-each">
                                <label class="container">Incompleted
                                    <input  type="checkbox" name = "completed" value = "incompleted">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="fillter-each">
                                <label class="container">Completed
                                    <input  type="checkbox" name = "completed" value = "completed"> 
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                        </div>
                    </div></div>

                    <div class="popup-btn-patientlist">
                        <div id="filter-cancel">ยกเลิก</div>
                        <div class="border" id="filter-accept">นำไปใช้</div>
                    </div>

                </div>
            </div>
        </div>

    </div>

    <div class="popup-login-patientlist" id="popup-sort" hidden >
        <div class="content-box">
            <div class="ipd-popup" id="popup-sort-box">
                <div class="popup-box-patientlist">
                    <img src="img/triangle.svg">
                    <div class="fillter-head">เรียงข้อมูลโดย</div>
                    <div class="fillter-content">
                            <div class="fillter-each">
                                    <label class="container_radio">หมายเลข TN
                                        <input id="sort-TN" type="radio" name="1" value = "TNR" checked="checked">
                                        <span class="checkmark_radio"></span>
                                    </label>
                                </div>
                        <div class="fillter-each">
                            <label class="container_radio">ชื่อผู้ป่วย
                                <input id="sort-name" type="radio"  name="1" value = "fullname">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>
                        
                        <div class="fillter-each">
                            <label class="container_radio">วันที่ Admit
                                <input id="sort-dateAdmit" type="radio" name="1" value = "created_date">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>

                    </div>
                    <div class="fillter-head">ลำดับการเรียง</div>
                    <div class="fillter-content margin-btm">
                        <div class="fillter-each">
                            <label class="container_radio">จากน้อยไปมาก
                                <input id="sort-min" type="radio" checked="checked" name="2" value = "ASC">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>
                        <div class="fillter-each">
                            <label class="container_radio">จากมากไปน้อย
                                <input id="sort-max" type="radio" name="2" value = "DESC">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>
                    </div>
                    <div class="popup-btn-patientlist">
                        <div id="sort-cancel">ยกเลิก</div>
                        <div class="border" id="sort-accept">นำไปใช้</div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="home.html" onclick="transition_page_back('home.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section">
                        <img id="filter" class="option_icon" src="img/icon1.svg">
                        <img id="sort" class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
            <section class="title">รายการผู้ป่วย</section>
            <div class="search-box">
                <img src="img/search.svg">
                <input id="search" type="text" placeholder="ค้นหาผู้ป่วย" name = "search">
            </div>
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div id="refer" class="refer each-list bg-gray " hidden>
                        <div class="refer-noti">มีผู้ป่วย Refer มาโรงพยาบาลท่าน</div>
                        <div id="refer_count" class="number-noty"></div>
                        <img src="img/next2.svg">
                    </div>
                    <div class="list-box patientlist" id="list" hidden>
                        <!-- <div class="each-list">
                            <div class="name-patient">1เด็กชายสมมุติ ร่างกายอ่อนแอ</div>
                            <div class="tn-patient">TN#: XXXXXXXXXX</div>
                            <div class="save-btn">กำลังบันทึก</div>
                            <img src="img/next2.svg">
                        </div>
                       -->
                    </div>
                    <div id="add" class="add_btn">
                        <img src="img/add.svg">
                        <div class="add-txt_btn">เพิ่มผู้ป่วยใหม่</div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <footer class="footer">
        <div id="active" class="">
            <img src="img/activegreen.svg">
            <div class="footer-text text-green">Active</div>
        </div>
        <div id="inactive" class="">
            <img src="img/inactivegray.svg">
            <div class="footer-text text-gray">Inactive</div>
        </div>
    </footer>

    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
   
    <script src="js/main.js"></script>
    <script src="js/patientlist.js"></script>
    <script src="js/animate_filter_box.js"></script>
    
</body>

</html>