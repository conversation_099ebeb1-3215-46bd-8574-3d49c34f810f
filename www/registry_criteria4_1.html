<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/criteria_result.css">
   

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body  class="cri4">

    <section class="header">
        <div class="content_box">
            <section class="bar_single_line" hidden>
                <div class="content_box">
                    <a href="home.html" onclick="transition_page_back('home.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/backwhite.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <!-- <section class="title">Registry Criteria</section> -->
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <img class="cri-img " src="img/complete.svg">
                        <div class="cri-text1">ลงทะเบียนเรียบร้อย</div>
                        <div class="cri-text2 color-text-green">ระบบได้ Generate TNR No. สำหรับ<br/>
                              <div id  = "Cname">  “เด็กชายสมมุติ ร่างกายอ่อนแอ” </div>
                                  เรียบร้อยแล้ว
                        </div>

                        <div class="section-head">
                            <div>Infant (patient) information</div>
                        </div>
                        <div class="tn-box">
                                <div id = "TN">TN#: ************</div>
                                <div id = "HN">HN: 7327934</div>
                                <div id = "mom">มารดา: นางอริศยา ร่างกายอ่อนแอ</div>
                            </div>
                        <!-- btn -->
                               <div class="btn-footer-criteria clearfix">
                                   
                                    <div class="btn-content back-ntn">   <a href="patientlist.html" onclick="transition_page_next('patientlist.html')"> <span class="set_center">กลับไปหน้า “รายการผู้ป่วย” </span></div> </a>
                                    <div class="btn-content back-ntn patient_display"> <div class="btn-content next-ntn color-btn-green"><span class="set_center">เริ่มการบันทึกข้อมูลส่วนต่อไป <img src="img/next.svg"></span></div>
                        </div>
                      

                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
            window.onload =function() {
                document.getElementById("Cname").innerHTML =sessionStorage.Cname.toString();
                document.getElementById("TN").innerHTML =sessionStorage.TNR.toString();
                document.getElementById("HN").innerHTML = sessionStorage.HN.toString();
                document.getElementById("mom").innerHTML = sessionStorage.mothername.toString();
            }
            $(".patient_display").unbind('click');

    $(".patient_display").on("click",function(){
        var TNR =sessionStorage.TNR;
        sessionStorage.Cname  = undefined;
        sessionStorage.TNR = undefined;
        sessionStorage.HN = undefined;
        sessionStorage.mothername = undefined;

        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/set_session_TNR.php",
            data:{TNR:TNR},
            success: function (data) {
                var result = JSON.parse(data);

                if(result.status == 1){
                   // alert(result.criteria);
                    sessionStorage.criteria = result.criteria;
                   // alert(sessionStorage.criteria);
                    window.location = "patient_display.html"
                }else{
                    alert_fail({text: "กำลังมีผู้อื่นบันทึกข้อมูลอยู่", header: "ข้อผิดพลาด"});
                }
            },
          });
    })
            </script>


<script src = "cordova.js"></script>
<script src="js/transition.js"></script>
<script src="js/main.js"></script>

    <script src="js/registration.js"></script>
</body>

</html>