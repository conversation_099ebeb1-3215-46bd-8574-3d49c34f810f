<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/home.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>
        <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body style="background-color: #fff" id = "body">
    <!-- <div id="popup_background" class="popup-login" hidden="" style="display: block;"></div>
    <div id="popup_container" class="popup-fake" hidden="" style="display: block;">
        <div class="content_box">
            <div class="ipd-popup">
                <div id="popup_box" class="popup-box2 color-white" style="opacity: 1; transform: scale(1, 1);">
                    <div class="content_box_flow">
                        เลือก Permanent เมื่อเลขที่กรอกเป็นเลขบัตรประชาชนของทารก โดยเมื่อ Submit แล้วจะไม่สามารถเปลี่ยนแปลงได้<br><br>
                        ถ้ายังไม่มีเลขบัตรประจำตัวประชาชนของทารก ให้ใช้เลข Hospital Number แทน และไม่เลือก Permanent<br><br>
                        * หากยังไม่เลือก Pemanment จะไม่สามารถ Submit ข้อมูลของทารกคนนี้ได้
                    </div>
                    <div id="popup_button_html">
                        <div id="popup_button_1" class="popup-btn">
                            <span id="popup_button_text_1">เรียบร้อย</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> -->
    <script src = "js/main.js"></script>
    <script>
            window.onload= function(){
                show_info("กรอกคำไม่ได้");
            }
        </script>
</body>

</html>
