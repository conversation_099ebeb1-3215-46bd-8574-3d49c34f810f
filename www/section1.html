<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

    <!-- date -->
    <link rel="stylesheet" href="https://techvernity.com/asset/backend/date/ui.css">
    <script src="https://techvernity.com/asset/backend/date/jq.js"></script>
    <script src="https://techvernity.com/asset/backend/date/jq3.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patient_display.html" onclick="transition_page_back('patient_display.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " > <!---hidden-->
                        <!-- <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg"> -->

                    </section>
                </div>
            </section>
                <section class="title">Section 1</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Section 1: Admission data </div>
                        </div>
                        <label class="container_radio">Inborn
                            <input type="radio"   name = "admission_status" value = "Inborn">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop1" hidden> <!---hidden-->
                            <div class="info_box">
                                <label class="container_radio">Intrauterine transfer
                                    <input  type="radio"  name = "intrauterine_transter" value = "Intrauterine transfer">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div id="info1" class="box-image-icon"><div class="content_box"><img src="img/info.svg" ></div></div>
                            </div>

                            <div class="info_box">
                                <label class="container_radio">Not intrauterine transfer
                                    <input type="radio"  name = "intrauterine_transter" value = "Not intrauterine transfer">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div id="info2" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                            </div>
                        </div>
                        <label class="container_radio">Outborn /
                            Transfer from another hospital
                            <input type="radio"  name = "admission_status" value = "Outborn / Transfer from another hospital">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop2" hidden> <!---hidden-->
                            <label class="container_radio">TNR member hospital
                                <input type="radio"  name = "transfer_member" value = "TNR member hospital">
                                <span class="checkmark_radio"></span>
                            </label>
                            <select class="select-section" aria-placeholder="เลือกโรงพยาบาล" id = "hospital_name" name = "hospital_name" hidden>
                                <option value="" selected disabled>เลือกโรงพยาบาล</option>
                                <option value="เลือกโรงพยาบาล1">เลือกโรงพยาบาล1</option>
                                <option value="เลือกโรงพยาบาล2">เลือกโรงพยาบาล2</option>
                            </select>
                            <label class="container_radio">Non TNR member hospital
                                <input type="radio"  name = "transfer_member" value ="Non TNR member hospital">
                                <span class="checkmark_radio"></span>
                            </label>
                            <input class="input-section " type="text" placeholder="ชื่อโรงพยาบาล"  id = "non_hospital_name"name = "non_hospital_name" hidden>
                        </div>
                        <label class="container_radio">BBA
                            <input type="radio" name = "admission_status" value = "BBA">
                            <span class="checkmark_radio"></span>
                        </label>




                        <!-- ------------------------ -->
                        <div class="section-head">
                            <div>Admission Info </div>
                        </div>
                        <div hidden> <!---hidden-->
                       
                        <div class="select-head">Admitted to</div>
                        <label class="container_radio">Sick newborn ward
                            <input type="radio" name = "admitted_to" value = "Sick newborn ward"> 
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">NICU
                            <input type="radio" name = "admitted_to" value = "NICU">
                            <span class="checkmark_radio"></span>
                        </label>
                        </div>

                        
                        <div class="select-head margin-top">Date of admission in our center<div id="info3" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                        <input type = "text" placeholder = "test date" id = "test">
                      
                        <input class="input-section " type="text" placeholder="Date of Admission in our center"
                            aria-placeholder="Date of Admission in our center" id = "admission_date" name = "admission_date" ontouchend="setting_input('date',this);" data-input="date" data-validate="l" hidden>
                        <div class="select-head margin-top">Date of discharge in our center<div id="info4" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                        <input class="input-section " type="text" placeholder="Date of Discharge in our center"
                            aria-placeholder="Date of Admission in our center" id = "discharge_date" name = "discharge_date" ontouchend="setting_input('date',this);" data-input="date" data-validate="r">


                        <!-- ------------------------ -->
                        <div class="section-head">
                            <div>Discharge from our center info </div>
                        </div>
                        <div class="select-head">Status at discharge from our center</div>
                        <label class="container_radio">Refer to TNR member hospital <div class = "choose_sd" style = "color:red;">(ไม่ต้องทำ section 6)</div>
                            <input type="radio" name = "discharge_type"  value = "Refer to TNR member hospital">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "discharge_hospital_box" hidden>    
                            <div class="section-head-reg2">Hospital</div>
                            <select class="select-section" aria-placeholder="เลือกโรงพยาบาล"  id = "discharge_hospital" name = "discharge_hospital">
                                
                                <option value="1">เลือกโรงพยาบาล1</option>
                                <option value="2">เลือกโรงพยาบาล2</option>
                            </select>
                            <div class = "choose_sd" style = "color:red;" hidden></div>
                            <!-- <div class="section-head-reg2">Hospital code</div>
                            <input class="input-section  " type="text" placeholder="N/A Day(s)"id = "discharge_hospital_code" name = "discharge_hospital_code"> -->
                        </div>

                        <label class="container_radio">Discharge home <div class = "choose_sd" style = "color:red;">(ต้องทำ section 6)</div>
                            <input type="radio" name = "discharge_type" value = "Discharge home">
                            <span class="checkmark_radio"></span>
                        </label>

                        <label class="container_radio">Refer to level 2 hospital /
                                non TNR member hospital <div class = "choose_sd" style = "color:red;">(ต้องทำ section 6)</div>
                            <input type="radio" name = "discharge_type" value = "Refer to level 2 hospital / non TNR member hospital">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop5" hidden>
                            <div class="section-head-reg2">Hospital</div>
                            <input class="input-section  margin-btm" type="text" placeholder="ชื่อโรงพยาบาลที่ย้ายไป"  id= "discharge_level2_hospital" name = "discharge_level2_hospital">
                            <!-- <div class="section-head-reg2">Date transfer to non-mem hospital
                                or level 2</div>
                            <input class="input-section  " type="date" data-input="date"  placeholder="วว/ดด/ปปปป" id = "discharge_non_mem_hospital" name = "discharge_non_mem_hospital">
                            <div class="section-head-reg">At age</div>
                            <input class="input-section " type="text" placeholder="N/A Day(s)" id = "discharge_at_age" name = "discharge_at_age">
                            <div class="section-head-reg">Hospital days</div>
                            <input class="input-section " type="text" placeholder="N/A Day(s)" id = "discharge_hospital_day" name = "discharge_hospital_day"> -->
                        </div>

                        <div class="select-head margin-top">Hospital stay in our center</div>
                        <div class="choose_sd">คำนวณอัตโนมัติ</div>
                        <input class="input-section " type="text" placeholder="- Day(s)"
                            value="" name = "stay_day" disabled >

                        <!-- btn -->
                        <div class="save-btn" onclick = "save()"> 
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src = "js/section1.js"></script>
    <script src = "js/moment.js"></script>

</body>

</html>