<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/training.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>
        <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body>

        <section class="bg-section">
                <div class="bg-1section">
                        <div class="content-in">
                                <div class="logo-box">
                                        <div class="img-box">
                                                <img src="img/logo_main.png">
                                        </div>
                                        <div class="txt-box">
                                                <div>ThaiNy Training System</div>
                                        </div>
                                </div>
                                <div class="logo-box">
                                        <div class="txt-box2">
                                                <div onclick=" change_page('training_login.html', 'next');">เข้าสู่ระบบ</div>
                                                <div class="txt-choose">ลงทะเบียน</div>
                                        </div>
                                </div>
                        </div>
                </div>
                <div class="bg-2section">
                        <div class="content-in">
                                <div class="head-txt ">โรงพยาบาล</div>
                                <div class="select-box">
                                        <select class="select-option" id = "hospital_name" name = "hospital_name">
                                                <option value="" selected disabled >เลือกโรงพยาบาล</option>
                                                <option value="เลือกโรงพยาบาล1">เลือกโรงพยาบาล1</option>
                                                <option value="เลือกโรงพยาบาล2">เลือกโรงพยาบาล2</option>
                                        </select>
                                </div>
                               
                                <div class="width100per clearfix margin-top14">
                                                <div class="head-txt ">ตำแหน่ง</div>
                                        <div class="section2-span4 clearfix">
                                                <label class="container_radio margin-right30">Doctor
                                                        <input type="radio" name="position"  value="doctor">
                                                        <span class="checkmark_radio"></span>
                                                </label>
                                                <label class="container_radio">Collector
                                                        <input type="radio" name="position"   value="collector">
                                                        <span class="checkmark_radio"></span>
                                                </label>
                                        </div>
                                </div>
                                <div class="width100per clearfix margin-top14">
                                        <div class="head-txt ">ชื่อ-นามสกุล</div>
                                        <div class="width50 margin-right30">
                                                <input class="input-section" type="text" id = "name" placeholder="กรุณากรอกชื่อของท่าน">
                                        </div>
                                        <div class="width50">
                                                <input class="input-section" type="text" id = "surname" placeholder="กรุณากรอกนามสกุลของท่าน">
                                        </div>
                                </div>
                                <div class="width100per clearfix margin-top14">
                                        <div class="width50 margin-right30">
                                                <div class="head-txt ">วัน/เดือน/ปีเกิด(พ.ศ.)</div>
                                                <input class="input-section" id = "dob" type="text" placeholder="เลือกวัน" onfocus="setting_input('date',this);" data-input="date">

                                        </div>
                                        <div class="width50">
                                                <div class="head-txt ">เบอร์โทรศัพท์ที่ติดต่อได้</div>
                                                <input class="input-section" type="tel" id = "tel" placeholder="กรุณากรอกเบอร์โทรศัพท์ที่ติดต่อได้">
                                        </div>
                                </div>
                                <div class="head-txt margin-top14">อีเมล</div>
                                <div class="select-box">
                                        <input class="input-section-100" type="email" id = "email" placeholder=" กรุณากรอกอีเมลของท่าน">

                                </div>
                                <div class="width100per clearfix margin-top14">
                                        <div class="width50 margin-right30">
                                                <div class="head-txt ">ชื่อผู้ใช้ (Username)</div>
                                                <input class="input-section" type="text" id = "user" placeholder="กรุณากรอกชื่อผู้ใช้">

                                        </div>

                                </div>
                                <div class="width100per clearfix margin-top14">
                                        <div class="width50 margin-right30">
                                                <div class="head-txt ">รหัสผ่าน (Password)</div>
                                                <input class="input-section" type="password" id = "pass" placeholder="กรุณากรอกรหัสผ่าน" >
                                                <div class="head-txt-red ">ต้องมีตัวอักษรภาษาอังกฤษ พิมพ์เล็ก พิมพ์ใหญ่และตัวเลข อย่างน้อย 1 ตัว 
                                                                ความยาว  6-12 ตัวอักษร </div>
                                        </div>
                                        <div class="width50">
                                                <div class="head-txt ">ยืนยันรหัสผ่าน (Password)</div>
                                                <input class="input-section" type="password" id = "c_pass" placeholder="กรุณากรอกรหัสผ่านอีกครั้ง">
                                        </div>
                                </div>
                                <div class="width100per clearfix margin-top14">
                                      <div class="btn-box">
                                              <div class="cancel-btn"><span class="set_center">ยกเลิก</span></div>
                                              <div class="regis-btn"  onclick="save_register();"><span class="set_center">ลงทะเบียน</span></div>
                                      </div>
                                </div>

                               
                        </div>
                </div>


                <div class="bg-3section"></div>
        </section>



        <script src="js/main.js"></script>
        <script src="js/training_register.js"></script>
</body>

</html>