<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patient_display.html" onclick="transition_page_back('patient_display.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Section 5: Hospital course </div>
                        </div>

                        <div style="width:calc(100% + 16px);">
                            <div class="list-home" name = "select_item" onclick= "goto_page(1)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Admission temperature (ᵒc)<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home" name = "select_item" onclick= "goto_page(2)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Respiratory support<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(3)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>O2 supplement <img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(4)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Nitric oxide (iNO)<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(5)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Surfactant use<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(6)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>TPN / PN use<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(7)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Date of start feeding <img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(8)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Feeding at 120 ml/kg/day<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(9)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Diagnosis <img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(10)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Central line<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(11)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>Infection during hospitalization<img class="more-icon-img" src="img/next3.svg">
                            </div>
                            <div class="list-home " name = "select_item" onclick= "goto_page(12)">
                                <div class="circle_process"><img src="img/correct-section.svg"></div>HIE<img class="more-icon-img" src="img/next3.svg"></div>
                            </div>
                        </div>

                        <!-- btn -->
                        <!-- <div class="save-btn" onclick="save()">
                            <span class="set_center">บันทึก</span>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5.js"></script>
    <script>    
        window.onload = function(){
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/get_progress.php",
                data : {hospital:localStorage.hospital},
                success: function (data) {
                    var result = JSON.parse(data);
                    console.log(result);
                    for(var i = 0;i<12;i++){
                        if(result[0]["section5_"+(i+1)]=="done"){
                            document.getElementsByName("select_item")[i].children[0].className = "circle_process_fill";
                        }
                    }
                },
            });
            var temp = get_criteria();
            console.log(temp);
            
            var do1=false,do2=false;
            ////section5_5
            if(temp["Major anomalies"]=="1"){
             //   alert("test");
                document.getElementsByName("select_item")[4].style.cssText += "display:none;";
                do1 = true;
                // document.getElementsByName("select_item")[11].style.cssText += "display:none;";
                // do2 = true;
            }      
            if(temp["HIE"]=="1"){
                document.getElementsByName("select_item")[4].style.cssText += "display:none;";
                do1 = true;
                // document.getElementsByName("select_item")[11].style.cssText = "";
                // do2 = false;
                
            } 
            if(temp["GA < 32 weeks GA"]=="1"){
                document.getElementsByName("select_item")[4].style.cssText = "";
                do1 = false;
                // document.getElementsByName("select_item")[11].style.cssText += "display:none";
                // do2 = false;
            }
            if(temp["BW < 1,500g"]=="1"){
                document.getElementsByName("select_item")[4].style.cssText = "";
                do1 = false;
                // document.getElementsByName("select_item")[11].style.cssText += "display:none";
                // do2 = false;
            }

            ///////////   section5_11
             if(temp["Major anomalies"]=="1"){
             //   alert("test");
                // document.getElementsByName("select_item")[4].style.cssText += "display:none;";
                // do1 = true;
                document.getElementsByName("select_item")[11].style.cssText += "display:none;";
                do2 = true;
            }      
          
            if(temp["GA < 32 weeks GA"]=="1"){
                // document.getElementsByName("select_item")[4].style.cssText += "";
                // do1 = false;
                document.getElementsByName("select_item")[11].style.cssText += "display:none";
                do2 = true;
            }
            if(temp["BW < 1,500g"]=="1"){
                // document.getElementsByName("select_item")[4].style.cssText += "";
                // do1 = false;
                document.getElementsByName("select_item")[11].style.cssText += "display:none";
                do2 = true;
            }
            if(temp["HIE"]=="1"){
                // document.getElementsByName("select_item")[4].style.cssText += "display:none;";
                // do1 = true;
                document.getElementsByName("select_item")[11].style.cssText = "";
                do2 = false;
                
            } 
            if(do1){
                $.ajax({
                            type: "POST",
                            url: "https://techvernity.com/thainy/php/update_progress.php",
                            data:{num:"5_5",status:"done"},
                            success: function (data) {
                                //alert(status);
                            },
                          });
            }
            if(do2){
                $.ajax({
                            type: "POST",
                            url: "https://techvernity.com/thainy/php/update_progress.php",
                            data:{num:"5_12",status:"done"},
                            success: function (data) {
                                //alert(status);
                            },
                          });
            }
        }

    </script>
</body>

</html>