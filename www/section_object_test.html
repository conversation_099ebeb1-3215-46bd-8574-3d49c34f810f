<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="home.html" onclick="transition_page_back('home.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 3</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Section 3: Perinatal Data </div><img src="img/info.svg">
                        </div>
                        <div class="select-head ">Mode of delivery</div>
                        <label class="container_radio">Vaginal
                            <input type="radio"  name = "delivery_mode" value = "Vaginal">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop1" hidden>
                            <label class="container_radio">With instrument
                                <input type="radio"  name = "vaginal" value = "With instrument">
                                <span class="checkmark_radio"></span>
                            </label> <label class="container_radio">Without instrument 
                                <input type="radio"  name = "vaginal" value = "Without instrument ">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>
                        <label class="container_radio">Caesarean section
                            <input type="radio"  name = "delivery_mode" value = "Caesarean section">
                            <span class="checkmark_radio"></span>
                        </label>
                        <!-- ------------------------ -->
                        <div class="select-head margin-top">Apgar score</div>
                        <div class="choose_sd">ถ้าไม่ทราบใส่ NA</div>
                        <label class="container_radio">1 min
                            <input type="radio"  name = "apgar_score" value = "1 min">
                            <span class="checkmark_radio"></span>
                        </label>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name = "1_min_score">
                        <label class="container_radio">5 min
                            <input type="radio"   name = "apgar_score" value = "5 min">
                            <span class="checkmark_radio"></span>
                        </label>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name = "5_min_score">
                        <label class="container_radio">10 min
                            <input type="radio"  name = "apgar_score" value = "10 min">
                            <span class="checkmark_radio"></span>
                        </label>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name = "10_min_score">

                        <!-- ------------------------ -->
                        <div class="select-head margin-top12">Resuscitation</div>
                        <div class="choose_sd">ถ้าไม่มีข้อมูลใส่ NA</div>
                        <label class="container_radio">No
                            <input type="radio"  name = "resuscitation" value = "No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Yes
                            <input type="radio"  name = "resuscitation" value = "Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop2" hidden> 
                            <label class="container">CPAP
                                <input type="checkbox"  name = "CPAP" value = "CPAP">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">PPV
                                <input type="checkbox"  name = "PPV" value = "PPV">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Intubation
                                <input type="checkbox"  name = "intubation" value = "Intubation">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Chest compression
                                <input type="checkbox"  name = "chest_compression" value = "Chest compression">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Epinephrine
                                <input type="checkbox"  name = "epinephrine" value ="Epinephrine">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <label class="container_radio">N/A
                            <input type="radio"  name = "resuscitation" value = "N/A"> 
                            <span class="checkmark_radio"></span>
                        </label>


                        <!-- ------------------------ -->
                        <div class="select-head  margin-top20">Cord blood / 1st hour blood gas</div>
                        <div class="choose_sd">ถ้าไม่มีข้อมูลใส่ NA</div>

                        <div class="section-head-reg">pH</div>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name = "cord_blood_pH">

                        <div class="section-head-reg">pCO2</div>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name = "cord_blood_pCO2">

                        <div class="section-head-reg">HCO2</div>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name= "cord_blood_HCO2">

                        <div class="section-head-reg">BE</div>
                        <input class="input-section margin-btm" type="number" placeholder="กรุณากรอกคะแนนน" name = "cord_blood_BE">

                      
                        <!-- ------------------------ -->
                        <div class="section-head">
                            <div>Delayed cord clamping / Milking</div><img src="img/info.svg">
                        </div>
                        <label class="container_radio">No
                            <input type="radio"  name = "delayed_cord_clamping" value  = "No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Yes 
                            <input type="radio"  name = "delayed_cord_clamping" value = "Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">N/A
                            <input type="radio"  name = "delayed_cord_clamping" value = "N/A">
                            <span class="checkmark_radio"></span>
                        </label>




                        <!-- btn -->
                        <div class="save-btn" onclick = "save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
    
    <script src="js/section_object_test.js"></script>
</body>

</html>