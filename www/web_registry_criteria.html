<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/web.css">
        <link rel="stylesheet" type="text/css" href="css/web_patientlist.css">
        <link rel="stylesheet" type="text/css" href="css/popup.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>

        <script>
                window.onload = function(){
                        var onresize = function () {
                                heightt = document.getElementById("flex1").clientHeight;
                                heightt2 = document.getElementById("flex2").clientHeight;
                                heightt = heightt - 34 ;
                                heightt2 = heightt2 - 34 ;
                                console.log(heightt + "++++");
                                $("#line1").css("height", heightt)
                                $("#line2").css("height", heightt2)
                        }
                        onresize();
                        window.addEventListener("resize", onresize);
                }

        </script>
</head>

<body>
        <!-- header -->
        <section class="header_web">
                <img class="img_logo" src="">
        </section>


        <!-- popup -->

        <div class="popup-fake">
                <div class="content_box">
                        <div class="ipd-popup">
                                <div class="popup-box color-pink">
                                        <img src="img/faillogin.svg">
                                        <div class="popup-text1">ไม่ถูกต้อง</div>
                                        <div class="popup-text2 color-text-pink">ดูเหมือนว่าอีเมลที่คุณกรอก<br />
                                                ไม่ตรงกับที่อยู่ในระบบ
                                        </div>
                                        <div class="popup-btn">ลองอีกครั้ง</div>
                                </div>

                                <div class="popup-box2 color-green">
                                        <img src="img/complete.svg">
                                        <div class="popup-text1">สำเร็จ</div>
                                        <div class="popup-text2 color-text-green">เราได้ส่งลิงค์การตั้งค่ารหัสผ่านใหม่<br>
                                                ไปยัง “<span id="txt-email"></span>” <br>
                                                เรียบร้อยแล้ว
                                        </div>
                                        <div class="popup-btn">ลองอีกครั้ง</div>
                                </div>

                        </div>
                </div>
        </div>

        <!-- content -->

        <section class="content_login">
                <div class="menuhome-leftbar">
                        <div class="head-home">
                                <img src="img/profile.svg">
                                <div class="name-box">
                                        <div class="name-person" id="fullname">พญ. ฉัตร์ฉายบุษฑิตา เปรมพันธ์พงษ์</div>
                                        <div class="name-hospital" id="hospital">โรงพยาบาลรามาธิบดี</div>
                                </div>
                                <div class="rank-box">
                                        <span class="rank" id="status">หัวหน้า</span>
                                </div>

                        </div>
                        <div class="list-content">

                                <div id="patient_list" class="list-home web margin-i1tem select_list">
                                        <img class="img_list" src="img/list.svg">
                                        <div>รายการผู้ป่วย</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="track_tn" class="list-home web">
                                        <img class="img_list" src="img/search2.svg">
                                        <div>ค้นหาหมายเลข TN</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="export" class="list-home web">
                                        <img class="img_list" src="img/export.svg">
                                        <div>นำออกข้อมูล</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="help" class="list-home web">
                                        <img class="img_list" src="img/help.svg">
                                        <div>ช่วยเหลือและสนับสนุน</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="approve" class="list-home web">
                                        <img class="img_list" src="img/approve2.svg">
                                        <div>อนุมัติบัญชีผู้ใช้</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="setting" class="list-home web margin-top24">
                                        <img class="img_list" src="img/setting.svg">
                                        <div>ตั้งค่า</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div class="absolute">
                                        <div id="logout" class="list-home web margin-top24">
                                                <img class="img_list" src="img/logout.svg">
                                                <div>ออกจากระบบ</div>
                                        </div>
                                </div>

                        </div>

                </div>
                <div class="content-main">
                        <div class="back-box contentmain"><img src="img/backgreen.svg"> <span>กลับ</span></div>
                        <div class="web_head">Registry Criteria
                                <!-- <div class="search-box">
                                        <div class="content_box">
                                                <img src="img/search.svg">
                                                <input type="text" placeholder="ค้นหาเลข TN">
                                        </div>
                                </div> -->
                        </div>
                        <div class="content-in">
                                <div id="flex1" class="list-detail2  clearfix">
                                        <div class="status-detail-box">
                                                <!-- ***** read ***** -->
                                                <!-- for line change css 'line-uncomplete or line-complte' -->
                                                <!-- for status change css 'status-green or status-red' -->


                                                 <!-- เปลี่ยนสองอันนี้ ถ้าผ่านขั้นตอนเเรกเเล้วก็ใช้อีกอัน -->
                                                <div class="svisited section-active"><span class="set_center">1</span></div>
                                                <!-- <div class="status-detail status-green"><img src="img/correct-section.svg"></div> -->



                                                <div id="line1" class="line-status2 line-uncomplete"></div>
                                        </div>
                                        <div class="txt-detail2">
                                                <div class="section-head2">
                                                        <div>Admit Ward </div>
                                                </div>
                                                <div  class="content-flex">
                                                        <div class="section-head-reg">คนไข้ถูก admid เข้า ward ใด?</div>
                                                        <div class="ward-choose" onclick="select_criteria1('NICU')">NICU</div>
                                                        <div class="ward-choice" onclick="select_criteria1('sick')">Sick
                                                                Newborn</div>
                                                        <div class="ward-choice" onclick="select_criteria1('other')">อื่นๆ</div>

                                                </div>
                                        </div>
                                </div>
                                <div  id="flex2" class="list-detail clearfix">
                                        <div class="status-detail-box">
                                                <div class="line-status-top line-uncomplete"></div>
                                                <div class="svisited section-unactive"><span class="set_center">2</span></div>
                                                <!-- <div class="status-detail status-green"><img src="img/correct-section.svg"></div> -->
                                                <div id="line2" class="line-status2 line-uncomplete"></div>
                                        </div>
                                        <div class="txt-detail2">
                                                <div class="section-head2">
                                                        <div>PNA (Postnatal Age) </div>
                                                </div>
                                                <div class="content-flex">
                                                        <div class="section-head-reg">อายุนับจากหลังคลอดของทารก
                                                                (วัน)</div>
                                                        <input class="input-section-criteria" type="text" placeholder="อายุหลังคลอด (วัน)"
                                                                id="brith_time_day">
                                                </div>

                                        </div>
                                </div>
                                <div class="list-detail clearfix">
                                        <div class="status-detail-box">
                                                <div class="line-status-top line-uncomplete"></div>
                                                <div class="svisited section-unactive"><span class="set_center">3</span></div>
                                                <!-- <div class="status-detail status-green"><img src="img/correct-section.svg"></div> -->

                                        </div>
                                        <div class="txt-detail2">
                                                <div class="section-head2">
                                                        <div>One of the following </div>
                                                </div>
                                                <div class="content-flex">
                                                        <div class="section-head-reg">อาการของคนไข้</div>
                                                        <label class="container flex">GA < 32 weeks GA <input type="checkbox"
                                                                        name="checkbox1">
                                                                        <span class="checkmark"></span>
                                                        </label>
                                                        <label class="container flex">BW < 1,500g <input type="checkbox"
                                                                        name="checkbox1">
                                                                        <span class="checkmark"></span>
                                                        </label>
                                                        <label class="container flex">HIE
                                                                <input type="checkbox" name="checkbox1">
                                                                <span class="checkmark"></span>
                                                        </label>

                                                        <label class="container flex">Major anomalies
                                                                <input type="checkbox" name="checkbox1">
                                                                <span class="checkmark"></span>
                                                        </label>
                                                        <label class="container flex">อื่นๆ
                                                                <input type="checkbox" name="checkbox1">
                                                                <span class="checkmark"></span>
                                                        </label>

                                                </div>

                                        </div>
                                </div>
                                <div class="generate-btn-web">
                                        <span class="set_center">ตรวจสอบ</span>
                                </div>

                        </div>


        </section>



        <!-- <script src="js/main.js"></script> -->
        <script src="js/popup_animate.js"></script>
        <script src="js/web_forgetpassword.js"></script>

</body>

</html>