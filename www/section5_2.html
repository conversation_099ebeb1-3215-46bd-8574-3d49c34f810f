<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Respiratory support</div><img id="info1" src="img/info.svg">
                        </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="respiratory" id = "respiratory_yes" value="Yes" onclick ="response1('check')">
                            <span class="checkmark_radio"></span>
                        </label>
                        
                        <div class="inside-box " id = "yes_content" >
                        <div class="choose_sd padding-left" >เลือกได้มากกว่า 1 ข้อ</div>
                        <div>
                            <div class="container">
                                <label for="ven_checkbox">Ventilator support</label> (HFOV <div id="info2" class="box-image-icon2"><div class="content_box"><img src="img/info.svg"></div></div> / CMV <div id="info3" class="box-image-icon2"><div class="content_box"><img src="img/info.svg"></div></div> ) <div id = "ven_day">0 days</div>
                                <label for="ven_checkbox">
                                    <input type="checkbox" name="" value="Ventilator support (HFOV / CMV)" id = "ven_checkbox" onclick="ven_response()">
                                    <span class="checkmark"></span>
                                </label>    
                            </div>
                            <div id = "ven">
           
                     
                            <div id = "ven_contain">
                                    <div class="inside-in-with-pad" name = "ven_box" >
                                            <div>วันเริ่มต้น</div>
                                            <input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "ven_start" ontouchend="setting_input('date',this);" data-validate="l" onchange="diff_date('ven_start','ven_final','ven_day')">
                                            <div class="margin-top8">วันสิ้นสุด</div>
                                            <input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "ven_final" ontouchend="setting_input('date',this);" data-validate="r" onchange="diff_date('ven_start','ven_final','ven_day')">
                                            <div class="circle-red red-sec5" onclick="del_ven(this)"><img src="img/delete.svg"></div>
                                        </div>
                            </div>

                            <div class="plus-box" >
                                    <div class="plus" onclick="ven_add()">
                                        <div class="content_box">
                                                <img src="img/add.svg">
                                        </div>
                                    </div>
                                </div>
                            </div> 

                            <div class="container">
                                <label for="invasive_checkbox">Non-invasive</label> <div id="info4" class="box-image-icon2"><div class="content_box"><img src="img/info.svg"></div></div> (CPAP / BiPAP / NIPPV
                                    / HFNC <div id="info5" class="box-image-icon2"><div class="content_box"><img src="img/info.svg"></div></div> <div id = "invasive_day">0 days</div>
                                 <label for="invasive_checkbox">  
                                    <input type="checkbox" name="" value="Non-invasive (CPAP / BiPAP / NIPPV 
                                                    / HFNC " id = "invasive_checkbox"  onclick="invasive_response()">
                                    <span class="checkmark"></span>  
                                 </label>
                                 
                            </div>

                            <div id = "invasive">
                         
                                 <div id = "invasive_contain">
                                        <div class="inside-in-with-pad" name = "invasive_box" >
                                                <div>วันเริ่มต้น</div>
                                                <input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "invasive_start" ontouchend="setting_input('date',this);" data-validate="l" onchange="diff_date('invasive_start','invasive_final','invasive_day')">
                                                <div class="margin-top8">วันสิ้นสุด</div>
                                                <input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "invasive_final" ontouchend="setting_input('date',this);" data-validate="r" onchange="diff_date('invasive_start','invasive_final','invasive_day')">
                                                <div class="circle-red red-sec5" onclick="del_invasive(this)"><img src="img/delete.svg"></div>
                                            </div>
                                </div>

                                <div class="plus-box">
                                        <div class="plus"  onclick="invasive_add()">
                                            <div class="content_box">
                                                    <img src="img/add.svg">
                                            </div>
                                        </div>
                                    </div>
                            </div>
                            <div class="container">
                                <label for="lfnc_checkbox">LFNC</label> <div class="box-image-icon2"><div id="info6" class="content_box"><img src="img/info.svg"></div></div> <div id = "lfnc_day">0 days</div>
                                <label for="lfnc_checkbox"> 
                                    <input type="checkbox" name="" value="LFNC" id = "lfnc_checkbox" onclick="lfnc_response()">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div id = "lfnc">
                           

                                <div id = "lfnc_contain">
                                        <div class="inside-in-with-pad" name = "lfnc_box" > 
                                                <div>วันเริ่มต้น</div>
                                                <input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "lfnc_start" ontouchend="setting_input('date',this);" data-validate="l" onchange="diff_date('lfnc_start','lfnc_final','lfnc_day')">
                                                <div class="margin-top8">วันสิ้นสุด</div>
                                                <input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "lfnc_final" ontouchend="setting_input('date',this);" data-validate="r" onchange="diff_date('lfnc_start','lfnc_final','lfnc_day')">
                                                <div class="circle-red red-sec5" onclick="del_lfnc(this)"><img src="img/delete.svg"></div>
                                            </div>
                                </div>

                                <div class="plus-box" >
                                        <div class="plus" onclick="lfnc_add()">
                                            <div class="content_box">
                                                    <img src="img/add.svg">
                                            </div>
                                        </div>
                                </div>
                            


                            </div>
                        </div>
                    </div> 
                        <label class="container_radio">No
                            <input type="radio" name="respiratory" id = "respiratory_no"  value="No" onclick ="response1('uncheck')">
                            <span class="checkmark_radio"></span>
                        </label>



                        <!-- btn -->
                        <div class="save-btn" onclick="save_section5_2()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
   
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5.js"></script>
    <script>
    
    window.onload = function(){
    get_birth_date(function(){
        timeline_min_max("birth_date","now");
    });

    //info
    $("#info1").click(function(){ 
        show_info("การใช้เครื่องช่วยหายใจแบบต่าง ๆ ทั้ง invasive and non-invasive โดยจะใช้ O<sub>2</sub> หรือไม่ก็ได้<br/><span style='font-weight:600'>จะลงข้อมูลว่าใช้ respiratory support ชนิดนั้น ๆ เมื่อใช้นานติดต่อกัน > 6 ชั่วโมง</span>") 
    })
    $("#info2").click(function(){ 
        show_info("High frequency oscillatory ventilator") 
    })
    $("#info3").click(function(){ 
        show_info("Conventional mechanical ventilator") 
    })
    $("#info4").click(function(){ 
        show_info("กรณีที่กลับบ้านด้วย Non-invasive ventilator เช่น CPAP หรือ HFNC ให้ลงว่าใช้ non-invasive ถึงวัน discharge") 
    })
    $("#info5").click(function(){ 
        show_info("High flow nasal cannula คือ การให้ nasal cannula ที่ ≥ 2 LPM ") 
    })
    $("#info6").click(function(){ 
        show_info("- Low flow nasal cannula คือ การให้ nasal cannula ที่ < 2 LPM"
                + "<br/>- กรณีที่กลับบ้านด้วย Home oxygen with LFNC ให้ลงว่าใช้ LFNC ถึงวัน discharge") 
    })

    document.getElementById("yes_content").style.display  = "none";
    document.getElementById("ven_checkbox").disabled = true;
    document.getElementById("invasive_checkbox").disabled = true;
    document.getElementById("lfnc_checkbox").disabled = true;
    
    document.getElementById("ven_checkbox").checked = false;
    document.getElementById("invasive_checkbox").checked = false;
    document.getElementById("lfnc_checkbox").checked = false;

    set_contain_disable("ven");
    set_contain_disable("invasive");
    set_contain_disable("lfnc");


    //var val = initValue();
    $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section5_2.php",
    data:{hospital:localStorage.hospital},
    success: function (data) {
        console.log(data);
        var obj = JSON.parse(data);
        
        if(obj[0].respiratory_support == "Yes" || obj[0].respiratory_support == "yes"){
            setTimeout(function(){
                   $("#yes_content").slideDown(200);
            },200);
            $("#ven_checkbox, #invasive_checkbox, #lfnc_checkbox").prop("disabled", false)

            $.ajax({
                type: "POST",
                 url: "https://techvernity.com/thainy/php/get_section5_2_all.php",
                 data:{hospital:localStorage.hospital},
                success: function (data) {
                var value1 = JSON.parse(data);
               // console.log(value1.length);
                //console.log(value1["lfnc"]);
                //console.log(value1["non_invasive"]);
                //console.log(value1["ventilator"]);
                var lfnc = JSON.parse(value1["lfnc"]);
                var non_invasive = JSON.parse(value1["non_invasive"]);
                var ventilator = JSON.parse(value1["ventilator"]);
                document.getElementById("respiratory_yes").checked = true;
                console.log(lfnc);
                console.log(length_json(non_invasive));
                console.log(length_json(ventilator));
                init_table("lfnc",lfnc,length_json(lfnc),"");
                init_table("invasive",non_invasive,length_json(non_invasive),"");
                init_table("ven",ventilator,length_json(ventilator),"");
                update_day_5_2_all();
                setTimeout(function(){
                if(localStorage.hospital != ""){
                         //   alert("a");
                            $("input").prop("disabled",true);
                            $("textArea").prop("disabled",true);
                            $("select").prop("disabled",true);
                            $(".save-btn").prop("hidden",true);
                            $(".plus").prop("hidden",true);
                            $(".circle-red").prop("hidden",true);
                     }
                 },250);
        }//scccess
    });
          
        }else{
            document.getElementById("respiratory_no").checked = true;
            if(localStorage.hospital != ""){
                        //    alert("a");
                        $("input").prop("disabled",true);
                            $("textArea").prop("disabled",true);
                            $("select").prop("disabled",true);
                            $(".save-btn").prop("hidden",true);
                            $(".plus").prop("hidden",true);
                            $(".circle-red").prop("hidden",true);
                    }
        }
     
        }//scccess
    });
                            
    }
        </script>
</body>

</html>