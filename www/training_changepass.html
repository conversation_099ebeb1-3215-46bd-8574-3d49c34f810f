<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/training.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>
        <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

        <section class="bg-section">
                <div class="bg-1section-content">
                        <div class="content-in">
                                <div class="logo-box">
                                        <div class="img-box">
                                                <img src="img/logo_main.png">
                                        </div>
                                        <div class="txt-box">
                                                <div >ThaiNy Training System</div>
                                        </div>
                                </div>
                                <div class="logo-box">
                                        <!-- <div class="txt-box2" hidden>
                                                <div class="txt-choose">เข้าสู่ระบบ</div>
                                                <div >ลงทะเบียน</div>
                                        </div> -->
                                        <div class="navi-box">
                                                <div class="navi-img-box"><img src="img/backwhite.svg">กลับ</div>
                                                <div  class="yellow-txt">ตั้งรหัสผ่านใหม่</div>
                                        </div>
                                </div>
                        </div>
                </div>
                <div class="bg-2section-content">
                        <div class="content-in-pass">
                                <div class="head-txt-red ">ต้องมีตัวอักษรภาษาอังกฤษ พิมพ์เล็ก พิมพ์ใหญ่และตัวเลข อย่างน้อย 1 ตัว 
                                        ความยาว  6-12 ตัวอักษร </div>
                        <div class="head-txt margin-top14">รหัสผ่านเดิม(Old Password)</div>
                        <div class="select-box">
                                <input class="input-section-100" type="email" placeholder="กรุณากรอกรหัสผ่าน">

                        </div>
                        <div class="head-txt margin-top14">ตั้งรหัสผ่านใหม่ (New Password)</div>
                        <div class="select-box">
                                <input class="input-section-100" type="email" placeholder="กรุณากรอกรหัสผ่าน">

                        </div>
                        <div class="head-txt margin-top14">ยืนยันรหัสผ่านใหม่ (Comfirm Password)</div>
                        <div class="select-box">
                                <input class="input-section-100" type="email" placeholder="กรุณากรอกรหัสผ่าน">

                        </div>
                              
                        <div class="width100per clearfix margin-top14">
                                <div class="btn-box">
                                        <div class="cancel-btn"><span class="set_center">ยกเลิก</span></div>
                                        <div class="regis-btn"><span class="set_center">ยืนยัน</span></div>
                                </div>
                          </div>

                               
                        </div>
                </div>


                <div class="bg-3section"></div>
        </section>



    
    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
        <script src="js/main.js"></script>
       
</body>

</html>