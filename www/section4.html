<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patient_display.html" onclick="transition_page_back('patient_display.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 4</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Section 4: Neonatal data</div>
                        </div>

                        <div class="select-head margin-top">Date of birth</div>
                        <input class="input-section " type="text" placeholder="Date of birth" name = "i1" ontouchend="setting_input('date',this);" data-input="date" > 
                        <div class="select-head margin-top">Time of birth</div>
                        <input class="input-section " type="text" placeholder="Time of birth" name = "i2" ontouchend="setting_input('time',this);">

                        <!-- ------------------------ -->
                        <div hidden>
                            <div class="select-head margin-top">Multiplicity</div>
                            <label class="container_radio">Singleton
                                <input type="radio" name="i3" value = "Singleton">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">Twins
                                <input type="radio" name="i3" value = "Twins">
                                <span class="checkmark_radio"></span>
                            </label>
                            <label class="container_radio">Higher order
                                <input type="radio" name="i3" value = "Higher_order">
                                <span class="checkmark_radio"></span>
                            </label>
                            <input class="input-section" type="text" placeholder="Please specify" name = "Higher_order_etc">
                        </div>
                        <!-- ------------------------ -->
                        <div class="select-head margin-top">Gender</div>
                        <!-- <div class="choose_sd">เลือกเพียงคำตอบเดียว ถ้าไม่ทราบใส่ NA</div> -->
                        <label class="container_radio">Male
                            <input type="radio" name="i4" value = "Male">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Female
                            <input type="radio" name="i4" value = "Female">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Ambiguous 
                            <input type="radio" name="i4" value = "Ambiguous">
                            <span class="checkmark_radio"></span>
                        </label>


                        <!-- ------------------------ -->
                        <div class="select-head margin-top">Gestational age (weeks:days)<div id="info1" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                        <div class="width2">
                            <div>
                                <input class="select-section" type="number" placeholder="weeks" name = "i5">
                            </div>
                            <div class="margin-left24">
                                <input class="select-section" type="number" placeholder="days" name = "i6">
                            </div>
                        </div>

                        <div class="select-head margin-top">Birth weight (g)</div>
                        <input class="input-section " type="number" placeholder="Birth weight " name = "i7">
                        <div class="select-head margin-top">Length (cm)</div>
                        <input class="input-section margin-btm" type="number" placeholder="Length" name = "i8">
                        <label class="container">N/A
                            <input type="checkbox" name="len_na" value="N/A">
                            <span class="checkmark"></span>
                        </label>

                        <div class="select-head margin-top">Head circumference (cm)</div>
                        <input class="input-section margin-btm" type="number" placeholder="Head circumference" name="i9">
                        <label class="container">N/A
                            <input type="checkbox" name="cir_na" value="N/A">
                            <span class="checkmark"></span>
                        </label>




                        <div class="select-head margin-top">Growth status (Fenton 2013)<div id="info2" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                        <label class="container_radio">SGA (< 10<sup>th</sup> percentile) 
                            <input type="radio" name="i10" value = "SGA (< 10th percentile) ">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">AGA (10-90<sup>th</sup> percentile) 
                            <input type="radio" name="i10" value = "AGA (10-90th percentile) ">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">LGA (> 90<sup>th</sup> percentile) 
                            <input type="radio" name="i10" value = "LGA (> 90th percentile) ">
                            <span class="checkmark_radio"></span>
                        </label>

                        <!-- <div class="select-head margin-top"></div>
                        <div class="inside-box2">
                           <div class="graph-box"></div>
                        </div> -->




                        <!-- btn -->
                        <div class="save-btn" onclick = "save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section4.js"></script>
</body>

</html>