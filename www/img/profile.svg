<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 128 128">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
      }

      .cls-2 {
        clip-path: url(#clip-path);
      }

      .cls-3 {
        fill: #7fc5c6;
      }

      .cls-4 {
        fill: #f2f2f2;
      }

      .cls-5 {
        fill: #f8b64c;
      }

      .cls-6 {
        opacity: 0.1;
      }

      .cls-7 {
        fill: #40596b;
      }

      .cls-8 {
        fill: #ffd15c;
      }

      .cls-9 {
        fill: #acb3ba;
      }

      .cls-10 {
        fill: #cdd6e0;
      }

      .cls-11 {
        fill: #334a5e;
      }

      .cls-12 {
        fill: #2a4356;
      }

      .cls-13 {
        fill: #ff7058;
      }
    </style>
    <clipPath id="clip-path">
      <circle id="Ellipse_198" data-name="Ellipse 198" class="cls-1" cx="64" cy="64" r="64" transform="translate(2686 -164)"/>
    </clipPath>
  </defs>
  <g id="Mask_Group_600" data-name="Mask Group 600" class="cls-2" transform="translate(-2686 164)">
    <g id="Group_639" data-name="Group 639" transform="translate(2686 -164)">
      <rect id="Rectangle_309" data-name="Rectangle 309" class="cls-3" width="128" height="128"/>
      <g id="doctor" transform="translate(10.436 20.872)">
        <path id="Path_4509" data-name="Path 4509" class="cls-4" d="M107.128,364.6v16.628H0V364.6a10.665,10.665,0,0,1,6.582-9.949l22.588-9.166L46.8,365.475l5.533-11.63v-.023l1.23-2.6,1.252,2.6,5.535,11.653,17.607-19.993,22.611,9.166a10.705,10.705,0,0,1,6.559,9.949" transform="translate(0 -274.098)"/>
        <path id="Path_4510" data-name="Path 4510" class="cls-5" d="M212.966,294.891h.023v-9.465a50.981,50.981,0,0,1-4.247,5.555l-2.257,2.487a11.031,11.031,0,0,1-8.084,3.57h-3.455a11.019,11.019,0,0,1-8.084-3.57l-2.257-2.487a50.06,50.06,0,0,1-4.286-5.615v9.509l.037.018,14.21,8.015,2.1,1.175,2.1-1.175Z" transform="translate(-143.097 -225.977)"/>
        <g id="Group_616" data-name="Group 616" class="cls-6" transform="translate(47.346 59.529)">
          <g id="Group_615" data-name="Group 615">
            <path id="Path_4511" data-name="Path 4511" class="cls-7" d="M228.321,296.587l-.041-.014c-.023-.009-.048-.016-.071-.023s-.048-.014-.081-.025l8.685,6.451,13.639-7.692h.014l.009-9.417c-.023.035-3.238,4.247-4.247,5.5l-2.257,2.49a11.031,11.031,0,0,1-8.084,3.57h-3.455a9.824,9.824,0,0,1-2.052-.219,10.664,10.664,0,0,1-2.059-.622" transform="translate(-228.128 -285.866)"/>
          </g>
        </g>
        <g id="Group_617" data-name="Group 617" transform="translate(27.085 34.017)">
          <path id="Path_4512" data-name="Path 4512" class="cls-5" d="M143.68,170.7c.359,4.134-1.647,7.687-4.479,7.932s-5.428-2.9-5.794-7.038,1.647-7.683,4.484-7.932,5.431,2.9,5.79,7.038" transform="translate(-133.364 -163.648)"/>
          <path id="Path_4513" data-name="Path 4513" class="cls-5" d="M341.089,171.607c-.359,4.134-2.955,7.285-5.79,7.036s-4.846-3.8-4.479-7.932,2.955-7.287,5.79-7.038,4.843,3.8,4.479,7.934" transform="translate(-288.153 -163.657)"/>
        </g>
        <path id="Path_4514" data-name="Path 4514" class="cls-8" d="M192.865,63.893c-.435,4.629-2.628,14.829-11.421,24.824L179.188,91.2a11.026,11.026,0,0,1-8.084,3.572H167.65a11.013,11.013,0,0,1-8.084-3.572l-2.257-2.487c-8.8-10-10.962-20.2-11.425-24.824a89.268,89.268,0,0,1,0-16.582,23.815,23.815,0,0,1,46.982,0,89.3,89.3,0,0,1,0,16.582" transform="translate(-115.8 -21.704)"/>
        <path id="Path_4515" data-name="Path 4515" class="cls-7" d="M135.489,12.5s.151-15.506,21.3-10.553a36.71,36.71,0,0,1,17.249,13.91s6.336,9.806,2.891,18.056c-1.119,2.678-2.321,5.744-2.182,8.206,0,0-1.173-5.226-1.874-9.581a21,21,0,0,0-4.322-9.871l-.067-.085a14.281,14.281,0,0,0-14.818-5.145A25.95,25.95,0,0,0,144.9,22s-8.812-6.3-13.169,4.73c0,0-1.941,8.065-1.666,14.435,0,0-12.129-27.068,5.42-28.666" transform="translate(-98.785 -0.97)"/>
        <path id="Path_4516" data-name="Path 4516" class="cls-9" d="M342.148,333.874c-.046-.391-.136-3.982-.873-5.433a8.258,8.258,0,0,0-6.172-4.491l-7.416-1.5v1.52h-.023l-1.062.6,8.132,1.658a5.9,5.9,0,0,1,4.468,3.245,5.816,5.816,0,0,1,.624,3.549l-.048.6c-9.742,8.452-12.505,16.075-12.621,16.42-.732,2.188-.689,3.915.117,5.159a3.557,3.557,0,0,0,2.554,1.589l.23-2.3a1.33,1.33,0,0,1-.873-.576c-.369-.6-.325-1.7.161-3.111a34.236,34.236,0,0,1,5.527-8.869,4.936,4.936,0,0,1,6.518,1.932,35.179,35.179,0,0,1-.576,10.6c-.44,1.428-1.039,2.372-1.7,2.625a1.123,1.123,0,0,1-1.016-.046l-.576,1.011-.6,1a3.5,3.5,0,0,0,1.75.461,3.163,3.163,0,0,0,1.246-.253c1.382-.527,2.443-1.911,3.13-4.122.094-.345,2.234-8.385-.9-21.259m-3.059,5.965a6.729,6.729,0,0,0-2.123-.274c.995-1.085,2.1-2.188,3.342-3.314.345,1.61.6,3.153.783,4.606a6.835,6.835,0,0,0-2-1.018" transform="translate(-258.669 -256.04)"/>
        <g id="Group_618" data-name="Group 618" transform="translate(70.854 97.989)">
          <path id="Path_4517" data-name="Path 4517" class="cls-5" d="M342.019,470.718a1.714,1.714,0,0,1-2.011,1.347c-.921-.182-.265-3.537.661-3.355a1.712,1.712,0,0,1,1.35,2.008" transform="translate(-339.565 -468.703)"/>
          <path id="Path_4518" data-name="Path 4518" class="cls-5" d="M367.66,479.743a1.71,1.71,0,0,0,.84,2.266c.859.394,2.285-2.713,1.426-3.109a1.711,1.711,0,0,0-2.266.843" transform="translate(-361.472 -476.577)"/>
        </g>
        <path id="Path_4519" data-name="Path 4519" class="cls-9" d="M110.414,323.963l-.037-.018v-1.5l-4.979,1.025a17.869,17.869,0,0,0-12.761,24.691l2.11-.926a15.565,15.565,0,0,1,11.112-21.506l5.626-1.158Z" transform="translate(-72.192 -256.033)"/>
        <g id="Group_619" data-name="Group 619" transform="translate(28.392 67.907)">
          <path id="Path_4520" data-name="Path 4520" class="cls-10" d="M281.172,332.854l-16.994,22.293L256,338.588l2.1-1.175,14.21-8.015h.023l.023-.023Z" transform="translate(-230.828 -329.376)"/>
          <path id="Path_4521" data-name="Path 4521" class="cls-10" d="M139.413,332.854l17.022,22.293,8.15-16.559-2.1-1.175L148.28,329.4l-.046-.023Z" transform="translate(-139.413 -329.376)"/>
        </g>
        <path id="Path_4522" data-name="Path 4522" class="cls-11" d="M218.251,402.051l4.65-10.594-2.006-2.948-1.469-2.211,5.707-11.651,1.269-2.6,1.292,2.6v.023L233.4,386.3l-1.5,2.19-2.006,2.969,4.677,10.594Z" transform="translate(-172.838 -294.923)"/>
        <path id="Path_4523" data-name="Path 4523" class="cls-12" d="M237.67,386.3l-1.5,2.19-5.483-11.218-5.53,11.239-1.469-2.211,5.707-11.651,1.269-2.6,1.292,2.6v.023Z" transform="translate(-177.103 -294.923)"/>
        <path id="Path_4524" data-name="Path 4524" class="cls-13" d="M97.217,439.316A5.216,5.216,0,1,1,92,434.1a5.221,5.221,0,0,1,5.219,5.214" transform="translate(-68.686 -343.583)"/>
        <path id="Path_4525" data-name="Path 4525" class="cls-4" d="M101.718,445.53a3.5,3.5,0,1,1-3.5-3.5,3.5,3.5,0,0,1,3.5,3.5" transform="translate(-74.936 -349.797)"/>
        <path id="Path_4526" data-name="Path 4526" class="cls-9" d="M104.484,449.344a2.455,2.455,0,1,1-2.45-2.453,2.452,2.452,0,0,1,2.45,2.453" transform="translate(-78.771 -353.611)"/>
      </g>
    </g>
  </g>
</svg>
