<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/home.css">
        <link rel="stylesheet" type="text/css" href="css/zoom.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>
        <script type="text/javascript" src="js/jquery.transit.min.js"></script>
        <script src="js/zoom-master/jquery.zoom.min.js"></script>
        <script src="js/zoom-master/jquery.zoom.js"></script>
       

</head>

<body style="background-color: #fff">
    <div id="popup_background" class="popup-login" hidden="" style="display: block;"></div>
    <div id="popup_container" class="popup-fake" hidden="" style="display: block;">
        <div class="content_box">
            <div class="ipd-popup">
                    <button  style="position:absolute;" onclick="plusDivs(-1)">&#10094;</button>
                    <button  style="left: 199px;position:absolute;"onclick="plusDivs(1)">&#10095;</button>

                <div id="popup_box" class="popup-box3 color-white" style="opacity: 1; transform: scale(1, 1);">
                     
                <div class="content_box_flow ">
                    <div class="zoom mySlides" id = 'ex1'>
                        <img class="" src="img/fenton_boy.jpg" style="height:80%;width:100%;left: 0;top: 0;" data-action="zoom" class="cropped">
                    </div>
                    <div class="zoom mySlides" id = 'ex2'>
                            <img src="img/fenton_girl.jpg" style="height:80%;width:100%;left: 0;top: 0;" data-action="zoom"class="cropped">          
                    </div>
                   
                      
                </div>
                  
                    <div id="popup_button_html">
                        
                        <div id="popup_button_1" class="popup-btn">
                            <span id="popup_button_text_1">เรียบร้อย</span>
                        </div>
                    </div>
                 
                </div>
            </div>
        </div>
    </div>
</body>


<script>
    var slideIndex = 1;
showDivs(slideIndex);

function plusDivs(n) {
  showDivs(slideIndex += n);
}

function showDivs(n) {
  var i;
  var x = document.getElementsByClassName("mySlides");
  if (n > x.length) {slideIndex = 1}
  if (n < 1) {slideIndex = x.length}
  for (i = 0; i < x.length; i++) {
    x[i].style.display = "none";  
  }
  x[slideIndex-1].style.display = "block";  
}
$(document).ready(function(){
   
    $('#ex1').zoom({ on:'grab' });
    $('#ex2').zoom({  on:'grab'});
    
});
    </script>
</html>