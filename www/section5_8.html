<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Feeding at 120 ml/kg/day</div>
                        </div>
                        <label class="container_radio">Never reach
                            <input type="radio" name="reach_120_ml" value="No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">1<sup>st</sup> day of 120 ml/kg/day of feeding at
                            <input type="radio" name="reach_120_ml" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <input class="input-section margin-btm" name="start_reach_120_ml" type="text" placeholder="Date of reaching 120 ml/kg/day" ontouchend="setting_input('date',this);" data-input="date" disabled>

                        <input class="input-section " type="text" placeholder="Age N/A day(s)" name="age" disabled>
                        <!-- btn -->
                        <div class="save-btn" onclick="save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5_8.js"></script>
</body>

</html>