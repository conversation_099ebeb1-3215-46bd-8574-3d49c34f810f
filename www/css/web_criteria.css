
.content-main.criteria4{
    background-color:#7FC5C6;
}
.criteria4 .back-box{
    color: white;
}
.content-main.criteria5{
    background-color:#DEA19E;
}
.criteria5 .back-box{
    color: white;
}
.refer3{
    padding-top: 40px;
    box-sizing: border-box;
}
.refer3 .content-in{
    height: calc(100% - 43px);
}
.content-ipz{
    width: 360px;
    height: 100%;
    position: relative;
    margin: auto;
}
.fitcontent{
    width: fit-content;
    margin: auto;
    height: 178px;
    top: calc(50% - (178px / 2));
    position: relative;
}
.content-ipz-fit{
    width: fit-content;
    height: 100%;
    position: relative;
    margin: auto;
}
.content-ipz-cri{
    max-width: 686px;
    width: 100%;
    padding: 0px 156px;
    box-sizing: border-box;
    height: 100%;
    position: relative;
    margin: auto;
}
.section-head{
    width: calc(100% );
    height: 58px;;
    padding-top: 30px;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    border-bottom: 1px solid #FFFFFF;
    display: flex;
    margin-bottom: 9px;
    color: white;
}
.section-head img{
    width: 18px;;
    margin-left: 6px;
}
.cri-img {
    width: 99px;
    height: 107px;
    position: relative;
    left: calc(50% - (99px / 2));
    margin-bottom: 6px;
}
.cri-text1{
    width: 100%;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    color: white;
    margin-bottom: 6px;
}
.cri-text2{
    width: 100%;
    text-align: center;
    font-size: 15px;
}

.color-pink{
    background-color: #DEA19E;
    color: #DEA19E;
}
.color-green{
    background-color: #7FC5C6;
    color: #7FC5C6;
}

.color-text-pink{
    color: #805C5B;
}
.color-text-green{
    color: #497273;
}

.fillter-each{
    height: 23px;
    position: relative;
    display: flex;
    color: white;
    font-size: 17px;

}
.fillter-each div{
    width: 22px;
    height: 22px;
    border: 1px solid white;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    position: relative;
    margin-top: -1px;
}
.fillter-each div img{
   width: 12.3px;
   height: 9.3px;
   position: absolute;
   left: 5px;
   top: 7px;
}
.fillter-each  > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 9px);
 }
 
 .btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}
.next-ntn{
    background-color:#FFFFFF;
    text-align:center;
    font-size:17px;
    font-weight:bold;
}
.color-btn-pink{
    color: #DEA19E;
}
.color-btn-green{
    color: #7FC5C6;
}
.next-ntn img{
    width: 5px;
    height: 10px;
}
.right-btn img{
    width: 5px;
    height: 10px;
}
.back-ntn{
    border: 1px solid white;
    color: white;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    margin-bottom: 14px;
}
.left-btn{
    border: 1px solid white;
    color: white;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    margin-bottom: 14px;
    position:relative;
    box-sizing:border-box;
    width: calc(50% - 10px);
   float: left;
    height:44px;
    margin-right: 20px;
    border-radius:22px;
}
.right-btn{
    background-color:#FFFFFF;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    position:relative;
    box-sizing:border-box;
    width: calc(50% - 10px);
    color: #7FC5C6;
   float: left;
    height:44px;
    border-radius:22px;
}
.btn-footer-special{
    width: calc(100% - 32px);
    position: absolute;
    bottom:  calc(16px );
}
.btn-footer{
    width: 328px;
    position: absolute;
    bottom:  calc(16px);
    left: calc(50% - ((328px) / 2));
}
.btn-footer-2{
    width: 328px;
    position: absolute;
    bottom:  calc(16px);
    left: calc(50% - ((328px) / 2));
}
.tn-box{
    width: calc(100% );
    height: 84px;
    position: relative;
    color: white;
    box-sizing: border-box;
    font-size: 17px;
}
.btn-footer-criteria{
    max-width: 686px;
    width: 100%;
    position: absolute;
    bottom: 0px;
    left: 0;
}

@media (max-height: 760px) {
    .btn-footer {
        width: 100%;
      position: relative;
      margin: 32px 0;
      bottom: unset;
    }
  
}