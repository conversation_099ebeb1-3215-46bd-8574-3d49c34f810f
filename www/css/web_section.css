input{
    margin-bottom: unset !important;
}
.container{
    margin-bottom: unset !important;
}
.container_radio{
    margin-bottom: unset !important;
}
.section2-content{
    width: 100%;
    position: relative;
    padding: 10px;
    box-sizing: border-box;
}

.section-head-section{
    width: calc(100% );
   min-height: 26px;;
    padding-top: 22px;
    height: fit-content;
    font-size: 17px;
    font-weight: bold;
    /* box-sizing: border-box; */
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    align-items: center;
}.section-head-section img{
    width: 18px;;
    height: 18px;;
    margin-left: 6px;
}
.display-content2{
    width: calc(100% - 20px);
    position: relative;
    padding: 10px;
}
.display-content{
    width: 100%;
    position: relative;
}
.margin-right150{
    margin-right: 150px;
}

.display-content-div{
    display: inline-block;
    padding: 10px;
}
.display-content-label{
    display: inline-block;
  
}
.section-span-per4{
    width:calc((100% / 4) - 12px) ;
    margin-right: 12px;
    float: left;
    /* height: 50px; */
    /* margin-bottom: 10px; */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 8px;
}
.section2-span{
    width:calc((100% / 3) - 12px) ;
    margin-right: 12px;
    float: left;
    /* height: 50px; */
    /* margin-bottom: 10px; */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 8px;
}

.section2-span2{
    width:calc((100% / 3) - 12px) ;
    margin-right: 12px;
    float: left;
    /* height: 50px; */
    box-sizing: border-box;
    /* margin-bottom: 10px; */
    padding: 8px;
}
.section2-span-per40{
    width:calc(55%) ;
    margin-right: 12px;
    float: left;
    /* height: 50px; */
    /* margin-bottom: 10px; */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 8px;
}
.section2-span3{
    width:calc((100% / 3) - 12px) ;
    margin-right: 12px;
    float: left;
    min-height: 44px;
    /* margin-bottom: 10px; */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 8px;
    padding-top: 19px;
}
.section2-span4{
    width:calc(100% );
    float: left;
    /* height: 50px; */
    /* margin-bottom: 10px; */
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 8px;
}
.section2-span5{
    width:calc((100% / 3) - 28px) ;
    margin-right: 28px;
    float: left;
    box-sizing: border-box;
    /* margin-bottom: 10px; */
    /* padding: 8px; */
}

.inside-span{
    width: 100%;
    box-sizing: border-box;
    padding: 8px 8px 8px 22px;
    position: relative;
   
}
.select-span{
    width:calc((100% / 3) - 12px) ;
    background-color: #F2F2F2;
}
.select-span2{
    width:calc((100% / 3) - 12px) ;
    background-color: #E6E6E6;
    margin-bottom: 4px;
}
.display-content-span{
   width:calc(100% / 3);
   float: left;
   padding: 0px 10px;;
   box-sizing: border-box;
   display: flex;
   height: 50px;
   align-items: center;
}
span label{
    /* margin-top: 4px; */
}
.show-content{
    width: 100%;
    padding: 12px;
    box-sizing: border-box;
    position: relative;
    background-color: #F2F2F2;
}
.show-choose{
    width: fit-content;
    background-color: #F2F2F2;
}
.select-section{
    width: 100%;
    max-width: 315px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding-left: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
    margin-bottom: 12px;
}
.select-section::placeholder{
    color: #B2B2B2;
}
.input-section {
    width: 100%;
    max-width: 315px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}

.input-section::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}
.input-section2 {
    width: 100%;
    max-width: 280px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}

.input-section2::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}
.input-section3 {
    width: calc(100% - 100px);
    /* width: 100%; */
    /* max-width: 280px; */
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}

.input-section3::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}
.width50{
    width: calc(50% - 30px);
    float: left;
    position: relative;
}
.width50-in{
    width: calc(50% - 15px);
    float: left;
    position: relative;
}
.select-head::placeholder{
    color: #B2B2B2;
   }
   .select-head{
       width: calc(100%);
       font-size: 17px;
       font-weight: bold;
       box-sizing: border-box;
       /* margin-top: 8px; */
   }
.width100{
    max-width: unset;
}
.width100per{
    width: 100%;
    height: fit-content;
    position: relative;
}
.width180{
    max-width: 180px;
    width: 100%;
}
.margin-right60{
    margin-right: 60px;
}
.margin-right30{
    margin-right: 30px;
}
.margin-right90{
    margin-right: 90px;
}
.margin-btm8{
    margin-bottom: 8px !important;
}
.choose_sd{
    font-size: 13px;
    color: #B2B2B2;
    margin-bottom: 6px;
}
.margin-right8{
    margin-right: 8px;
}
.padding-top19{
    padding-top: 19px;
}
.section-head-noline{
    width: calc(100%);
    height: fit-content;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}
.section-head-noline img{
    width: 18px;;
    margin-left: 6px;
 
}

.margin-top30{
    margin-top: 30px;
}
.margin-top4{
    margin-top: 4px;
}
.margin-top-14{
    /* margin-top: -14px; */
}
.margin-top6{
    margin-top: 6px;
}
.margin-top8{
    margin-top: 8px;
}
.margin-top10{
    margin-top: 10px;
}
.padding-left{
    padding-left: 28px;
}

.inside-box-graph{
   width: 100%;
   max-width: 375px;
    background-color: #F2F2F2;
    padding: 8px 16px 20px 16px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 9px;
}.graph-box{
    width: 100%;
    height: 205px;
    box-sizing: border-box;
    position: relative;
    background-color: #FFFFFF;
    font-size: 17px;
    margin-top: 8px;
}

.font-bold{
    font-weight: bold;
    width: 110px;
}
.inside-gray1{
    width: 100%;
    padding: 8px 44px;
    position: relative;
    box-sizing: border-box;
    background-color: #F2F2F2;
}
.inside-gray2{
    width: calc(100% + 88px);
    padding: 8px 44px;
    position: relative;
    box-sizing: border-box;
    background-color: #E6E6E6;
    margin-left: -44px;
    margin-top: 8px;
    margin-bottom: 8px;
}
.inside-gray2-in{
    width: calc(100% + 88px);
    padding: 8px 72px;
    position: relative;
    box-sizing: border-box;
    background-color: #E6E6E6;
    margin-left: -44px;
    margin-top: 8px;
    margin-bottom: 8px;
}
.inside-gray3-in{
    width: calc(100% + 144px);
    padding: 8px 72px;
    position: relative;
    box-sizing: border-box;
    background-color: #D9D9D9;
    margin-left: calc(-72px );
    margin-top: 8px;
    margin-bottom: 8px;
}

.inside-gray3{
    width: calc(100% + 144px);
    padding: 8px 100px;
    position: relative;
    box-sizing: border-box;
    background-color: #D9D9D9;
    margin-left: calc(-72px );
    margin-top: 8px;
    margin-bottom: 8px;
}
.img_info{
    width: 18px;
    height: 18px;
    vertical-align: middle;
}
.section-head-reg{
    font-size: 17px;
    /* margin-top: 8px; */
    
}

.list-home.patient{
    height: 28px;
     padding: 12px 0px 12px 16px;
     position: relative;
     cursor: pointer;
 }
 .list-home.patient > img{
     float: left;
     height: 28px;
     margin-right: 10px;
 }
 
 .list-home.patient > div {
     float: left;
     height: 100%;
     width: calc(100% );
     padding-bottom: 12px;
     border-bottom: 1px solid #eee;
     font-size: 17px;
     position: relative;
 }
 
 .list-home.patient > div > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 14px);
 }

 .export-request-btn-web{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 314px;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    position: absolute;
    bottom: 0;
    left: calc(50% - (314px / 2));
    margin-top: 40px;
    margin-bottom: 20px;
    cursor: pointer;
}
.flex-box{
    display: flex;
    position: relative;
    box-sizing: border-box;
    width: 100%;
    align-items: center;
}
.width140{
    width: 140px;
}
.plus-box{
    width: 100%;
    height: 42px;
    position: relative;
    margin: 8px 0px;
}
.plus{
    background-color: #82C6C7;
    height: 42px;
    width: 42px;
    border-radius: 50%;
    position: absolute;
    right: 0;
}
.plus img{
    width: 18px;
    height: 18px;
    position: absolute;
    left: 12px;
    top: 12px;
}
.gray-txt{
    color: #B2B2B2;
    margin-right: 8px;
}
.black-txt{
    color: black;
    margin-right: 8px;
}
.black-txt2{
   color: black;
   margin-left: 8px;
}
.input-small{
    width: 80px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
    text-align: center;
}
.input-small::placeholder{
    color: #B2B2B2 !important;
}