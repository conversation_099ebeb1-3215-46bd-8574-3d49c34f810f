.header_web{
    width: 100%;
    height: 47px;
    background: #7FC4C5;
    border-bottom: 5px solid #F2F2F2;
}
.content_login
{
    position: relative;
    box-sizing: border-box;
    height: calc(100vh - 52px);
    width: 100%;
}
.login_data{
    width: 312px;
    position: absolute;
    top: calc(50% - (377.44px / 2));
    left:  calc(50% - (312px / 2));
}
.term_data{
    width: 50%;
    height: 100%;
    overflow-x: auto;
    margin: 0px auto;
}
.term_data2{
    width: 370px;
    margin: 0px auto;
    position: relative;
    height: 100%;
}
.head-content{
    width: 100%;
    text-align: center;
    position: relative;
    padding-top: 76px;
    color: #7FC5C6;
    font-size: 34px;
    margin-bottom: 38px;
}
.logo-content{
    width: fit-content;
    position: relative;
    box-sizing: border-box;
    margin: auto;
}
.logo-box{
    width: 96px;
    height: 96px;
    box-sizing: border-box;
    position: relative;
    background-color: #7FC5C6;
    border-radius: 96px;
}
.name-app{
    width: fit-content;
    font-size: 32px;
    font-weight: bold;
    color: #7FC5C6;
}
.name-detail{
    width: fit-content;
    font-size: 16px;
    color: #7FC5C6;
}.error-box{
    width: 100%;
    height: 64px;
    position: relative;
    box-sizing: border-box;
}
.error-text{
    width: 100%;
    color: #FF3B30;
    margin-bottom: 8px;
    position: absolute;
    bottom: 0px;
}
.error-text2{
    width: 100%;
    color: #4A4A4A;
    margin-bottom: 8px;
    position: absolute;
    bottom: 0px;
}

.error-text img{
    width: 14px;
    height: 14px;
    vertical-align: middle;
}
.btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
    font-size: 17px;
    cursor: pointer;
}
.btn-content::placeholder{
    color: #C7C7CC;
}
input{
    border: none;
    background-color:#FFFFFF;
    padding-left:12px;
    font-size:17px;
    color:#000000;
    margin-bottom:14px;
}
.current-type{
    border: 1px solid #C7C7CC;
}
.error-type{
    border: 1px solid #FF3B30;
}
.login-btn{
    background-color:#7FC5C6;
    color: #fff;
    text-align:center;
    font-size:17px;
    line-height:44px;
    font-weight:bold;
    margin-bottom: 12%;
}
.forget-btn{
    color: #7FC5C6;
    text-align:center;
    font-size:17px;
    line-height:44px;
    font-weight:bold;
    justify-content: center;
    border:1px solid #7FC5C6;
    cursor: pointer;
}



.btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}
.next-ntn{
    width: 332px;
    margin: 0px auto;
    background-color:#7FC5C6;
    color: #fff;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    /* margin-bottom: 12%; */
    margin-top: 12%;
}
.next-ntn img{
    width: 5px;
    height: 10px;
}


.bottom-box{
    width: 100%;
    padding-top: 12px;
    position: absolute;
    box-sizing: border-box;
    bottom: 70px;
}
.bottom-box2{
    width: 100%;
    height: 120px;
    padding-top: 12px;
    position: relative;
    box-sizing: border-box;
    bottom: 0px;
}
.back-box{
    left: 18%;
    top: 10px;
    position: absolute;
    color: #7FC5C6;
    font-size: 17px;
}
.back-box img{
    width: 12px;
    height: 20px;
    vertical-align: middle;
    cursor: pointer;
}
.back-box span{
   
    cursor: pointer;
}
.back-box.contentmain{
    left: 8px;
    height: 40px;
    position: relative;
}
.web-main-head{
    min-height: 90px;
}

.web_head{
    width: calc(100% - (43px + 43px));
    height: 50px;;
    font-size: 34px;
    margin-left: 43px;
    padding-bottom: 9px;
    font-weight: 600;
    box-sizing: border-box;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    position: relative;
}
.web_head.sup3{
    height: unset;
    border-bottom: 1px dashed #C8C7CC;
}
.web_head_reg{
    width: calc(100% - (43px + 43px));
    /* height: 50px;; */
    font-size: 20px;
    margin-left: 43px;
    /* padding-bottom: 9px; */
    box-sizing: border-box;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    position: relative;
}


.web_head_reqlist{
    width: calc(100% - (43px + 43px));
    /* padding-bottom: 9px;  */
    margin-left: 43px;
    box-sizing: border-box;
    position: relative;
}
.web_head_reqlist > div:first-child{
    width: 100%;
    height: 60px;
    font-size: 34px;
    padding-bottom: 9px;
    font-weight: 600;
    box-sizing: border-box;
    display: flex;
    position: relative;
}
.web-head-flex{
    display: flex;
    width: 100%;
    border-bottom: 1px solid #C8C7CC;
}
.web-head-flex > div{
    font-size: 15px;
    margin-right: 28px;   
    cursor: pointer;

}
.web-choose{
    font-weight: bold;
    color: #7FC5C6;
    border-bottom: 3px solid #7FC5C6;
    padding-bottom: 2px;
}
.web_2_head{
    width: calc(100% );
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    padding: 8.5px 0px 8.5px 15px;
    background-color: #F8F8F8;
}
.box2_content{
    width: 100%;
    height: fit-content;
   
}
.box2_content .box1{
    width: calc(50% - 15px);
    float: left;
    margin-right: 30px;
    padding-left: 15px;
    box-sizing: border-box;
}
.box2_content .box2{
    width: calc(50% - 15px);
    float: left;
    padding-left: 15px;
    box-sizing: border-box;
}
.color-red{
    color: #FF3B30;
}
/* homebar */
.menuhome-leftbar{
    width: 240px;
    height: calc(100vh - 47px);
    margin-top: -5px;
    float: left;
    position: relative;
}
.content-main{
    width: calc(100% - 240px);
    height: calc(100% );
    float: left;
    position: relative;
    overflow: hidden;
   
}

.head-home{
    position: relative;
    height: 196px;
    background-image: url('../img/bghome.svg') ;
    background-repeat: no-repeat;
    background-size: cover;
}
.head-home img{
    width: 96px;
    display: block;
    position: absolute;
    top: calc(25px);
    left: calc(50% - (96px / 2));
    
}
.name-box{
    position: absolute;
    top: calc(25px + 96px + 12px);
    width: 100%;
    
}
.name-person{
    text-align: center;
    font-size: 17px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
    padding: 0px 12px;
    box-sizing: border-box;
}
.name-hospital{
    font-size: 15px;
    font-weight: 300;
    text-align: center;
    margin-top: -4px;
}
.rank-box{
    width: 100%;
    height: 28px;
    position: absolute;
    bottom: calc(-14px);
    text-align: center;
}
.rank{
    height: 28px;
    box-sizing: border-box;
    font-size: 13px;
    border-radius: 16px;
    color: white;
    background-color: #7FC5C6;
    padding: 4px 26px;
    position: relative;
    z-index: 1;
 
}
.list-content{
    height: calc(100% -  216px);
    position: relative;
}

.list-home.web{
   height: 28px;
    padding: 12px 0px 12px 12px;
    position: relative;
    cursor: pointer;
}
.img_list{
    float: left;
    height: 28px;
    margin-right: 10px;
}

.list-home.web > div {
    float: left;
    height: 100%;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    font-size: 17px;
    position: relative;
}
.list-home.indivi{
    height: 28px;
     padding: 12px 12px 12px 12px;
     position: relative;
     cursor: pointer;
 }
 .list-home.indivi > div > img{
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 14px);
 }
 
 .list-home.indivi > div {
     float: left;
     height: 100%;
     width: calc(100%);
     padding-bottom: 12px;
     border-bottom: 1px solid #eee;
     font-size: 17px;
     position: relative;
 }

.img_next3{
   height: 16px; 
   position: absolute;
   right: 12px;
   top: calc(50% - ( 16px / 2));
}
.margin-top24{
  margin-top: 24px;
}
.margin-top40{
    margin-top: 24px;
  }

.pagging-2item{
    padding-top: 24px;
}
.margin-i1tem{
    margin-top: 20px;
}
.absolute{
    position: absolute;
    bottom: 0;
}

.content-in{
    margin: 43px;
    margin-top: 0px;
    width: calc(100% - (43px + 43px));
    height: calc(100% - (43px + 43px));
   overflow-x: auto;
}
.save-btn-web{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 314px;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    margin: 0px auto;
    position: relative;
    margin-top: 40px;
    margin-bottom: 20px;
    cursor: pointer;
   
}
.select_list{
    background-color: #F2F2F2;
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: #C7C7CC;
    font-size:17px;
  }
  ::-moz-placeholder { /* Firefox 19+ */
    color: #C7C7CC; 
    font-size:17px;
  }
  :-ms-input-placeholder { /* IE 10+ */
    color: #C7C7CC; 
    font-size:17px;
  }
  :-moz-placeholder { /* Firefox 18- */
    color: #C7C7CC; 
    font-size:17px;
  }

    /* iPhone5 */
@media (max-width: 320px){
    .head-home img{
        width: calc(100vw * 128/375);
        top: calc(50% - ((100vw * 128/375) / 2));
        left: calc(50% - ((100vw * 128/375) / 2));
    }
    .name-box{
        top: calc(50% + ((100vw * 128/375) / 2) + 7px);
        width: 100%;
    }
    .rank-box{
        height: 31px;
        bottom: calc(-16px);
    }
}













/* checkbox */
/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 12px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: #FFFFFF;
    border-radius: 4px;
    border:2px solid #7FC5C6;
}
/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: #7FC5C6;
}


/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 5px;
    top: 2px;
    width: 3px;
    height: 8px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}


/* radiobuttun */
/* The container */
.container_radio {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 8px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container_radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark_radio {
    position: absolute;
    top: 1px;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: white;
    border-radius: 50%;
    border: 2px solid #7FC5C6;
}



/* When the radio button is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}


/* When the checkbox is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark_radio:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container_radio input:checked ~ .checkmark_radio:after {
    display: block;
}

/* Style the checkmark/indicator */
.container_radio .checkmark_radio:after {
    top: 3px;
	left: 3px;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #7FC5C6;
}
