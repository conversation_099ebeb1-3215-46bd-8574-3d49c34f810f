

/* popup */
.popup-login{
    width: 100%;
    height: 100%;
    position: fixed;
    box-sizing: border-box;
    background-color: rgba(0 , 0, 0, 0.25);
    z-index: 100;
    padding: 13%;
}
.popup-fake{
    width: 100%;
    height: 100%;
    position: fixed;
    box-sizing: border-box;
    z-index: 100;
    padding: 13%;
}

.popup-box{
    width: 270px;
    height: 294px;
    border-radius: 14px;
    padding: 24px;
    position: absolute;
    top: calc(50% - (294px / 2));
    left: calc(50% - (270px / 2));
    box-sizing: border-box;
}
.popup-box2{
    width: 270px;
    height: 314px;
    border-radius: 14px;
    padding: 24px;
    position: absolute;
    left: calc(50% - (270px / 2));
    top: calc(50% - (314px / 2));
    box-sizing: border-box;
}
.popup-box3{
    width: 100%;
    height: 400px;
    border-radius: 14px;
    position: absolute;
    left: calc(50% - (100% / 2));
    top: calc(50% - (400px / 2));
    box-sizing: border-box;
}

.popup-box-info{
    width: 270px;
    /* height: 360px; */
    border-radius: 14px;
    padding: 24px;
    padding-bottom: 62px;
    position: absolute;
    left: calc(50% - (270px / 2));
    /* top: calc(50% - (360px / 2)); */
    vertical-align: middle;
    box-sizing: border-box;
}

.popup-box-info  .content_box_flow{
    font-size: 16px !important;
}

.color-pink{
    background-color: #DEA19E;
    color: #DEA19E;
}
.color-green{
    background-color: #7FC5C6;
    color: #7FC5C6;
}
.color-brown{
    background-color: #BFA791;
    color: #A18D7A;
}
.color-white{
    background-color: #FFFFFF;
    color: #7FC5C6;
}
.color-white .content_box_flow {
    color: #333333;
    font-size: 17px;
    height: calc(100% - 20px) !important;
}
.color-text-pink{
    color: #805C5B;
}
.color-text-green{
    color: #497273;
}
.color-text-brown{
    color: #4D4D4D;
}


.popup-box img{
    width: 99px;
    height: 107px;
    position: relative;
    left: calc(50% - (99px / 2));
    margin-bottom: 18px;
}
.popup-box2 img{
    width: 99px;
    height: 107px;
    position: relative;
    left: calc(50% - (99px / 2));
    margin-bottom: 18px;
}
.waitapprove {
    width: 168px !important;
    height: 122px !important;
    left: calc(50% - (168px / 2)) !important;
    margin-bottom: 8px !important;
}
.popup-text1{
    width: 100%;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    color: white;
    margin-bottom: 6px;
}
.popup-text2{
    width: 100%;
    text-align: center;
    font-size: 15px;
}
.popup-btn{
    width: 100%;
    height: 44px;
    position: absolute;
    bottom: 0px;
    left: 0px;;
    background-color: white;
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
    text-align: center;
    line-height: 44px;
    font-size: 17px;
    font-weight:bold;
    border-top: 1px solid #F2F2F2;
}
.popup-btn-box{
    width: 100%;
    height: 44px;
    position: absolute;
    bottom: 0px;
    left: 0px;;
    background-color: white;
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
    text-align: center;
    line-height: 44px;
    font-size: 17px;
    font-weight:bold;
    padding: 10px;
    box-sizing: border-box;
}
.cencel-btn{
    width: 50% ;
    height: 100%;
    float: left;
    font-size: 17px;
    box-sizing: border-box;
}
.approve-btn{
    width: 50%;
    height: 100%;
    float: left;
    font-size: 17px;
    box-sizing: border-box;
    font-weight: bold;
    border-left: 1px solid #C7C7CC;
}
/* iPad */
@media (min-width: 768px) {
    .ipd-popup{
            position: relative;
            width: calc(414px - (26%));
            box-sizing: border-box;
            height: 100%;
            margin: auto;
    }
}

/* iPad Pro */
@media (min-width: 1024px) {
    .ipd-popup{
            position: relative;
            width: calc(414px - (24%));
            box-sizing: border-box;
            height: 100%;
            margin: auto;
    }
   
}
