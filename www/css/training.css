body{
    /* min-height: 2000px; */
}
.bg-section{
    width: 100vw;
    min-width: 960px;
    height: 100vh;
    position: relative;
    box-sizing: border-box;
    background-color: #F0EFEB;
}
.bg-1section{
    width: 100vw;
    min-width: 960px;
    height: 196px;
    position: relative;
    background-color: #50A0A0;
    box-sizing: border-box;
    z-index: 1;
}
.bg-1section-main{
    width: 100vw;
    min-width: 960px;
    min-height:384px;
    position: relative;
    background-color: #50A0A0;
    box-sizing: border-box;
    z-index: 1;
}
.bg-1section-main .content-in{
    width: 746px;
    min-width: 960px;
    height: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
  
}
.bg-1section-content{
    width: 100vw;
    min-width: 960px;
    height: 128px;
    position: relative;
    background-color: #50A0A0;
    box-sizing: border-box;
    z-index: 1;
}
.bg-1section-content .content-in{
    width: 746px;
    min-width: 960px;
    height: 100%;
    padding-top: 26px;
    padding-bottom: 40px;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
  
}
.bg-2section{
    width: 100vw;
    min-width: 960px;
    height: calc(100vh - 196px - 48px);
    min-height: 500px;
    position: relative;
    background-color: #F0EFEB;
    padding-bottom: 40px;
}
.bg-2section-content{
    width: 100vw;
    min-width: 960px;
    height: calc(100vh - 128px - 48px);
    min-height: 500px;
    position: relative;
    background-color: #F0EFEB;
    padding-top: 40px;
    padding-bottom: 40px;
    box-sizing: border-box;
    /* overflow: hidden; */
}
.bg-2section-normol{
    width: 100vw;
    min-width: 960px;
    height: calc(100vh - 196px - 48px);
    min-height: 520px;
    position: relative;
    background-color: #F0EFEB;
    padding-top: 40px;
    padding-bottom: 40px;
    box-sizing: border-box;
}
.bg-3section{
    width: 100vw;
    min-width: 960px;
    height: 48px;
    position: relative;
    background-color: #50A0A0;
    box-sizing: border-box;
}
.bg-1section .content-in{
    width: 746px;
    min-width: 960px;
    height: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
  
}
.bg-2section .content-in{
    width: 746px;
    min-width: 740px;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    margin-top: -40px;
    background-color: #FFFFFF;
    border-radius: 12px;
    z-index: 2;
    overflow-y: auto;
    padding: 30px 68px;
    box-shadow: 0px 12px 24px rgba(0 , 0, 0, 0.16);
}
.bg-2section .content-in-login{
    width: 428px;
    min-width: 420px;
    height: fit-content;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    margin-top: -40px;
    background-color: #FFFFFF;
    border-radius: 12px;
    z-index: 2;
    overflow-y: auto;
    padding: 30px 68px;
    box-shadow: 0px 12px 24px rgba(0 , 0, 0, 0.16);
}
.bg-2section-content  .content-in-editprofile{
    width: 746px;
    min-width: 740px;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    background-color: #FFFFFF;
    border-radius: 12px;
    z-index: 2;
    overflow-y: auto;
    padding: 30px 68px;
    box-shadow: 0px 12px 24px rgba(0 , 0, 0, 0.16);
}
.bg-2section-content .content-in-pass{
    width: 428px;
    min-width: 420px;
    height: fit-content;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    background-color: #FFFFFF;
    border-radius: 12px;
    z-index: 2;
    overflow-y: auto;
    padding: 30px 68px;
    box-shadow: 0px 12px 24px rgba(0 , 0, 0, 0.16);
}
.bg-2section-normol .content-in-max{
    width: 746px;
    min-width: 960px;
    height: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    padding-left: 50px;
}
.bg-3section .content-in-max{
    width: 746px;
    min-width: 960px;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    padding-left: 50px;
    display: flex;
    align-items: center;
}
.bg-2section-main{
    width: 100vw;
    min-width: 960px;
    height: calc(100vh - 384px - 48px);
    min-height: 310px;
    position: relative;
    background-color: #F0EFEB;
    padding-top: 40px;
    padding-bottom: 40px;
    box-sizing: border-box;
}
.bg-2section-main .content-in-main{
    width: 824px;
    min-width:824px;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    padding-left: 50px;
    display: flex;
    align-items: center;
}
.bg-2section-normol .content-in-main{
    width: 746px;
    min-width: 860px;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    display: flex;
    align-items: center;
}
.bg-2section-content .content-in-main{
    width: 746px;
    min-width: 860px;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    margin: 0 auto;
    display: flex;
    align-items: center;
}
.visit-page{
    font-size: 17px;
    cursor: pointer;
    color: #FFD580;
}
.unvitsit-page{
    color: #FFFFFF;
    font-size: 17px;
    padding-right: 12px;
    border-right: 1px solid white;
    margin-right: 12px;
    cursor: pointer;
}
.visit-page2{
    font-size: 16px;
    cursor: pointer;
    color: #FFD580;
}
.unvitsit-page2{
    color: #FFFFFF;
    font-size: 16px;
    padding-right: 12px;
    border-right: 1px solid white;
    margin-right: 12px;
    cursor: pointer;
}
.profile-box{
    width: 100%;
    position: relative;
    display: flex;
    box-sizing: border-box;
}
.profile-box-left{
    width: 164px;
    position: relative;
    box-sizing: border-box;
    padding-right: 54px;
    color: #50A0A0;
    font-size: 15px;
    text-align: center;
    
}
.profile-box-right{
    width: calc(100% - 164px);
}
.img-camera{
    width: 38px;
    height: 30px;
    position: relative;
    margin: auto;
    top: calc(50% - 15px);
}
.edit-txt{
    margin-top: 8px;
}
.profile-img{
    width: 110px;
    height: 110px;
    background-color: #7FC5C6;
    position: relative;
    box-sizing: border-box;
    border-radius: 8px;
   
    
}
.name-logo{
    position: absolute;
    top: 26px;
    right: 0;
    display: flex;
    flex-direction: row-reverse;
}
.logo-box{
    width: fit-content;
    position: relative;
    display: flex;
    align-items: center;
}
.logo-box-back{
    width: fit-content;
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 12px;
}
.img-box{
    width: 38px;
    height: 100%;
    position: relative;
    margin-right: 12px;
    box-sizing: border-box;
}
.img-box-show{
    width: fit-content;
    height: 38px;
    position: relative;
    box-sizing: border-box;
    display: flex;
    text-align: center;
}
.img-box img{
    width: 100%;
}
.img-showmore{
    width: 4.5px;
    
}
.txt15{
    font-size: 15px;
    text-align: right;
    margin-right: 12px;
}
.txt12{
    font-size: 12px;
    text-align: right;
    margin-right: 12px;
}
.txt-box{
    width: fit-content;
    height: 100%;
    position: relative;
    font-size: 22px;
    font-weight: 600;
    color: #FFFFFF;
}
.txt-box2{
    padding-left: 50px;
    font-size: 17px;
    font-weight: 600;
    color: white;
    display: flex;
    position: relative;
    cursor: pointer;
}
.txt-box2 div:first-child{
   margin-right: 12px;
    position: relative;
}
.navi-box{
    padding-left: 50px;
    font-size: 17px;
    font-weight: 600;
    color: white;
    display: flex;
    position: relative;
    cursor: pointer;
    align-items: center;
}
.navi-img-box{
    display: flex;
    position: relative;
    cursor: pointer;
    align-items: center;
}
.navi-box div:first-child{
    margin-right: 12px;
     position: relative;
 }
 .navi-box img{
     width: 12px;
     margin-right: 6.5px;
 }
 .yellow-txt{
     font-size: 22px;
     font-weight: 600;
     color: #FFD580;
 }
 .yellow-txt-34{
    font-size: 34px;
    font-weight: 600;
    color: #FFD580;
    margin-top: 8px;
 }
.txt-choose{
    color: #FFD580;
    padding-bottom: 2px;
    border-radius: 2px;
    border-bottom:4px solid #FFD580;
}
.head-txt{
    font-size: 15px;
    font-weight: 600;
    color: #404040;
}
.select-box{
    width: 100%;
    margin-top: 4px;
    position: relative;
    box-sizing: border-box;
}
.select-option{
    width: 100%;
    height: 44px;;
    background-color: #F0EFEB;
    border: none;
    padding: 12px;
   
}
.section2-span4{
    width:calc(100% );
    float: left;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}
.width100per{
    width: 100%;
    height: fit-content;
    position: relative;
}
.width50{
    width: calc(50% - 15px);
    float: left;
    position: relative;
}
.width40{
    width: calc(40% - 15px);
    float: left;
    position: relative;
}
.width60{
    width: calc(60% - 15px);
    float: left;
    position: relative;
}
.input-section {
    width: 100%;
    max-width: 315px;
    font-size: 17px;
    background-color: #F0EFEB;
    border: none;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
}

select::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;
}
.input-section::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}

.input-section-100 {
    width: 100%;
    font-size: 17px;
    background-color: #F0EFEB;
    border: none;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
}


.input-section-100::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}
.margin-right30{
    margin-right: 30px;
}
.margin-left30{
    margin-left: 30px;
}
.margin-top14{
    margin-top: 14px;
}
.margin-top8{
    margin-top: 8px;
}

.head-txt-red{
    color: #F04545;
    font-size: 10px;
}

.txt-green{
    color: #50A0A0;
}

.btn-box{
    width: fit-content;
    position: relative;
    margin: 0 auto;
    box-sizing: border-box;
    height: 44px;;
    display: flex;
    margin-top: 30px;
}
.cancel-btn{
    width: 144px;
    height: 44px;
    position: relative;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: #A6CFCF;
    color: white;
    font-size: 17px;
    font-weight: 600;
    margin-right: 20px;
    box-shadow: 0px 3px 6px rgba(0 , 0, 0, 0.16);
    cursor: pointer;
}
.regis-btn{
    width: 144px;
    height: 44px;
    position: relative;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: #50A0A0;
    color: white;
    font-size: 17px;
    font-weight: 600;
    box-shadow: 0px 3px 6px rgba(0 , 0, 0, 0.16);
    cursor: pointer;
}
.btn-box-login{
    width: 100%;
    position: relative;
    margin: 0 auto;
    box-sizing: border-box;
    height: 44px;;
    display: flex;
}
.cancel-btn-login{
    width: calc(100% - 10px);
    height: 44px;
    position: relative;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: #A6CFCF;
    color: white;
    font-size: 17px;
    font-weight: 600;
    margin-right: 20px;
    box-shadow: 0px 3px 6px rgba(0 , 0, 0, 0.16);
    cursor: pointer;
}
.regis-btn-login{
    width: calc(100% - 10px);
    height: 44px;
    position: relative;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: #50A0A0;
    color: white;
    font-size: 17px;
    font-weight: 600;
    box-shadow: 0px 3px 6px rgba(0 , 0, 0, 0.16);
    cursor: pointer;
}
.txt-forget{
    width: 100%;
    text-align: right;
    position: relative;
    margin-top: 10px;
    font-size: 15px;
}
.color-head-txt{
    color: #50A0A0;
    font-size: 15px;
    font-weight: 600;
}

.color-txt{
    color: #404040;
    font-size: 17px;
    margin-bottom: 12px;
}
.color-txt-change{
    color: #989795;
    font-size: 17px;
    margin-bottom: 12px;
}


.main-content-box{
    width: 100%;
    height: fit-content;
    position: relative;
    box-sizing: border-box;
    margin-top: calc(-40%);
    z-index: 1;
  
}
.main-content{
    width: calc(100% / 3 - 18px);
    height: fit-content;
    float: left;
    position: relative;
    box-sizing: border-box;
   
}
.margin-right27{
    margin-right: 27px;
}
.img-main-box{
    width: 100%;
    height: 256px;
    position: relative;
    box-sizing: border-box;
    background-color: #7FC5C6;
    border-radius: 8px;
    position: relative;
    box-sizing: border-box;
}

.main-txt{
    width: 100%;
    text-align: center;
    margin-top: 24px;
    position: relative;
    box-sizing: border-box;
    color: #50A0A0;
    font-size: 22px;
    font-weight: 600;
}
.txt-left-box{
    width: 50%;
    height: 100%;
    float: left;
    box-sizing: border-box;
    position: relative;
    padding-bottom: 40px;
}
.box-list-right-content{
    width: 50%;
    height: 100%;
    float: left;
    padding-left: 20px;
    box-sizing: border-box;
    position: relative;
}

.page-txt{
    width: 100%;
    text-align: right;
    font-size: 15px;
    color: #50A0A0;
    height: 29px;;
}
.img-left-box{
    width: 45%;
    height: 100%;
    float: left;
    box-sizing: border-box;
    position: relative;
    padding-bottom: 40px;
}
.list-right-content{
    width: 55%;
    height: 100%;
    float: left;
    padding-left: 60px;
    box-sizing: border-box;
    position: relative;
}
.img-insige-box{
    width: 100%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    background-color: #7FC5C6;
    border-radius: 8px;
}
.img-insige-box-right{
    width: 100%;
    /* height: 50%; */
    padding-top: 100%;
    position: relative;
    box-sizing: border-box;
    background-color: #7FC5C6;
    border-radius: 8px;
    margin-top: 20px;
}
.list-line-content{
    position: relative;
    width: 100%;
    height: fit-content;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    margin-bottom: 14px;
    margin-top: 4px;
}
.list-circle{
    width: 18px;
    height: 18px;
    position: relative;
    box-sizing: border-box;
    border: 1px solid #50A0A0;
    border-radius: 50%;
}
.list-txt{
    color: #50A0A0;
    font-size: 17px;
    margin-left: 8px;
}

.txt-head{
    color: #50A0A0;
    font-size: 22px;
    font-weight: 600;
}

.txt-content{
    font-size: 15px;
    color: #404040;
    margin-top: 18px;
}

.nb-page{
    width: fit-content;
    height: fit-content;
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    right: 60px;
    top: 8px;
}

.ng-icon-show{
    width: 32px;
    cursor: pointer;
    height: 32px;
}
.ng-icon-hide{
    width: 32px;
    height: 32px;
    opacity: 0;
}
.nb-page img:first-child{
    margin-left: 12px;
}
.header-number{
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 4px;
}
.number-txt{
    width: 22px;
    height: 22px;
    border-radius: 50%;
    position: relative;
    color: white;
    background-color: #50A0A0;
    font-size: 15px;
    margin-right: 4px;
   
}
.number-txt2{
    width: 22px;
    height: 22px;
    border-radius: 50%;
    position: relative;
    font-size: 15px;
    margin-right: 4px;
    font-weight: 600;
   
}
.txt-txt{
    color: #404040;
    font-size: 15px;
    font-weight: 600;
}
.txt-txt-normal{
    color: #404040;
    font-size: 15px;
}

.whatis{
    width: 130%;
    min-width: 100%;
}
.cri2{
    width: 130%;
    margin-left: -30%;
    margin-top: 30px;
}
.cri3{
    width: 130%;
    margin-left: -20%;
    margin-top: 30px;
}
.img-icon2{
    width: 20px;
}
.padding-box{
    width: 100%;
    position: relative;
    box-sizing: border-box;
    padding-left: 20px;
}
.send{
    width: 130%;
    min-width: 100%;
    margin-left: -18%;
    margin-top: 30px;
}

.send2{
    width: 130%;
    min-width: 100%;
    margin-left: -18%;
    margin-top: 10px;
}
.loginm{
    /* width: 100%; */
    height: 378px;
    margin-top: 30px;
    position: relative;
    left: 20%;
}

/* checkbox */
/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 12px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: #FFFFFF;
    border-radius: 4px;
    border:2px solid #7FC5C6;
}
/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: #7FC5C6;
}


/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 5px;
    top: 2px;
    width: 3px;
    height: 8px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}


/* radiobuttun */
/* The container */
.container_radio {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 8px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container_radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark_radio {
    position: absolute;
    top: 3px;
    left: 0;
    height: 14px;
    width: 14px;
    background-color: white;
    border-radius: 50%;
    border: 2px solid #7FC5C6;
}



/* When the radio button is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}


/* When the checkbox is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark_radio:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container_radio input:checked ~ .checkmark_radio:after {
    display: block;
}

/* Style the checkmark/indicator */
.container_radio .checkmark_radio:after {
    top: 2px;
	left: 2px;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #7FC5C6;
}
