.list-home.patient{
    height: 28px;
     padding: 12px 0px 12px 16px;
     position: relative;
     cursor: pointer;
 }
 .list-home.patient > img{
     float: left;
     height: 28px;
     margin-right: 10px;
 }
 
 .list-home.patient > div {
     float: left;
     height: 100%;
     width: calc(100% );
     padding-bottom: 12px;
     border-bottom: 1px solid #eee;
     font-size: 17px;
     position: relative;
 }
 
 .list-home.patient > div > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 14px);
 }
 .search-box-patientlist{
     width: 240px;
     height: 36px;
     position: relative;
     box-sizing: border-box;
 }
 .search-box-patientlist input{
    width: 100%;
    height: 100%;
    border: none;
    box-sizing: border-box;
    background-color: #F8F8F8;
    border-radius: 10px;
    padding-left: 30px;
    font-size: 17px;
    color: #8E8E93;
    position: absolute;
 }
 .search-box-patientlist img{
    position: absolute;
   height: 14px;
   width: 14px;
   top: 11px;
   left: 8px;
   z-index: 1;
 }
 .search-box{
    width: 345px;
    height: 36px;
    position: absolute;
    bottom: 2px;
    right: 0;
    top: 7px;
    /* padding-left: 16px; */
    /* padding-right: 16px; */
    margin-left: -16px;
    box-sizing: border-box;
}
.search-box input{
    width: 100%;
    height: 100%;
    border: none;
    box-sizing: border-box;
    background-color: #DFDFE0;
    border-radius: 10px;
    padding-left: 30px;
    font-size: 17px;
    color: #8E8E93;
    position: absolute;
}

.search-box img{
   position: absolute;
   height: 14px;
   width: 14px;
   top: 11px;
   left: 12px;
   z-index: 1;
}
 /* setting */

 .list-setting{
    padding: 12px 0px 0px 12px;
    position: relative;
    cursor: pointer;
}
.list-setting > img{
    float: left;
    height: 28px;
    margin-right: 10px;
}

.list-setting > div {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    font-size: 22px;
    position: relative;
}

.list-setting> div > img {
   height: 16px;
   position: absolute;
   right: 12px;
   top: calc(50% - 14px);
}
.list-setting> div > div {
    width: calc(100% - 20px) ;
    display: flex;
 }
 .sd-txt{
    font-size: 15px;
    color: #8E8E93;
}
 .content-txt{
     width: 100%;
     padding: 12px;
     box-sizing: border-box;
 }
 .each-list.req{
    width: 100%;
    padding: 17px 16px;
    position: relative;
    box-sizing: border-box;
    border-bottom: 0.5px solid #C8C7CC;
    cursor: pointer;
}.name-patient{
    color: #000000;
    font-size: 17px;
    height: 20px;
    overflow: hidden;
    width: calc(100% - 120px);
    position: relative;
    white-space: nowrap;
    overflow: hidden;
}
.req-btn{
    height: 26px;
    line-height: 26px;
    color: white;
    padding-left: 8px;
    padding-right: 8px;
    position: absolute;
    right: calc(16px + 20px);
    top: calc(50% - 13px);
    font-size: 15px;
    border-radius: 13px;
    text-align: center;
    cursor: pointer;
}
.wait-color{
    background-color:#FFCC00;
}
.approve-color{
    background-color:#4CD964;
}
.can-color{
    background-color: #FF3B30;
}
.each-list img{
     height: 14px;
     width: 14px;
     position: absolute;
     right: 16px;
     top: calc(50% - 7px);
     
}
.number-active{
    text-align:center;
    font-size: 15px;
    color: #8E8E93;
    margin: 12px 0px 24px 0px;
}


 /* export detail*/
 .list-detail{
    padding: 12px 0px 0px 0px;
    position: relative;
    width: calc(100% - 16px);
 }
 .list-detail2.color{
    padding: 12px 0px 0px 16px;
    position: relative;
    width: calc(100% - 0px);
    margin-left: -16px;
    background-color: #F8F8F8;
 }
 .list-detail2{
        padding: 12px 0px 0px 16px;
        position: relative;
        width: calc(100% - 16px);
        margin-left: -16px;
 }
 .status-detail{
    width: 20px;
    height: 20px;
    border: 1px solid #7FC5C6;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    position: relative;
    float: left;
  
 }
 .status-green{
    border: 1px solid #7FC5C6;
    background-color: #7FC5C6;
 }
 .status-red{
    border: 1px solid #FF3B30;
    background-color: #FF3B30;
 }
 .status-detail img{
    width: 12.3px;
    height: 9.3px;
    position: absolute;
    left: 4px;
    top: 6px;
 }
 .status-detail-box{
    width: 20px;
    height: 100%;
    margin-right: 8px;
    position: relative;
    float: left;
    margin-top: 1px;
 }
 .line-status-top{
    position: absolute;
    width: 0px;
    bottom: 22px;
    left: 11px;
    height: 13px;
 }
 .line-status{
    position: absolute;
    width: 0px;
    top: 22px;
    left: 11px;
    height: calc(100% + 22px);
    
 }
 .line-status2{
    position: absolute;
    width: 0px;
    top: 22px;
    left: 11px;
    height: calc(100% + 84px);
    
 }
 .line-dashed{
    border: 0.5px dashed #C7C7CC;
 }
 .line-complte{
    border: 0.5px solid  #7FC5C6;
   
 }
 .line-uncomplete{
    border: 0.5px solid  #C7C7CC;
 }
 .line-dash-complte{
    border: 0.5px dashed #7FC5C6;
 }
.txt-detail {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    font-size: 22px;
    position: relative;
}
.txt-detail2 {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    font-size: 22px;
    position: relative;
}
.left-detail{
    float: left;
    font-size: 17px;
    width: 60%;
    height: 100%;
}
.right-detail{
    float: right;
    width: 40%;
    height: 100%;
    text-align: right;
    font-size: 15px;
    color: #8E8E93;
}
.name-txt{
    font-size: 22px;
    font-weight: bold;
}
.name-txt2{
    font-size: 20px;

}

.name-txt3{
    font-size: 17px;
}

.cancel-request-btn-web{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 314px;
    background-color: #FF3B30;
    height: 44px;
    border-radius: 22px;
    position: absolute;
    bottom: 0;
    left: calc(50% - (314px / 2));
    margin-top: 40px;
    margin-bottom: 20px;
    cursor: pointer;
}
.export-request-btn-web{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 314px;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    position: absolute;
    bottom: 0;
    left: calc(50% - (314px / 2));
    margin-top: 40px;
    margin-bottom: 20px;
    cursor: pointer;
}

.select-head{
    width: calc(100%);
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    /* margin-top: 8px; */
}
.select-head-line{
    width: calc(100%);
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    padding: 4px 0px;
    border-bottom: 1px solid #C8C7CC;
}
.select-head-line2{
    width: calc(100%);
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    margin-top: 30px;
    margin-bottom: 14px;
    padding: 4px 0px;
    border-bottom: 1px solid #C8C7CC;
}

.margin-btm{
    margin-bottom: 20px;
}.txt-15{
    font-size: 15px;
    margin-bottom: 4px;
}
.number-noty{
    background-color: #FF3B30;
    height: 26px;
    min-width: 26px;
    padding-left: 8px;
    padding-right: 8px;
    line-height: 26px;
    border-radius: 13px;
    box-sizing: border-box;
    color: white;
    /* position: absolute; */
    right: calc(16px + 20px);
    /* top: calc(50% - 13px); */
    margin-top: 3.5px;
    text-align: center;
    font-size: 15px;
    margin-left: 14px;
}

.each-list{
    width: 100%;
    padding: 12px 16px;
    position: relative;
    box-sizing: border-box;
    border-bottom: 0.5px solid #C8C7CC;
    cursor: pointer;
}

.tn-patient{
    color: #8E8E93;
    font-size: 15px;
}
.search-line-box{
        width: 100%;
        display: flex;
        position: relative;
        height: fit-content;
        margin-bottom: 21px;
        margin-top: 21px;
        align-items: center;
}
.input-register{
    width: 343px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    margin-left: 20px;
    margin-bottom: unset;
}

.input-register::placeholder , .input-pass::placeholder{
    color: #B2B2B2;
}
.input-pass{
    width: 343px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    margin-bottom: unset;
}
.search-txt{
    font-size: 15px;
}
.search-btn{
    font-size: 17px;
    font-weight: bold;
    background-color: #7FC5C6;
    border-radius: 22px;
    color: white;
    height: 44px;;
    padding: 0px 64px;
    margin-left: 48px;
    cursor: pointer;

}
.resul-box{
    background-color: #F8F8F8;
    padding: 8px 0px;
}
.result-each-box{
    width: 100%;
    position: relative;
   
}
.txt-result1{
    width: 50%;;
    float: left;
    font-size: 22px;
    font-weight: bold;
    color:#000000;
    padding-left: 100px;
    box-sizing: border-box;
}
.txt-result2{
    width: 50%;;
    float: left;
    font-size: 17px;
    padding-left: 100px;
    box-sizing: border-box;
}
.txt-result3{
    width: 50%;
    float: left;
    box-sizing: border-box;
    padding-left: 100px;
    font-weight: 20px;
}
.change-txt{
    font-size: 17px;
    margin-top: 10px;
}
.txt-15 {
    font-size: 15px;
    margin-bottom: 20px;
}
.flex-box{
    width: 100%;
    position: relative;
    display: flex;
}
.flex-box > div{
    display: inline-block;
    width: 33.3%;
    position: relative;
}
.margin-top{
    margin-top: 30px;
}
.width2_1{
    max-width: 343px;
}
.width2_1 > div > div {
    width: calc(50% - 15px);
    height: 100%;
    float: left;
  
 }
 .set-height2{
    height: 24px;
}
 .set-height{
     height: 44px;;
     position: relative;
 }
 .width2_1 > div > img{
    width: 9.32px;
    height: 18.65px;
    position: absolute;
    top:  calc(50% -  (18.65px / 2));
    left: calc(50% -  (9.32px / 2));
  
 }
 .input-section {
   width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}
.margin-btm{
    margin-bottom: 12px;
}
.input-section ::placeholder{
    color: #B2B2B2;
} .margin-left30{
    margin-left: 30px;
}


/* patient display */
.pd-left{
    width: 50%;
    position: relative;
    height: 100%;
    background-color: #F8F8F8;
    padding: 16px;
    box-sizing: border-box;
    float: left;
}

.txt-size22{
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 15px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
}
.txt-size20{
    font-size: 20px;
    margin-bottom: 15px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
}
.txt-size20 > span:first-child{
    margin-right: 70px;
}
.txt-size202{
    font-size: 20px;
}
.txt-size202 > span:first-child{
    margin-right: 20%;
}
.txt-size17{
    font-size: 17px;
    margin-bottom: 18px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
}
.txt-line{
    width: 100%;
    border: 0.5px solid #DDDDDD;
}
.pd-right{
    width: 50%;
    position: relative;
    height: 100%;
    padding: 0px 16px 16px 16px;
    box-sizing: border-box;
    float: left;
}
.choose_hospital{
    width: calc(100%);
    padding-top: 30px;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
}
.choose_hospital2{
    width: calc(100%);
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
}

.fillter-each{
    height: 23px;
    padding: 15px 0px;
    position: relative;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    cursor: pointer;

}
.fillter-each div{
    width: 20px;
    height: 20px;
    border: 1px solid #7FC5C6;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    /* background-color: #7FC5C6; */
    position: relative;
}
.fillter-each div img{
   width: 12.3px;
   height: 9.3px;
   position: absolute;
   left: 4px;
   top: 6px;
}
.fillter-each  > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 9px);
 }
 
 .generate-btn-web{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 314px;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    position: absolute;
    bottom: 0;
    left: calc(50% - (314px / 2));
    margin-top: 40px;
    cursor: pointer;
    margin-bottom: 20px;
}
.app-reject-box{
    width: calc(100% - 32px);
    position: absolute;
    cursor: pointer;
    height: 44px;
    bottom: 16px;
    color: white;
}
.app-btn-web{
    width: calc(50% - 7px);
    height: 100%;
    background-color: #7FC5C6;
    border-radius: 22px;
    float: left;
  

}
.reject-btn-web{
    width: calc(50% - 7px);
    height: 100%;
    border-radius: 22px;
    margin-right: 14px;
    background-color:#FF0000;
   float: left;
}
.progress-box{
    width: calc(100% - 16px);
    position: relative;
    padding-top: 30px;
}
.progress-texxt{
    height: 40px;
}
.progress-texxt > div:first-child{
    position: absolute;
    left: 0px;
    bottom: 18px;
    font-size: 17px;
    font-weight: bold;
}
.progress-texxt > div:not(:first-child){
    position: absolute;
    right: 0px;
    font-size: 28px;
}
.progress-bar{
    width: calc(100% );
    height: 16px;
    border: 1px solid #B2B2B2;
    border-radius: 8px;
}
.progress-line{
    width: 20%;
    height: 100%;
    border-radius: 8px;
    background-color: #7FC5C6;
}

.cancel-refer-btn{
    position: absolute;
    bottom: 16px;
    color: white;
    background-color: #FF3B30;
    font-size: 17px;
    font-weight: bold;
    border-radius: 18px;
    width: 128px;
    left: calc(50% - (128px / 2));
    height: 36px;
    cursor: pointer;
}
.refer-btn{
    position: absolute;
    bottom: 16px;
    color: white;
    background-color: #7FC5C6;
    font-size: 17px;
    font-weight: bold;
    border-radius: 18px;
    width: 128px;
    left: calc(50% - (128px / 2));
    height: 36px;
    cursor: pointer;
}

/* table page */
.table-box{
    width: 100%;
   padding: 8px 0px;
    background-color: #F8F8F8;
    position: relative;
    
}
.table-box2{
    width: 100%;
   padding: 8px 0px;
   cursor: pointer;
    position: relative;
    
}
.table-1{
    height: 100%;
    width: 34%;
    float: left;
    padding-left: 12px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
    font-size: 17px;
}
.table-2{
    height: 100%;
    width: 40%;
    padding-left: 12px;
    box-sizing: border-box;
    float: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
    font-size: 17px;
}
.table-3{
    height: 26px;
    width: 106px;
    float: right;
    padding-right: 12px;
    font-size: 17px;
    box-sizing: border-box;
}
.waitlist-btn{
    height: 26px;
    line-height: 26px;
    color: white;
    padding-left: 8px;
    padding-right: 8px;
    position: absolute;
    right: calc(16px + 20px);
    top: calc(50% - 13px);
    font-size: 15px;
    border-radius: 13px;
    text-align: center;
    background-color: #007AFF;
    cursor: pointer;
}
.table-box2 img{
    height: 14px;
    width: 14px;
    position: absolute;
    right: 16px;
    top: calc(50% - 7px);
}

.section-head2{
    width: calc(100% + 26px);
    height: 26px;;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    margin-bottom: 9px;
}.section-head2 img{
    width: 18px;;
    margin-left: 6px;
}
/* .div-select{

} */
.select-section{
    width: 309px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding-left: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
    margin-bottom: 20px;
    margin-left: 6px;
}
.select-section::placeholder{
    color: #B2B2B2;
}

.checkmark_radio.special{
    top: 12px !important;
}
.ln-box{
    width: 100%;
    margin-bottom: 20px;
    position: relative;
}
.left-head-menu{
    width: 125px;
    padding-left: 16px;
    display: inline-block;
    position: relative;
    color: #8E8E93;
    font-size: 15px;
}
.next-head-menu{
    display: inline-block;
    position: relative;
    font-size: 22px;
}
.next-head-menu2{
    display: inline-block;
    position: relative;
    font-size: 17px;
}
.btn-box-detail{
    width: fit-content;
    position: relative;
    margin: 0px auto;
    display: flex;
    margin-top: 32px;
}
.btn-box-detail div:first-child{
    width: 164px;
    height: 44px;
    position: relative;
    border-radius: 22px;
    background-color: #FF3B30;
    color: white;
    font-size: 17px;
    font-weight: bold;
    margin-right: 15px;
    box-sizing: border-box;
    margin-bottom: 8px;
}
.btn-box-detail div:not(:first-child){
    width: 164px;
    height: 44px;
    position: relative;
    border-radius: 22px;
    background-color: #7FC5C6;
    color: white;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
}
.search-right-box{
    position: absolute;
    right: 0px;
    height: 36px;
    display: flex;
    align-items: center;
    top: calc(50% - (36px / 2));
}
.search-right-box > img{
    height: 28px;
    width: 28px;
    margin-right: 12px;
}

.add_btn{
    border-radius: 50%;
    background-color: #7FC5C6;
    width: 62px;
    height: 62px;
    position: fixed;
    bottom: calc(42px );
    right:42px;
    text-align: center;
    padding-top: 19px;
    box-sizing: border-box;
}
.add_btn img{
    width: 24px;
    height: 24px;
}
.refer-box{
    padding: 15px;
    position: absolute;
    display: flex;
    font-size: 17px;
    background-color: #F8F8F8;
    border-radius: 12px;
    top: calc(52px / 2);
    right: 43px;
    z-index: 1;
}




/* popup */
.popup-login-patientlist{
    width: 100%;
    height: 100%;
    position: fixed;
    box-sizing: border-box;
    background-color: rgba(0 , 0, 0, 0.25);
    z-index: 80;
    margin-top: -52px;
    padding: unset;
}

.popup-box-patientlist{
    width: 375px;
    background-color: #F8F8F8;
    border-radius: 14px;
    right: 280px;
    position: absolute;
    top: 160px;
    top: calc(160px + (env(safe-area-inset-top) * 0));
    box-sizing: border-box;
}
.fillter-head{
    position: relative;
    margin: 0px 16px;
    padding: 9px 0px;
    border-bottom:1px solid #C8C7CC;
    font-size: 17px;
    font-weight: 600;
}
.fillter-content{
    position: relative;
    background-color: white;
    padding: 0px 16px;
    font-size: 17px;
   
}
.fillter-each-popup{
    padding-top:9px;
    box-sizing: border-box;
    border-bottom:1px solid #C8C7CC;

}
.popup-btn-patientlist{
    width: 100%;
    height: 44px;
    position: relative;
    background-color: white;
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
    text-align: center;
    font-size: 17px;
    font-weight:bold;
    color: #7FC5C6;
    padding: 12px 16px;
    box-sizing: border-box;
}

.popup-btn-patientlist > div {
    width: 50%;
    display: inline-block;
    /* box-sizing: border-box; */
    margin-left: -3px;
}
.popup-box-patientlist img{
    width: 30px;
    height: 13px;
    position: absolute;
    top: -12px;
    right: 14px;
}


.content-flex{
    width: 100%;
    align-items: center;
    display: inline-block;
    margin-top: 20px;
}

.section-head-reg{
    font-size: 17px;
    display: inline-block;
}
.input-section-criteria{
    width: 343px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
    display: inline-block;
    margin-left: 20px;
    margin-bottom: unset;
}
.container.flex{
    display: inline-block;
    margin-bottom: unset;
    margin-left: 40px;
}
.ward-choice{
    width: 225px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 10px 0px 10px 0px;
    border-radius: 6px;
    text-align: center;
    display: inline-block;
    box-sizing: border-box;
    margin-left: 20px;
    cursor: pointer;
    margin-bottom: 8px;
}
.ward-choose{
    width: 225px;
    font-size: 17px;
    font-weight: bold;
    border: 1px solid #57AAAC;
    background-color: #7FC5C6;
    color: white;
    padding: 10px 0px 10px 0px;
    border-radius: 6px;
    display: inline-block;
    text-align: center;
    box-sizing: border-box;
    margin-left: 20px;
    margin-bottom: 8px;
    cursor: pointer;
}


.svisited{
    width: 20px;
    height: 20px;
    border-radius: 50%;
    float: left;
    position: relative;
    border: 1px solid #7FC5C6;
 }
 .section-visited{
     background-color: #7FC5C6;
 }
 .section-active{
     border: 1px solid #7FC5C6;
     color: white;
     background-color: #7FC5C6;
 }
 .section-unactive{
     border:1px solid #B2B2B2;
     color: #B2B2B2;
 }