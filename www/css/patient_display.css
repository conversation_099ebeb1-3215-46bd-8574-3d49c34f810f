body{
    background-color: #FFFFFF;
}
.frame_view_padding {
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 50px;
}
.frame_view_padding.select_hospital{
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: unset;
}
.header_large_title {
    background-color: #F8F8F8;
    height: calc(96px + 74px);
}
.header_large_title .title {
   color: #000000;
   font-size: 22px;
   font-weight: bold;
   bottom: calc( 84px);
}
.main_section_double{
    padding-top: 170px;
    padding-top: calc(170px + (env(safe-area-inset-top)));
    overflow-x: auto;
}

.tn-box{
    width: calc(100% + 32px);
    height: 84px;
    position: absolute;
    bottom: 0;
    padding-left: 16px;
    padding-right: 16px;
    background-color: #F8F8F8;
    margin-top: -2px;
    margin-left: -16px;
    box-sizing: border-box;
    font-size: 17px;
    /* border-bottom: 1px solid rgba(0, 0, 0, 0.3); */
}
.tn-box div:first-child {
   font-size: 20px;

}
.tn-box div{
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
}
.list-box{
    width: calc(100% + 16px);
    height: 100%;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}

.choose_hospital{
    width: calc(100%);
    padding-top: 30px;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
}

.fillter-each{
    height: 23px;
    padding: 15px 0px;
    position: relative;
    border-bottom: 1px solid #C8C7CC;
    display: flex;

}
.fillter-each div{
    width: 20px;
    height: 20px;
    border: 1px solid #7FC5C6;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    /* background-color: #7FC5C6; */
    position: relative;
}
.fillter-each div img{
   width: 12.3px;
   height: 9.3px;
   position: absolute;
   left: 4px;
   top: 6px;
}
.fillter-each  > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 9px);
 }
 .save-btn{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: calc(100% - 16px);
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    margin-top: 18px;
    margin-bottom: 40px;
}
.app-btn{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: calc(100% - 16px);
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    margin-top: 14px;
    margin-bottom: 8px;
}
.reject-btn{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: calc(100% - 16px);
    background-color: #FF3B30;
    height: 44px;
    border-radius: 22px;
    margin-bottom: 14px;
}
.progress-box{
    width: calc(100% - 16px);
    position: relative;
    padding-top: 30px;
}
.progress-texxt{
    height: 40px;
}
.progress-texxt > div:first-child{
    position: absolute;
    left: 0px;
    bottom: 18px;
    font-size: 17px;
    font-weight: bold;
}
.progress-texxt > div:not(:first-child){
    position: absolute;
    right: 0px;
    font-size: 28px;
}
.progress-bar{
    width: calc(100% );
    height: 16px;
    border: 1px solid #B2B2B2;
    border-radius: 8px;
}
.progress-line{
    width: 0%;
    height: 100%;
    border-radius: 9px;
    
    background-color: #7FC5C6;
}

.option_icon.cancel-refer {
    margin-top: 4px;
}
.cancel-refer{
    float: right;
    background-color: red;
    border-radius: 18px;
    width: fit-content;
    color: white;
    font-size: 17px;
    font-weight: bold;
    padding: 4px 16px;
}

.margin-btm{
    margin-bottom: 14px;
}
/* iPhone X */
@media (width: 375px) and (height: 812px){
   
}
/* iPad */
@media (min-width: 768px) and (min-height: 1024px){
 
}

/* iPad pro */
@media (min-width: 1024px) and (min-height: 1366px){
   
    
}
  



/* .list-home{
    height: 28px;
     padding: 12px 0px 12px 16px;
     position: relative;
 } */
 
 
 /* .list-home > div {
     float: left;
     height: 100%;
     width: calc(100% );
     padding-bottom: 12px;
     border-bottom: 1px solid #eee;
     font-size: 17px;
     position: relative;
 }
 
 .list-home > div > img {
    height: 16px;
    position: absolute;
    right: 12px;
    top: calc(50% - 14px);
 }
 .choose_hospital{
     font-size: 17px;
     font-weight: bold;
     margin-top: 30px;
     box-sizing: border-box;
     padding-left: 16px;
 } */