body{
    background-color: #FFFFFF;
}
.frame_view_padding {
    padding-left: 16px;
}
.list-box{
    width: calc(100%);
    height: 100%;
    padding-right: 16px;
    padding-bottom: 16px;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}
.box-image-icon2 {
    display: inline-block;
}
.box-image-icon2 img{
    width: 18px;
    transform: translate(4px,0px);
}
.choose_hospital{
    width: calc(100% + 16px);
    padding-top: 30px;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    border-bottom: 1px solid #C8C7CC;
}
.choose_sd{
    font-size: 13px;
    color: #B2B2B2;
}
.head-input{
    font-size: 17px;
    margin-top: 8px;
    /* margin-bottom: -2px; */
}

.input-register{
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}
.margin-btm{
    margin-bottom: 12px;
}
.input-register::placeholder{
    color: #B2B2B2;
    font-size: 17px;
}
.Optional{
    font-size: 17px;
    color: #B2B2B2;
    font-style: italic;
}
.choose-sex{
    width: 100%;
    height: 44px;
    display: flex;
    font-size: 17px;
    cursor: pointer;
}
.choose-sex div:first-child{
    width: 50%;
    text-align: center;
    border-top-left-radius: 6px;
    border-bottom-left-radius:6px;
    box-sizing: border-box;
}
.choose-sex div:not(:first-child) {
    width: 50%;
    text-align: center;
    border-top-right-radius: 6px;
    border-bottom-right-radius:6px;
    box-sizing: border-box;
}
.choose-fm{
    background-color: #7FC5C6;
    border: 1px solid #7FC5C6;
    color: white;
    font-weight: bold;
}
.unchoose-fm{
    border:1px solid #B2B2B2;
    background-color: white;
}
textarea {
    resize: none;
    border:1px solid #B2B2B2;
    width: 100%;
    height: 108px;
    box-sizing: border-box;
    border-radius: 6px;
    padding: 12px;
    font-size: 17px;
 }
 textarea::placeholder{
     color: #B2B2B2;
 }

.save-btn{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 100%;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
   
}


/* checkbox */
/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 4px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 0px;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: white;
    border:2px solid #7FC5C6;
    border-radius: 6px;
}


/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: #7FC5C6;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 5px;
    top: 1px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}


/* iPhone X */
@media (width: 375px) and (height: 812px){
   
}
/* iPad */
@media (min-width: 768px) and (min-height: 1024px){
    
}

/* iPad pro */
@media (min-width: 1024px) and (min-height: 1366px){
   
    
}
  


