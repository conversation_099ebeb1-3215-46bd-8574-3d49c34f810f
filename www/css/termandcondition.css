body{
    background-color: #7FC5C6;
}
.frame_view_padding {
    padding-left: 16px;
    padding-right: 16px;
}
.header_large_title {
    background-color:  #7FC5C6;
}
.header_large_title .title {
   color: white;
}

.btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}
.next-ntn{
    background-color:#FFFFFF;
    color: #7FC5C6;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    margin-bottom: 12%;
}
.next-ntn img{
    width: 5px;
    height: 10px;
}

.term-content{
    position: relative;
    box-sizing: border-box;
    background-color: white;
    border-radius: 8px;
    font-size: 14px;
    padding: 16px;
    height: calc(100vh - (96px + 128px + 40px + env(safe-area-inset-bottom)));
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}
.bottom-box{
    width: 100%;
    height: 120px;
    padding-top: 12px;
    position: relative;
    box-sizing: border-box;
}

/* checkbox */
/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 24px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #FFFFFF;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 5px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: #FFFFFF;
    border-radius: 4px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
    background-color: #FFFFFF;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: #FFFFFF;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 5px;
    top: 2px;
    width: 3px;
    height: 8px;
    border: solid #7FC5C6;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}


/* iPhone X */
@media (width: 375px) and (height: 812px){
   
}
/* iPad */
@media (min-width: 768px) and (min-height: 1024px){
    .frame_view_padding {
        width: 414px;
        margin-left: calc(50% - (414px / 2));
        /* padding-top: 12%; */
    }
}

/* iPad pro */
@media (min-width: 1024px) and (min-height: 1366px){
    .frame_view_padding {
        width: 414px;
        margin-left: calc(50% - (414px / 2));
        /* padding-top: 24%; */
    }
    
}
  

