* {
    font-family: 'NotoSansDisplay', 'NotoSansThai' !important;
}

#ui-datepicker-div {
    width: 300px !important;
    max-width: 414px !important;
    left: calc(50vw - (300px / 2)) !important;
    top: calc(50vh - (300px / 2)) !important;
    box-sizing: border-box !important;
    height: 300px !important;
    border-radius: 14px;
    border: none;
    box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.1);
    padding: 0px !important;
}

.ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: 6px !important;
    width: calc(100%) !important;
    height: 62px;
    background: #FE3B30;
    border: none;
    box-sizing: border-box !important;
    border-radius: 14px 14px 0px 0px !important;
}

.ui-datepicker table {
    width: calc(100% - 12px);
    font-size: .9em;
    border-collapse: collapse;
    margin: 0;
    margin-left: 6px;
    margin-top: 6px;
    height: calc(100% - (62px + 12px));
}

.ui-datepicker .ui-datepicker-title {
    margin: 0px !important;
    line-height: 1.8em !important;
    text-align: left !important;
}

.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
    width: calc(40%) !important;
    font-size: 24px !important;
    height: 36px !important;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);
    border: none !important;
    background: #D31B10 !important;
    color: #FFFFFF !important;
    float: left !important;
    padding: 6px 6px !important;
    box-sizing: content-box !important;
    text-align: center !important;
    -webkit-appearance: none !important;
    line-height: 32px !important;
    border-radius: 8px !important;
}

.ui-datepicker select.ui-datepicker-year {
    width: calc(20%) !important;
}

.ui-datepicker th {
    padding: 0px !important;
    height: 24px !important;
}

.ui-datepicker td {
    padding: 0px !important; 
}

.ui-datepicker td span, .ui-datepicker td a {
    display: block;
    vertical-align: middle;
    text-align: center;
    text-decoration: none;
    height: calc(100% - 2px) !important;
    width: calc(100% - 2px) !important;
    border: none !important;
    margin-top: 1px;
    margin-left: 1px;
    box-sizing: border-box;
    border-radius: 24px !important;
    font-size: 15px;
    line-height: 18px;
    padding-top: calc((100% - 24px) / 2);
    font-weight: bold !important;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
    border: none;
    background: #f6f6f6;
    color: #454545;
    border-radius: 24px !important;
}