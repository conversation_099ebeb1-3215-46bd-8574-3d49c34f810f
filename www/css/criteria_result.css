body.cri4{
    background-color: #7FC5C6;
}
body.cri5{
    background-color: #DEA19E;
}
.frame_view_padding {
    padding-bottom: 16px;
    padding-top: 32px;
}
.header {
    background-color: unset;
}
.bar_single_line .title-main {
    color: white;
}
.header_large_title {
    border-bottom: unset;
}
.list-box{
    width: calc(100%);
    height: 100%;
    padding-right: 16px;
    padding-left: 16px;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}
.section-head{
    width: calc(100% + 16px);
    height: 58px;;
    padding-top: 30px;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    border-bottom: 1px solid #FFFFFF;
    display: flex;
    margin-bottom: 9px;
    color: white;
}
.section-head img{
    width: 18px;;
    margin-left: 6px;
}
.cri-img {
    width: 99px;
    height: 107px;
    position: relative;
    left: calc(50% - (99px / 2));
    margin-bottom: 6px;
}
.cri-text1{
    width: 100%;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    color: white;
    margin-bottom: 6px;
}
.cri-text2{
    width: 100%;
    text-align: center;
    font-size: 15px;
}

.color-pink{
    background-color: #DEA19E;
    color: #DEA19E;
}
.color-green{
    background-color: #7FC5C6;
    color: #7FC5C6;
}

.color-text-pink{
    color: #805C5B;
}
.color-text-green{
    color: #497273;
}

.fillter-each{
    height: 23px;
    position: relative;
    display: flex;
    color: white;
    font-size: 17px;
    margin-bottom: 6px;

}
.fillter-each div{
    width: 22px;
    height: 22px;
    border: 1px solid white;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    position: relative;
    margin-top: -1px;
}
.fillter-each div img{
   width: 12.3px;
   height: 9.3px;
   position: absolute;
   left: 5px;
   top: 7px;
}
.fillter-each  > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 9px);
 }

 .btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}
.next-ntn{
    background-color:#FFFFFF;
   
    text-align:center;
    font-size:17px;
    font-weight:bold;
}
.color-btn-pink{
    color: #DEA19E;
}
.color-btn-green{
    color: #7FC5C6;
}
.next-ntn img{
    width: 5px;
    height: 10px;
}
.back-ntn{
    border: 1px solid white;
    color: white;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    margin-bottom: 14px;
}
.btn-footer-special{
    width: calc(100% - 32px);
    position: absolute;
    bottom:  16px;
    bottom:  calc(16px + (env(safe-area-inset-bottom)));
}
.btn-footer{
    width: calc(100% - 32px);
    position: absolute;
    bottom:  16px;
    bottom:  calc(16px + (env(safe-area-inset-bottom)));
}
.tn-box{
    width: calc(100% );
    height: 84px;
    position: relative;
    color: white;
    box-sizing: border-box;
    font-size: 17px;
}

.scrollable-section {
    width: calc(100% + 16px);
    height: calc(100% - 284px);
    margin-top: 24px;
}





@media (max-height: 666px) and (max-width: 374px){
    .btn-footer {

    }
    .frame_view_padding {
        padding-top: 0px;
    }
}

/* iPhone X */
@media (width: 320px) and (max-height: 666px) {
   
}
/* iPad */
@media (min-width: 768px) and (min-height: 1024px){
    
}

/* iPad pro */
@media (min-width: 1024px) and (min-height: 1366px){
   
    
}
  


