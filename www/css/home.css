
body{
    background-color:white;
}
.main_section_double{
    overflow-x: auto;
}
.head-home{
    position: relative;
    height: 232px;
    height: calc(232px + (env(safe-area-inset-top)));
    /* margin-top: calc(-98px + (env(safe-area-inset-top))); */
    background-image: url('../img/bghome.svg') ;
    background-repeat: no-repeat;
    background-size: cover;
}
.head-home img{
    width: 128px;
    display: block;
    position: absolute;
    top: 24px;
    top: calc(24px + (env(safe-area-inset-top)));
    left: calc(50% - (128px / 2));
    
}
.name-box{
    position: absolute;
    top: 158px;
    top: calc(158px + (env(safe-area-inset-top)));
    width: 100%;
}

.name-person{
    text-align: center;
    font-size: 22px;
}
.name-hospital{
    font-size: 17px;
    font-weight: 300;
    text-align: center;
    margin-top: -4px;
}
.rank-box{
    width: 100%;
    height: 28px;
    position: absolute;
    bottom: calc(-14px);
    text-align: center;
}
.rank{
    height: 28px;
    box-sizing: border-box;
    font-size: 15px;
    border-radius: 16px;
    color: white;
    background-color: #7FC5C6;
    padding: 4px 26px;
    position: relative;
    z-index: 1;
 
}
.list-content{
    height: calc(100vh -  340px);
    position: relative;
    margin-top: 16px;
    padding-bottom: 10px;
}

.list-home{
   height: 28px;
    padding: 12px 0px 12px 12px;
    position: relative;
}
.list-home > img{
    float: left;
    height: 28px;
    margin-right: 10px;
}

.list-home > div {
    float: left;
    height: 100%;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    font-size: 22px;
    position: relative;
}

.list-home > div > img {
   height: 16px;
   position: absolute;
   right: 12px;
   top: calc(50% - 14px);
}

.list-home span {
    width: calc(100% - 25px);
    display: inline-block;
    white-space: nowrap;
    overflow-x: hidden;
    overflow-y: hidden;
    text-overflow: ellipsis;
    line-height: 25px;
    padding-bottom: 2px;
}


.pagging-2item{
    padding-top: 24px;
}
.margin-i1tem{
    margin-top: 20px;
}



::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: #C7C7CC;
    font-size:17px;
  }
  ::-moz-placeholder { /* Firefox 19+ */
    color: #C7C7CC; 
    font-size:17px;
  }
  :-ms-input-placeholder { /* IE 10+ */
    color: #C7C7CC; 
    font-size:17px;
  }
  :-moz-placeholder { /* Firefox 18- */
    color: #C7C7CC; 
    font-size:17px;
  }

    /* iPhone5 */
@media (max-width: 320px){

}
/* galaxy s5 */
@media (min-width: 360px){
    
}
/* iPhone6 */
@media (min-width: 375px) and (min-height: 667px){
   
}
/* iPhone6+ */
@media (width: 414px) and (height: 736px){

}

/* s8+ */
@media (min-width: 360px) and (min-height: 740px){

}

/* iPhone X */
@media (width: 375px) and (height: 812px){
   
}

/* iPad */
@media (min-width: 768px) {
    
}

/* iPad Pro */
@media (min-width: 1024px) {
  
    
}

