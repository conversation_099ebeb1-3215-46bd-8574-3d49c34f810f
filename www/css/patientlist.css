body{
    background-color: #FFFFFF;
}
.frame_view_padding {
    padding-left: 16px;
    padding-right: 16px;
}
.frame_view_padding.select_hospital{
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: unset;
}
.frame_view_padding.refer{
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: unset;
}
.header_large_title {
    background-color: #F8F8F8;
    height: calc(96px + 46px);
}
.header_large_title .title {
   color: #000000;
   bottom: calc(9px + 46px);
}
.main_section_double{
    padding-top: 143px;
    padding-top: calc(143px + (env(safe-area-inset-top)));
    overflow-x: auto;
}
.main_section_double.help_and_support3{
    padding-top: 44px;
    padding-top: calc(44px + (env(safe-area-inset-top)));
    overflow-x: auto;
}
.search-box{
    width: calc(100% + 32px);
    height: 46px;
    position: absolute;
    bottom: 0;
    padding-left: 16px;
    padding-right: 16px;
    background-color: #F8F8F8;
    margin-top: -2px;
    margin-left: -16px;
    box-sizing: border-box;
    /* border-bottom: 1px solid rgba(0, 0, 0, 0.3); */
}
.search-box input{
    width: 100%;
    height: 36px;
    border: none;
    box-sizing: border-box;
    background-color: #DFDFE0;
    border-radius: 10px;
    padding-left: 30px;
    font-size: 17px;
    color: #000000;
}

.search-box input::placeholder{
    color: #8E8E93;
}

.search-box img{
   position: absolute;
   height: 14px;
   width: 14px;
   top: 11px;
   left: 24px;
}
.list-box{
    width: calc(100% + 32px);
    height: 100%;
    left: -16px;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}
.list-box.search{
    width: calc(100%);
    height: 100%;
    left: unset;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}
.list-box.patientlist{
    width: calc(100% + 32px);
    height: 100%;
    left: -16px;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    padding-bottom: 192px;
    -webkit-overflow-scrolling: touch;
}
.refer.each-list{
    width: calc(100% + 32px);
    left: -16px;
    padding: 12px 16px;
    position: relative;
    box-sizing: border-box;
    border-bottom: 0.5px solid #C8C7CC;
}
.each-list{
    width: 100%;
    padding: 12px 16px;
    position: relative;
    box-sizing: border-box;
    border-bottom: 0.5px solid #C8C7CC;
}
.each-list.req{
    width: 100%;
    padding: 17px 16px;
    position: relative;
    box-sizing: border-box;
    border-bottom: 0.5px solid #C8C7CC;
}
.bg-gray{
    background-color: #F8F8F8;
    padding: 20.5px 16px;
}
.refer-noti{
    color: #000000;
    font-size: 17px;
    /* height: 20px; */
    overflow: hidden;
    width: calc(100% - 14px);
    position: relative;
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;  */
}

.number-noty{
        background-color: #E5706A;
        height: 26px;
        min-width: 26px;
        width: max-content !important;
        padding: 0px 8px;
        line-height: 24px;
        border-radius: 13px;
        box-sizing: border-box;
        color: white;
        text-align: center;
        font-size: 15px;
        position: absolute;
        right: 34px;
        top: calc(50% - 13px);     
}

.number-noty-2{
    background-color: #E5706A;
    height: 26px;
    min-width: 26px;
    width: max-content !important;
    padding: 0px 8px;
    line-height: 24px;
    border-radius: 13px;
    box-sizing: border-box;
    color: white;
    text-align: center;
    font-size: 15px;
    position: absolute;
    right: 34px;
    top: calc(50% - 19px);     
}

.name-patient{
    color: #000000;
    font-size: 17px;
    height: 20px;
    overflow: hidden;
    width: calc(100% - 120px);
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; 
}
.tn-patient{
    color: #8E8E93;
    font-size: 15px;
}
.save-btn{
    height: 26px;
    line-height: 26px;
    color: white;
    padding-left: 8px;
    padding-right: 8px;
    position: absolute;
    right: calc(16px + 20px);
    top: calc(50% - 13px);
    font-size: 15px;
    border-radius: 13px;
    text-align: center;
    background-color: #75ACE6;
}

.status-no-next{
    height: 26px;
    line-height: 26px;
    color: white;
    padding-left: 8px;
    padding-right: 8px;
    position: absolute;
    right: calc(16px - 8px);
    top: calc(50% - 13px);
    font-size: 15px;
    border-radius: 13px;
    text-align: center;
    background-color: #75ACE6;
}

.req-btn{
    height: 26px;
    line-height: 26px;
    color: white;
    padding-left: 8px;
    padding-right: 8px;
    position: absolute;
    right: calc(16px + 20px);
    top: calc(50% - 13px);
    font-size: 15px;
    border-radius: 13px;
    text-align: center;
}
.wait-color{
    background-color:#E5CC62;
}
.approve-color{
    background-color:#71DE83;
}
.can-color{
    background-color: #E5706A;
}
.each-list img{
     height: 14px;
     width: 14px;
     position: absolute;
     right: 16px;
     top: calc(50% - 7px);
}
.number-active{
    text-align:center;
    font-size: 15px;
    color: #8E8E93;
    margin: 12px 0px 24px 0px;
}
.footer{
 border-top: 0.5px solid rgba(0, 0, 0, 0.3);;
}
.footer > div{
    float: left;
    width:50%;
    position: relative;
    height: 100%;
    text-align: center;
}
.footer div img{
    width: 27.06px;
    height: 25.5px;
     /* left: calc(50% - (27.6px / 2)); */
     position: relative;
     margin-top: 4px;
}
.footer .box{
    float: left;
    width: 33.33%;
    position: relative;
    height: 100%;
    text-align: center;
}
.footer-text{
    margin-top: -4px;
    font-size: 10px;
}
.text-green{
    color: #7FC5C6;
    font-weight: bold;
}
.text-gray{
    color: #8E8E93;
}
.box img{
   width: 56px;
   height: 43px;
    /* left: calc(50% - 28px); */
    position: relative;
    margin-top: 3px;
}
.txt-box{
    width: 100%;
    position: relative;
    margin-top: 12px;
}
.txt-head{
    color: #8E8E93;
    font-size: 15px;
}
.txt-detail{
    font-size: 22px;
    color: black;
}
.approve-box{
    width: 100%;
    height: 44px;
    position: relative;
    margin: 16px 0px;
}
.denial-btn{
    width: calc(50% - 8px);
    height: 100%;
    float: left;
    background-color: #FF3B30;
    color: #FFFFFF;
    font-size: 17px;
    box-sizing: border-box;
    font-weight: bold;
    border-radius: 22px;
    margin-right: 16px;
}
.accept-btn{
    width: calc(50% - 8px);
    height: 100%;
    float: left;
    background-color: #7FC5C6;
    color: #FFFFFF;
    font-size: 17px;
    box-sizing: border-box;
    font-weight: bold;
    border-radius: 22px;
}


/* popup */
.popup-login-patientlist{
    width: 100%;
    height: 100%;
    position: fixed;
    box-sizing: border-box;
    background-color: rgba(0 , 0, 0, 0.25);
    z-index: 80;
    padding: unset;
}

.popup-box-patientlist{
    width: 100%;
    /* height: 294px; */
    background-color: #F8F8F8;
    border-radius: 14px;
    /* padding: 24px; */
    position: absolute;
    top: 48px;
    top: calc(48px + (env(safe-area-inset-top)));
    box-sizing: border-box;
    /* z-index: 1; */
}
.popup-box-patientlist2{
    width: 100%;
    /* height: 294px; */
    max-height: 500px;
    background-color: #F8F8F8;
    border-radius: 14px;
    /* padding: 24px; */
    position: absolute;
    top: 48px;
    top: calc(48px + (env(safe-area-inset-top)));
    box-sizing: border-box;
    /* z-index: 1; */
}
.fillter-head{
    position: relative;
    margin: 0px 16px;
    padding: 9px 0px;
    border-bottom:1px solid #C8C7CC;
    font-size: 17px;
    font-weight: 600;
}
.fillter-content{
    position: relative;
    background-color: white;
    padding: 0px 16px;
    font-size: 17px;
   
}
.fillter-each{
    padding-top:9px;
    box-sizing: border-box;
    border-bottom:1px solid #C8C7CC;

}
.popup-btn-patientlist{
    width: 100%;
    height: 44px;
    position: relative;
    background-color: white;
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
    text-align: center;
    font-size: 17px;
    font-weight:bold;
    color: #7FC5C6;
    padding: 10px 16px;
    box-sizing: border-box;
}

.popup-btn-patientlist > div {
    width: 50%;
    display: inline-block;
    /* box-sizing: border-box; */
    margin-left: -3px;
}
.popup-box-patientlist img{
    width: 30px;
    height: 13px;
    position: absolute;
    top: -12px;
    right: 14px;
}
.popup-box-patientlist2 img{
    width: 30px;
    height: 13px;
    position: absolute;
    top: -12px;
    right: 58px;
}
.border{
    border-left:1px solid #C8C7CC;
}
.add_btn{
    border-radius: 12px;
    background-color: #7FC5C6;
    width: 141px;
    height: 42px;
    position: fixed;
    bottom: 66px;
    bottom: calc(66px + (env(safe-area-inset-bottom)));
    right: 16px;
    text-align: center;
    padding-top: 19px;
    box-sizing: border-box;
}
.add_btn img{
    width: 20px;
    height: 22px;
    left: 2px;
    position: absolute;
    margin-left: 11px;
    top: 10px;

}
.add_btn .add-txt_btn{
    position: absolute;
    margin-left: 44px;
    top: 10px;
    color: #FFFFFF;
}

.input-register{
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
}

.input-register::placeholder{
    color: #B2B2B2;
    font-size: 17px;
}


.search-btn.search{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 100%;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    position: relative;
    margin-top: 20px;
  
}
.search-detail.search{
    font-size: 15px;
    margin-top: 34px;
    margin-bottom: 12px;
}

.list-home{
    height: 28px;
     padding: 12px 0px 12px 16px;
     position: relative;
 }
 .list-home > img{
     float: left;
     height: 28px;
     margin-right: 10px;
 }
 
 .list-home > div {
     float: left;
     height: 100%;
     width: calc(100% );
     padding-bottom: 12px;
     border-bottom: 1px solid #eee;
     font-size: 17px;
     position: relative;
 }
 
 .list-home > div > img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 14px);
 }
 .choose_hospital{
     font-size: 17px;
     font-weight: bold;
     margin-top: 30px;
     box-sizing: border-box;
     padding-left: 16px;
 }
 
 /* setting */

 .list-setting{
    padding: 12px 0px 12px 12px;
    position: relative;
    height: 28px;
}
.list-setting > img{
    float: left;
    height: 28px;
    margin-right: 10px;
}

.list-setting > div {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 7px;
    border-bottom: 1px solid #eee;
    font-size: 22px;
    position: relative;
}

.list-setting .box-adaptive {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 18px;
    border-bottom: 1px solid #eee;
    font-size: 22px;
    position: relative;
}

.list-setting> div > img {
   height: 16px;
   position: absolute;
   right: 12px;
   top: calc(50% - 14px);
}
.list-setting> div > div {
    width: calc(100% - 20px) ;
 }
 

.txt-15{
    font-size: 15px;
    padding: 0px 16px;
}
.margin-top{
    margin-top: 20px;
}

/* help */
.txt-help3{
    width: calc(100% - 32px);
    font-size: 22px;
    font-weight: bold;
    margin-left: 16px;
    margin-top: 16px;
    margin-bottom: 16px;
    border-bottom: 1px dashed #8E8E93;
}
.txt-help3-2{
    width: calc(100% - 32px);
    font-size: 17px;
    margin-left: 16px;
    margin-top: 16px;
    margin-bottom: 16px;
}

.input-section {
    width: calc(100% - 28px);
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
   margin-left: 28px;
}
.head-in{
    color: #000000;
    font-size: 17px;
    font-weight: bold;
    padding-left: 28px;
    margin-top: 8px;
}

.input-section::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}

.sd-txt {
    font-size: 15px;
    color: #8e8e93;
}

.height-filter-list {
    height: 440px;
}

/* checkbox */
/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
    margin-bottom: 6px;
}
.withimg{
    display: flex !important;
    align-items: center;
}
.withimg img{
    margin-left: 6px;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 1.5px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: white;
    border:2px solid #7FC5C6;
    border-radius: 6px;
}


/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: #7FC5C6;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 5px;
    top: 1px;
    width: 3px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

/* radiobuttun */
/* The container */
.container_radio {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 6px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container_radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark_radio {
    position: absolute;
    top: 1px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: white;
    border-radius: 50%;
    border: 2px solid #7FC5C6;
}



/* When the radio button is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}


/* When the checkbox is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark_radio:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container_radio input:checked ~ .checkmark_radio:after {
    display: block;
}

/* Style the checkmark/indicator */
.container_radio .checkmark_radio:after {
    top: 2px;
	left: 2px;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #7FC5C6;
}

.list-setting span {
    width: calc(100% - 55px);
    display: inline-block;
    white-space: nowrap;
    overflow-x: hidden;
    overflow-y: hidden;
    text-overflow: ellipsis;
    line-height: 25px;
    padding-bottom: 2px;
}

.margin-btm{
    margin-bottom: 14px;
}

.clearfix {
    height: unset;
    padding: 12px 0px 0px 12px;
}


/* iPhone X */
@media (width: 375px) and (height: 812px){
   
}
/* iPad */
@media (min-width: 768px) {
    .ipd-popup {
        width: 414px;
        position: absolute;
        right: 0px;
    }
}

/* iPad pro */
@media (min-width: 1024px) and (min-height: 1366px){
   
    
}
  




 

