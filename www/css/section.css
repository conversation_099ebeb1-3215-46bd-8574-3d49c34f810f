body{
    background-color: #FFFFFF;
}
.frame_view_padding {
   
    padding-bottom: 16px;
}
.list-box{
    width: calc(100%);
    height: 100%;
    padding-right: 16px;
    padding-left: 16px;
    overflow-x: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}
.section-head{
    width: calc(100% + 16px);
    height: 58px;;
    padding-top: 30px;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    margin-bottom: 9px;
    align-items: center;
}

.bar-delete-list-input {
    width: 100%;
    margin-bottom: 6px;
}

.circle-red {
    width: 28px;
    height: 28px;
    border-radius: 14px;
    background-color: #E5706A;
    position: absolute;
    top: 8px;
    right: 8px;
    text-align: center;
    box-sizing: border-box;
    padding-top: 4px;
}
.red-sec5{
    width: 24px;
    height: 24px;
    border-radius: 14px;
    background-color: #E5706A;
    position: absolute;
    top: 18px;
    right: 24px;
    text-align: center;
    box-sizing: border-box;
    padding-top: 2px;
}

.circle-red img {
    width: 64%;
}

.section-head img{
    width: 18px;
    margin-left: 6px;
}

.select-head img{
    width: 18px;
    margin-left: 6px;
}


.box-image-icon {
    display: inline-block;
}

.box-image-icon img{
    width: 18px;
    margin-left: 6px;
    position: absolute;
    top: -14px;
}

.box-image-icon2 {
    display: inline-block;
}

.box-image-icon2 img{
    width: 18px;
    transform: translate(0px,2px);
}

.section-head2{
    width: calc(100% + 26px);
    height: 26px;;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
    margin-bottom: 9px;
}.section-head2 img{
    width: 18px;;
    margin-left: 6px;
}
.section-head-noline{
    width: calc(100%);
    height: fit-content;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}
.section-head-noline img{
    width: 18px;;
    margin-left: 6px;
 
}

.inside-box2{
    width: calc(100% + 32px);
    left: -16px;
    background-color: #F2F2F2;
    padding: 8px 16px 20px 16px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 9px;
}
.inside-box{
    width: calc(100% + 32px);
    left: -16px;
    background-color: #F2F2F2;
    padding: 8px 16px 8px 28px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 9px;
}
.pop1{
    background-color: #F6F1F1 ;
}
.inside-box-with-pad{
    width: calc(100% + 32px);
    left: -16px;
    background-color: #F2F2F2;
    padding: 8px 16px 8px 34px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 9px;
}
.inside-in{
    width: calc(100% + 48px);
    height: 100%;
    margin-left: -32px;
    padding: 8px 16px 8px 32px;
    box-sizing: border-box;
    position: relative;
    background-color: #E6E6E6;
    margin-bottom: 8px;

}
.inside-in-with-pad{
    width: calc(100% + 50px);
    height: 100%;
    margin-left: -34px;
    padding: 8px 16px 8px 32px;
    box-sizing: border-box;
    position: relative;
    background-color: #E6E6E6;
    margin-bottom: 8px;
}
.inside-trans{
    width: calc(100% );
    padding: 8px 0px 8px 18px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 8px;
}
.inside-trans2{
    width: calc(100% + 16px);
    margin-left: -16px;
    padding: 0px 0px 0px 34px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 0px;
}
.inside-box-with-pad2{
    width: calc(100% + 34px + 16px);
    left: -34px;
    background-color: #F2F2F2;
    padding: 8px 16px 8px 34px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 9px;
}
.inside-in2{
    width: calc(100% + 48px);
    height: 100%;
    margin-left: -32px;
    padding: 8px 16px 8px 48px;
    background-color: #E5706A;
    box-sizing: border-box;
    position: relative;
    background-color: #E6E6E6;
    margin-bottom: 8px;

}

.inside-in2-with-pad{
    width: calc(100% + 50px);
    height: 100%;
    margin-left: -34px;
    padding: 8px 16px 8px 48px;
    background-color: #E5706A;
    box-sizing: border-box;
    position: relative;
    background-color: #E6E6E6;
    margin-bottom: 8px;

}
.inside-in-in{
    width: calc(100% + 48px);
    height: 100%;
    margin-left: -32px;
    padding: 8px 16px 8px 48px;
    background-color: #E5706A;
    box-sizing: border-box;
    position: relative;
    background-color: #D9D9D9;
    margin-bottom: 8px;
}
.inside-in-in2{
    width: calc(100% + 64px);
    height: 100%;
    margin-left: -48px;
    padding: 8px 16px 8px 48px;
    background-color: #E5706A;
    box-sizing: border-box;
    position: relative;
    background-color: #D9D9D9;
    margin-bottom: 8px;
}
.inside-in-in3{
    width: calc(100% + 64px);
    height: 100%;
    margin-left: -48px;
    padding: 8px 16px 8px 64px;
    background-color: #E5706A;
    box-sizing: border-box;
    position: relative;
    background-color: #D9D9D9;
    margin-bottom: 8px;
}
.input-section {
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}
.input-sec5{
    width: 85%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}
.margin-btm{
    margin-bottom: 12px;
}
.input-section::placeholder{
    color: #B2B2B2 !important;
    font-size: 17px;

}

.bar-delete-list-input .input-section {
    width: calc(100% - 54px);
}

.input-small{
    width: 80px;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
    text-align: center;
}
.input-small::placeholder{
    color: #B2B2B2 !important;
}
.select-section{
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding-left: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
    margin-bottom: 12px;
}
.select-section::placeholder{
    color: #B2B2B2;
}
.select-head::placeholder{
 color: #B2B2B2;
}
.select-head{
    width: calc(100%);
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    /* margin-top: 8px; */
}

.margin-top{
    margin-top: 20px;
}
.margin-top12{
    margin-top: 12px;
}
.margin-top8{
    margin-top: 8px;
}
.margin-btm4{
        margin-bottom: 4px !important;
}
.choose_sd{
    font-size: 13px;
    color: #B2B2B2;
    margin-bottom: 6px;
}

.choose_sd_black{
    font-size: 13px;
    color: #000;
    margin-bottom: 6px;
}

.margin-left15{
    margin-left: 15px;
}

.save-btn{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 100%;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
    margin: 32px 0;
   
}

.section-head-reg{
    font-size: 17px;
    margin-top: 8px;
    
}
.section-head-reg2{
    font-size: 17px;
    /* margin-top: 8px; */
    
}

.padding-left{
    padding-left: 28px;
}

.textarea{
    width: 100%;
    resize: none;
    height: 205px;
    box-sizing: border-box;
    position: relative;
    padding: 8px;
    font-size: 17px;
}
.graph-box{
    width: 100%;
    height: 205px;
    box-sizing: border-box;
    position: relative;
    background-color: #FFFFFF;
    font-size: 17px;
    margin-top: 8px;
}

.ward-choice{
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 10px 0px 10px 0px;
    border-radius: 6px;
    text-align: center;
    box-sizing: border-box;
    margin-bottom: 8px;
}
.ward-choose{
    width: 100%;
    font-size: 17px;
    font-weight: bold;
    border: 1px solid #57AAAC;
    background-color: #7FC5C6;
    color: white;
    padding: 10px 0px 10px 0px;
    border-radius: 6px;
    text-align: center;
    box-sizing: border-box;
    margin-bottom: 8px;
}
.section-box{
    width: 146px;
    left: calc(50% - (146px / 2));
    margin: 0 auto;
    text-align: center;
    position: absolute;
    bottom: 0px;
}
.section-line{
    width: 29px;
    margin-top: 13px;
    float: left;
}
.section-line.uncorrect{
    border-bottom: 1px solid#B2B2B2;
}
.section-line.correct{
    border-bottom: 1px solid #7FC5C6;
}
.section-visited img{
    width: 15.55px;
    height: 11.42px;
    position: absolute;
    left: 6px;
    top: 9px;
 }
.svisited{
   width: 28px;
   height: 28px;
   border-radius: 50%;
   float: left;
   box-sizing: border-box;
   position: relative;
   padding-bottom: 3px;
}
.section-visited{
    background-color: #7FC5C6;
}
.section-active{
    border: 1px solid #7FC5C6;
    color: white;
    background-color: #7FC5C6;
}
.section-unactive{
    border:1px solid #B2B2B2;
    color: #B2B2B2;
}
.width2_1{
    width: 100%;
}
.width2_1 > div > div {
    width: calc(50% - 15px);
    height: 100%;
    float: left;
  
 }
 .set-height2{
    height: 24px;
}
 .set-height{
     height: 44px;;
     position: relative;
 }
 .width2_1 > div > img{
    width: 9.32px;
    height: 18.65px;
    position: absolute;
    top:  calc(50% -  (18.65px / 2));
    left: calc(50% -  (9.32px / 2));
  
 }
.width2{
   width: 100%;
   height: 44px;
}
.width2 > div {
    width: calc(50% - 12px);
    height: 100%;
    float: left;
  
 }
 .margin-left24{
     margin-left: 24px;
 }
 .margin-left30{
    margin-left: 30px;
}


 .export-btn img{
    width: 5px;
    height: 10px;
}
.export-btn{
    background-color: #7FC5C6;
    color: white;
    text-align:center;
    font-size:17px;
    font-weight:bold;
    
}
.cancel-btn{
    background-color: #E5706A;
    color: white;
    text-align:center;
    font-size:17px;
    font-weight:bold;
}
.btn-footer-special{
    width: calc(100% - 32px);
    position: absolute;
    bottom:  16px;
    bottom:  calc(16px + (env(safe-area-inset-bottom)));
}
.btn-footer{
    width: calc(100% - 32px);
    position: absolute;
    bottom:  16px;
    bottom:  calc(16px + (env(safe-area-inset-bottom)));
} .btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}


 /* export detail*/
 .list-detail{
    padding: 12px 0px 0px 0px;
    position: relative;
 }
 .list-detail2.color{
    padding: 12px 0px 0px 16px;
    position: relative;
    width: calc(100% + 16px);
    margin-left: -16px;
    background-color: #F8F8F8;
 }
 .list-detail2{
    padding: 12px 0px 0px 16px;
    position: relative;
    width: calc(100% + 16px);
    margin-left: -16px;
 }
 .status-detail{
    width: 20px;
    height: 20px;
    border: 1px solid #7FC5C6;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    position: relative;
    float: left;
  
 }
 .status-green{
    border: 1px solid #7FC5C6;
    background-color: #7FC5C6;
 }
 .status-red{
    border: 1px solid #E5706A;
    background-color: #E5706A;
 }
 .status-detail img{
    width: 12.3px;
    height: 9.3px;
    position: absolute;
    left: 4px;
    top: 6px;
 }
 .status-detail-box{
    width: 20px;
    height: 100%;
    margin-right: 8px;
    position: relative;
    float: left;
    margin-top: 1px;
 }
 .line-status-top{
    position: absolute;
    width: 0px;
    bottom: 22px;
    left: 11px;
    height: 13px;
 }
 .line-status{
    position: absolute;
    width: 0px;
    top: 22px;
    left: 11px;
    height: calc(100% + 22px);
    
 }
 .line-status2{
    position: absolute;
    width: 0px;
    top: 22px;
    left: 11px;
    height: calc(100% + 91px);
    
 }
 .line-dashed{
    border: 0.5px dashed #C7C7CC;
 }
 .line-complte{
    border: 0.5px solid  #7FC5C6;
   
 }
 .line-dash-complte{
    border: 0.5px dashed #7FC5C6;
 }
.txt-detail {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    font-size: 22px;
    position: relative;
}
.txt-detail2 {
    float: left;
    width: calc(100% - (28px + 10px));
    padding-bottom: 12px;
    font-size: 22px;
    position: relative;
}
.left-detail{
    float: left;
    font-size: 17px;
    width: 60%;
    height: 100%;
}
.right-detail{
    float: right;
    width: 40%;
    height: 100%;
    text-align: right;
    font-size: 15px;
    color: #8E8E93;
}
.name-txt{
    font-size: 22px;
    font-weight: bold;
}
.name-txt2{
    font-size: 20px;

}

.name-txt3{
    font-size: 17px;
}

.img_info{
    width: 18px;
    height: 18px;
    vertical-align: middle;
}

.list-home{
    height: 23px;
    padding: 15px 0px;
    position: relative;
    border-bottom: 1px solid #C8C7CC;
    display: flex;
 }

 .more-icon-img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 9px);
 }
 
 /* .list-home > div {
     float: left;
     height: 100%;
     width: calc(100% );
     padding-bottom: 12px;
     border-bottom: 1px solid #eee;
     font-size: 17px;
     position: relative;
 } */
 
 .list-home > div > .more-icon-img {
    height: 16px;
    position: absolute;
    right: 16px;
    top: calc(50% - 14px);
 }

.circle_process {
    width: 20px;
    height: 20px;
    border: 1px solid #7FC5C6;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    /* background-color: #7FC5C6; */
    position: relative;
    display: inline-block;
}

.circle_process img {
    width: 12.3px;
    height: 9.3px;
    position: absolute;
    left: 4px;
    top: 6px;
}

.circle_process_fill {
    width: 20px;
    height: 20px;
    border: 1px solid #7FC5C6;
    border-radius: 50%;
    align-items: center;
    margin-right: 8px;
    background-color: #7FC5C6;
    position: relative;
    display: inline-block;
}

.circle_process_fill img {
    width: 12.3px;
    height: 9.3px;
    position: absolute;
    left: 4px;
    top: 6px;
}

 .plus-box{
     width: 100%;
     height: 42px;
     position: relative;
     margin: 8px 0px;
 }
 .plus{
     background-color: #82C6C7;
     height: 42px;
     width: 42px;
     border-radius: 50%;
     position: absolute;
     right: 0;
 }
 .plus img{
     width: 18px;
     height: 18px;
     position: absolute;
     left: 12px;
     top: 12px;
 }
 .gray-txt{
     color: #B2B2B2;
     margin-right: 8px;
 }
 .black-txt{
     color: black;
     margin-right: 8px;
 }
 .black-txt2{
    color: black;
    margin-left: 8px;
 }

.column-sep {
    width: calc((100% / 3) - 12px);
    box-sizing: border-box;
    margin-right: 12px;
    display: inline-block;
}

.column-sep-last {
    margin-right: 0px;
}

.txt-bold-left{
    font-size: 17px;
    font-weight: 600;
    color: #000000;
    margin-left: -12px;
    margin-bottom: 8px;
}

.txt-obli{
    font-style: oblique;
}
/* checkbox */
/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
    margin-bottom: 6px;
}
.withimg{
    display: flex !important;
    align-items: center;
}
.withimg img{
    margin-left: 6px;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 1.5px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: white;
    border:2px solid #7FC5C6;
    border-radius: 6px;
}


/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: #7FC5C6;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 5px;
    top: 1px;
    width: 3px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

/* radiobuttun */
/* The container */
.container_radio {
    display: block;
    position: relative;
    padding-left: 28px;
    margin-bottom: 6px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #000;
    font-size: 17px;
}

/* Hide the browser's default checkbox */
.container_radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark_radio {
    position: absolute;
    top: 1px;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: white;
    border-radius: 50%;
    border: 2px solid #7FC5C6;
}



/* When the radio button is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}


/* When the checkbox is checked, add a blue background */
.container_radio input:checked ~ .checkmark_radio {
    background-color: white;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark_radio:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container_radio input:checked ~ .checkmark_radio:after {
    display: block;
}

/* Style the checkmark/indicator */
.container_radio .checkmark_radio:after {
    top: 3px;
	left: 3px;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #7FC5C6;
}



@media (max-height: 666px) and (max-width: 374px){
    .btn-footer {
        width: 100%;
      position: relative;
      margin: 32px 0;
      bottom: unset;
    }
    .name-txt{
        font-size: 20px;
    }
    .name-txt2{
        font-size: 18px;
    }
    .name-txt3{
        font-size: 16px;
    }
    
}


/* iPhone X */
@media (width: 375px) and (height: 812px){
   
}
/* iPad */
@media (min-width: 768px) and (min-height: 1024px){
    
}

/* iPad pro */
@media (min-width: 1024px) and (min-height: 1366px){
   
    
}
  


