body{
    background-color: #FFFFFF;
}
.frame_view_padding {
    padding-left: 16px;
    padding-right: 16px;
    padding-bottom: 50px;
}
.header_large_title {
    background-color: #F8F8F8;
    height: 325px;
}
.main_section_double{
    padding-top: 326px;
    padding-top: calc(326px + (env(safe-area-inset-top)));
}
.txt-box{
    width: 100%;
    position: relative;
    margin-top: 12px;
}
.txt-head1{
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    padding: 16px 0px 6px 0px;
    border-bottom: 0.5px solid  #B2B2B2;
}
.txt-head{
    color: #8E8E93;
    font-size: 15px;
}
.txt-detail{
    font-size: 22px;
    color: black;
}
.approve-box{
    width: 100%;
    height: 44px;
    position: relative;
    margin: 16px 0px;
}
.denial-btn{
    width: calc(50% - 8px);
    height: 100%;
    float: left;
    background-color: #FF3B30;
    color: #FFFFFF;
    font-size: 17px;
    box-sizing: border-box;
    font-weight: bold;
    border-radius: 22px;
    margin-right: 16px;
}
.accept-btn{
    width: calc(50% - 8px);
    height: 100%;
    float: left;
    background-color: #7FC5C6;
    color: #FFFFFF;
    font-size: 17px;
    box-sizing: border-box;
    font-weight: bold;
    border-radius: 22px;
}




/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 9px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
   
}

/* Hide the browser's default radio button */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

/* Create a custom radio button */
.checkmark {
    position: absolute;
    top: 5px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: white;
    border-radius: 50%;
    border: 1px solid #7FC5C6;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
    background-color: white;
}

/* When the radio button is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: white;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the indicator (dot/circle) when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the indicator (dot/circle) */
.container .checkmark:after {
 	top: 3px;
	left: 3px;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #7FC5C6;
}
.margin-btm{
    margin-bottom: 14px;
}
/* iPad */
@media (min-width: 768px) {
   
    
}

/* iPad Pro */
@media (min-width: 1024px) {
    
}
