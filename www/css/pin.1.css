.pin-box{
    width: 100%;
    height: 100vh;
    position: fixed;
    background-color: #7FC5C6;
    z-index: 10000000;
    text-align: center;
    color: white;
    box-sizing: border-box;
    padding: 60px 0px 50px 0px;
    padding-top: calc(100vw * 50/375);
}
.pin-box img{
    width: 26px;
    height: 34px;
    margin-bottom: 8%;
}
.pin-lock{
    width: 100%;
    text-align: center;
    margin-top: 8%;
    margin-bottom: 8%;
}
.pin-lock  span:first-child{
    width: 16px;
    height: 16px;
    border: 1px solid #FFFFFF;
    border-radius: 50%;
    box-sizing: border-box;
    display: inline-block;
}
.pin-lock  span:not(:first-child){
    width: 16px;
    height: 16px;
    border: 1px solid #FFFFFF;
    border-radius: 50%;
    margin-left: 23px;
    box-sizing: border-box;
    display: inline-block;
}
.pin-main {
    font-size: 42px;
    line-height: 42px;
    font-weight: 600;
    position: relative;
    height: 378px;
    width: 300px;
    margin-left: calc(50% - (300px / 2));
}
.first-box{
    width: 78px;
    height:78px;
    border-radius: 50%;
    box-sizing: border-box;
    display: inline-block;
    background-color: #92CECF;
    margin-left: unset;
    position: relative;
    margin-bottom: 12px;
    transition: 0.75s;
}

.first-box:active {
    transition: 0.1s;
    background-color: #BFE2E2;
}

.not-first{
    width: 78px;
    height:78px;
    border-radius: 50%;
    box-sizing: border-box;
    display: inline-block;
    background-color: #92CECF;
    margin-left: 22px;
    position: relative;
    margin-bottom: 12px;
    transition: 0.75s;
}

.not-first:active {
    transition: 0.1s;
    background-color: #BFE2E2;
}

.text-1{
    font-size: 28px;
}
.text-2{
    font-size: 15px;
}
.h-text-2{
    height: 42px;
}
.foot-box{
    position: absolute;
    bottom: 0px;
    bottom: calc(8px + (env(safe-area-inset-bottom)));
    width: 320px;
    left: calc(50% - (320px / 2));
    height: 44px;
    font-size: 17px;
   
}
.foot-box div:first-child{
    width: 35%;
    height: 100%;
    text-align: center;
    position: absolute;
    left: 0;
}
.foot-box div:not(:first-child){
    width: 35%;
    height: 100%;
    text-align: center;
    position: absolute;
    right: 0;
}


@media (max-width: 375px) and (max-height: 667px) {
   
    .pin-box img {
        display: none;
    }
    .pin-box{
        padding-top: calc(100vw * 40/375);
    }
 
}
@media (max-width: 320px)  {

    .pin-lock{
        margin-top: 4%;
    }
    .pin-box{
        padding-top: calc(100vw * 30/375);
    }
 
}
@media (min-height: 768px) {
   
}
@media (min-width: 1024px) {
    
}

/* iPad Landscape */
@media (orientation: landscape) and (max-height: 1024px) and (max-width: 1366px){

    .pin-box img {
        margin-bottom: 2%;
    }
    .pin-lock {
        margin-top: 4%;
        margin-bottom: 4%;
    }
    .pin-box{
        padding-top: calc(100vw * 30/375);
    }
    
}
/* iPad Landscape */
@media (orientation: landscape) and (max-height: 768px) and (max-width: 1024px){
    
    .pin-box img {
        margin-bottom: 2%;
    }
    .pin-lock {
        margin-top: 4%;
        margin-bottom: 4%;
    }
    .pin-box{
        padding-top: calc(100vw * 15/375);
    }
    
}
