@import "popup.css";
@import "pin.css";
@import "font/NotoSansThai/NotoSansThai.css";
@import "font/NotoSansDisplay/NotoSansDisplay.css";

* {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0) !important; 
    -webkit-focus-ring-color: rgba(255, 255, 255, 0) !important; 
    outline: none !important;
}
body{
    -webkit-touch-callout: none;                /* prevent callout to copy image, etc when tap to hold */
    -webkit-text-size-adjust: none;             /* prevent webkit from resizing text to fit */
    /*-webkit-user-select: none;                   prevent copy paste, to allow, change 'none' to 'text' */
    height:100%;
    margin:0px;
    padding:0px;
    width:100%;
    font-family: 'NotoSansDisplay', 'NotoSansThai';
    position: relative;
}

input, section, textarea, select{
    -webkit-appearance: none;
    -moz-appearance:    none;
    appearance:         none;
    font-family: 'NotoSansDisplay', 'NotoSansThai';
}

input[data-input="date"]:before{
    content: attr(data-date);
    color: black;
}

input[data-input="date"]{
    color: transparent;
}

input[data-input="date"]::-webkit-datetime-edit, input::-webkit-inner-spin-button, input::-webkit-clear-button {
    visibility: hidden;
}

input[data-input=date]::-webkit-calendar-picker-indicator {
    color: black;
}

.info_box .container, .info_box .container_radio{
    width: fit-content;
    display: inline-block;
}

.info_box img{
   margin-left: 0 !important;
}

/* @import url(sarabun-webfont-master/fonts/thsarabunnew.css); */

.set_center{
    position: relative;
    float: left;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    
}
.set_center_left{
    position: relative;
    float: left;
    top: 50%;
    /* left: 50%; */
    transform: translateY(-50%);
}

.content_box {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    position: relative;
}

.content_box_flow {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}

.content_box_normal {
    padding-top: 64px;
    padding-top: calc(44px + (env(safe-area-inset-top) * 0));
}

.content_box_large {
    padding-top: 116px;
    padding-top: calc(96px + (env(safe-area-inset-top) * 0));
}

.content_box_footer {
    padding-bottom: 49px;
    padding-bottom: calc(49px + (env(safe-area-inset-bottom) * 0));
}

.frame_view_padding {
    box-sizing: border-box;
    height: 100%;
    width: 100%;
    /* padding-left: 8%; */
    /* padding-right: 8%; */
}

/* Single Title */

.header {
    position: fixed;
    z-index: 10;
    top: 0;
    left: 0;
    right: 0;
    height: calc(44px);
    width: calc(100% - 22px);

    background-color: #F7F7F7;

    padding: 0px 11px;
    padding-top: 0px; /* #statusbar (Android 0px iOS 20px) */
    padding-top: max(20px, env(safe-area-inset-top));
}

.bar_single_line {
    height: 44px;
}

.bar_single_line .title-main {
    font-size: 17px;
    line-height: 24px;
    /* font-weight: 600; */
    position: absolute;
    left: 20px;
    color: #7FC5C6;
    bottom: calc(50% - (22px / 2));
}

.header .wo_back {
    left: 0px;
}

.header .back_section {
    width: 44px;
    height: 44px;
    position: absolute;
    left: -11px;
}

.header .option_section {
    width: 60%;
    height: 100%;
    position: absolute;
    right: 0px;
    text-align: right;
}

/* Double Title */

.header_double_title {
    position: fixed;
    z-index: 10;
    top: 0;
    left: 0;
    right: 0;
    height: calc(98px);
    width: calc(100% - 22px);

    background-color: #FFFFFF;

    padding: 0px 11px;
    padding-top: 0px; /* #statusbar (Android 0px iOS 20px) */
    padding-top: max(20px, env(safe-area-inset-top));
}

.header_double_title .title {
    font-size: 18px;
    font-weight: 600;
    position: absolute;
    left: 33px;
    top: 10px;
    
}

.header_double_title .wo_back {
    left: 0px;
}

.header_double_title .back_section {
    width: 44px;
    height: 44px;
    position: absolute;
    left: -11px;
}

.header_double_title .option_section {
    width: 60%;
    height: 100%;
    position: absolute;
    right: 0px;
    text-align: right;
}

/* Large Title */

.header_large_title {
    position: fixed;
    z-index: 10;

    top: 0;
    left: 0;
    right: 0;
    height: calc(96px);
    width: calc(100% - 32px);

    background-color: #F8F8F8;
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.3);
    padding: 0px 16px;
    padding-top: 0px; /* #statusbar (Android 0px iOS 20px) */
    padding-top: max(20px, env(safe-area-inset-top));
}

.header_large_title .bar_single_line {
    height: 44px;
}

.header_large_title .title {
    font-size: 34px;
    line-height: 40px;
    font-weight: bold;
    position: absolute;
    left: 0px;
    bottom: 9px;
    text-align: left;
}

.header_large_title .title2 {
    font-size: 20px;
    line-height: 40px;
    position: absolute;
    left: 0px;
    bottom: 9px;
    text-align: left;
}

.header_large_title .wo_back {
    left: 0px;
}

.header_large_title .title_back {
    font-size: 17px;
    position: absolute;
    left: 24px;
    bottom: calc(50% - (21px / 2));
}

.header_large_title .back_section {
    width: 44px;
    height: 44px;
    position: absolute;
    left: -11px;
}

.header_large_title .option_section {
    width: 60%;
    height: 100%;
    position: absolute;
    right: 0px;
    text-align: right;
}

/* icon in Title Bar */

.back_btn {
    height: 20px;
    position: absolute;
    top: calc(50% - (20px /2));
    left: 11px;
}

.option_icon {
    height: 28px;
    margin-top: 8px;
    margin-left: 11px;
}

.footer {
    position: fixed;
    z-index: 10;

    bottom: 0;
    left: 0;
    right: 0;
    height: 49px;

    background-color: #F7F7F7;

    padding-bottom: env(safe-area-inset-bottom);
}

.footer .title {
    width: 100%;
    font-size: 17px;
    text-align: center;
    font-weight: 600;
    position: absolute;
    top: calc(50% - (21px / 2));
}

.main_section {
    position: fixed;
    height: 100vh;
    height: calc(100vh - (env(safe-area-inset-top)));
    width: 100%;

    padding-top: 44px; /* #statusbar (Android 44px iOS 64px) */
    padding-top: calc(env(safe-area-inset-top) + 44px);

    box-sizing: border-box;
}

.main_section_double {
    position: fixed;
    height: 100vh;
    height: calc(100vh - env(safe-area-inset-bottom));
    width: 100%;

    padding-top: 138px;
    padding-top: calc(98px + (env(safe-area-inset-top)));

    box-sizing: border-box;
}
.main_section_nopadding{
    position: fixed;
    height: 100vh;
    height: calc(100vh - env(safe-area-inset-bottom));
    width: 100%;
    box-sizing: border-box;
}

.main_section_large {
    height: 100vh;

    padding-top: 136px;
    padding-top: calc(96px + (env(safe-area-inset-top)));
}

.main_section_footer {
    padding-bottom: 49px;
    padding-bottom: calc(49px + (env(safe-area-inset-bottom)));
}

.popup_box {
    width: 270px;
    height: 180px;
    position: fixed;
    background-color: #FFFFFF;
    z-index: 50;
    border-radius: 15px;
    top: calc(50vh - (180px / 2));
    left: calc(50vw - (270px / 2));
    box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.25);
    box-sizing: border-box;
    padding: 12px;
}

.popup_box .title {
    font-size: 18px;
    line-height: 24px;
    font-weight: bold;
    width: 100%;
    text-align: center;
}

.popup_box .des {
    font-size: 14px;
    line-height: 20px;
    width: 100%;
    margin-top: 8px;
    text-align: center;
}

.btn_sec_group {
    height: 44px;
    position: absolute;
    width: calc(100% + 24px);
    bottom: -12px;
    left: -12px;
    border-top: 1px solid #DFE0E2;
}

.btn_popup_1 {
    font-size: 18px;
    line-height: 26px;
    font-weight: bold;
    color: #37BCF0;
    text-align: center;
    box-sizing: border-box;
    padding-top: 9px;
    height: 100%;
}

.btn_popup_2_1 {
    font-size: 18px;
    line-height: 26px;
    color: #37BCF0;
    text-align: center;
    box-sizing: border-box;
    padding-top: 9px;
    width: 50%;
    height: 100%;
    display: inline-block;
    border-right: 1px solid #DFE0E2;
}

.btn_popup_2_2 {
    font-size: 18px;
    line-height: 26px;
    font-weight: bold;
    color: #37BCF0;
    text-align: center;
    box-sizing: border-box;
    padding-top: 9px;
    width: 50%;
    display: inline-block;
    height: 100%;
}

.star_group {
    width: 100%;
    margin-top: 8px;
    text-align: center;
}

.star_icon {
    height: 32px;
    margin-right: 4px;
}

.star_icon_last {
    height: 32px;
}

.spinner_load {
    width: 100%;
    height: 54px;
    margin-top: 12px;
    margin-bottom: 12px;
    text-align: center;
}

.spinner_icon {
    height: 54px;
}

.login_input_section {
    margin-top: 58px;
}

.input_txt_v1 {
    height: 34px;
    width: 100%;
    border: none;
    border-bottom: solid 1px #d0d0d0;
    font-size: 17px;
    color: #000000;
    border-radius: 0px;
    box-sizing: border-box;
}

.input_txt_v1::placeholder {
    color: #888888;
}

.btn_full_width {
    width: 100%;
    height: 60px;
    border-radius: 30px;
    background-color: #33d574;
    text-align: center;
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    box-sizing: border-box;
    padding-top: 20px;
}

.btn_txt {
    font-size: 17px;
    width: 100%;
    text-align: center;
    color: #888888;
}

.login_input_section .input_txt_v1 {
    margin-bottom: 28px;
}

.login_input_section .btn_full_width {
    margin-top: 12px;
}

.login_input_section .btn_txt {
    margin-top: 30px;
}


/* unvisited link */
a:link {
    color: inherit;
    text-decoration: inherit;
}

/* visited link */
a:visited {
    color: inherit;
    text-decoration: inherit;
}

/* mouse over link */
a:hover {
    color: inherit;
    text-decoration: inherit;
}

/* selected link */
a:active {
    color: inherit;
    text-decoration: inherit;
}


/* switch */
.switch{
    position: absolute;
    right: 18px;
    top: calc(50% - (14px));
    border-radius: 15px;
    width: 41px !important;
    height: 28px;
    background-color: #e0e0e0;
    border: none;
    box-sizing: border-box;
   padding: 4px;
   }
   .switch.setting{
    top: calc(2px) !important;
    right: 12px !important;
   }
   .switch2{
       width: 20px!important;
       height: 20px;
       border-radius: 15px;
       box-sizing: border-box;
       cursor: pointer;
       background-color: #fff;
   }
   .clearfix::after {
    content: "";
    clear: both;
    display: table;
}


/* iPad */
@media (min-width: 768px){

}

/* iPhone5 */
@media (width: 320px){
    .main_section_double{
        padding-top: 98px;
        padding-top: calc(98px + (env(safe-area-inset-top)));
    }
}
@media (max-height: 666px) and (max-width: 374px){
    .header_large_title .help_and_support1  {
        font-size: 28px;
    }
}


/* iPhone4 */
@media (width: 320px) and (height: 480px){

}

/* s8 */
@media (min-width: 320px) and (min-height: 740px){

}

/* iPhone X */
@media (min-width: 375px) and (min-height: 812px){

}

/* s8+ */
@media (min-width: 480px) and (min-height: 960px){

}


@media (width: 375px) and (height: 812px){
    
}

@media (min-width: 768px) and (min-height: 1024px){
    
}

@media (min-width: 1024px) and (min-height: 1366px){
   
}
  