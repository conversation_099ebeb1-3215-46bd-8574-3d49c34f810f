body {
    background-color: #FFFFFF;
}
.frame_view_padding {
    padding-top: 28px;
    padding-left: 16px;
    padding-right: 16px;
}
.error-box{
    width: 100%;
    height: 40px;
    position: relative;
    box-sizing: border-box;
}
.error-text{
    width: 100%;
   text-align: center;
    color: #FF3B30;
    margin-bottom: 8px;
    position: absolute;
    bottom: 0px;
}

.error-text img{
    width: 14px;
    height: 14px;
    vertical-align: middle;
}

.btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}

input{
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px !important;
    background-color: #FFFFFF;
    color: #000000;
    margin-bottom:14px;
}

input::placeholder{
    color: #C7C7CC;
}

.error-type{
    border: 1px solid #FF3B30;
}.login-btn{
    background-color:#7FC5C6;
    color: #fff;
    text-align:center;
    font-size:17px;
    line-height:44px;
    font-weight:bold;
    margin-bottom: 12%;
}
.forget-detail{
    font-size: 17px;
}

.head-input{
    font-size: 17px;
    margin-top: 8px;
    margin-bottom: -2px;
}.input-register{
    width: 100%;
    font-size: 17px;
    border: 1px solid #B2B2B2;
    padding: 12px;
    height: 44px;
    box-sizing: border-box;
    border-radius: 6px;
    background-color: white;
}
.input-register::placeholder{
    color: #B2B2B2;
    font-size: 17px;
}
.margin-btm{
    margin-bottom: 12px;
}.save-btn{
    color: #FFFFFF;
    font-size: 17px;
    font-weight: bold;
    width: 100%;
    background-color: #7FC5C6;
    height: 44px;
    border-radius: 22px;
   
}
.txt-15{
    font-size: 15px;
}

/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 9px;
    cursor: pointer;
    font-size: 17px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
   
}

/* Hide the browser's default radio button */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

/* Create a custom radio button */
.checkmark {
    position: absolute;
    top: 5px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: white;
    border-radius: 50%;
    border: 1px solid #7FC5C6;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
    background-color: white;
}

/* When the radio button is checked, add a blue background */
.container input:checked ~ .checkmark {
    background-color: white;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the indicator (dot/circle) when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the indicator (dot/circle) */
.container .checkmark:after {
 	top: 3px;
	left: 3px;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #7FC5C6;
}
.margin-btm{
    margin-bottom: 14px;
}
/* iPad */
@media (min-width: 768px) {
    
    .frame_view_padding {
        width: 414px;
        margin-left: calc(50% - (414px / 2));
        padding-top: 5%;
    }
    
}

/* iPad Pro */
@media (min-width: 1024px) {
    
    .frame_view_padding {
        width: 414px;
        margin-left: calc(50% - (414px / 2));
        padding-top: 5%;
    }
    
}

