* {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0) !important; 
    -webkit-focus-ring-color: rgba(255, 255, 255, 0) !important; 
    outline: none !important;
}
body{
    
    -webkit-touch-callout: none;                /* prevent callout to copy image, etc when tap to hold */
    -webkit-text-size-adjust: none;             /* prevent webkit from resizing text to fit */
    /*-webkit-user-select: none;                   prevent copy paste, to allow, change 'none' to 'text' */
    margin: 0px;
    padding: 0px;
    background-color:#F2F2F2;
    position: relative;

}
.frame_view_padding {
    padding-left: 8%;
    padding-right: 8%;
}
.content-box{
    width: 100%;
    height: 100%;
    position: relative;
}
.splash-box{
    width: 100%;
    height: 100vh;
    position: relative;
    box-sizing: border-box;
    padding: 22% 8% 8% 8%;
}
.btn-content{
    position:relative;
    box-sizing:border-box;
    width: 100%;
    height:44px;
    border-radius:22px;
}
input{
    border: none;
    background-color:#FFFFFF;
    padding-left:12px;
    font-size:17px;
    color: #000000;
    margin-bottom:14px;
}
.error-type{
    border: 1px solid #FF3B30;
}
.login-btn{
    background-color:#7FC5C6;
    color: #fff;
    text-align:center;
    font-size:17px;
    line-height:44px;
    font-weight:bold;
    margin-bottom: 42px;
}
.forget-btn{
    color: #7FC5C6;
    text-align:center;
    font-size:17px;
    line-height:44px;
    font-weight:bold;
    justify-content: center;
    border:1px solid #7FC5C6;
}
.logo-content{
    width: fit-content;
    position: relative;
    box-sizing: border-box;
    margin: auto;
}
.logo-box{
    width: 96px;
    height: 96px;
    box-sizing: border-box;
    position: relative;
}

.logo-box img {
    height: 100%;
}

.name-app{
    width: fit-content;
    font-size: 32px;
    font-weight: bold;
    color: #7FC5C6;
}
.name-detail{
    width: fit-content;
    font-size: 16px;
    color: #7FC5C6;
}
.error-box{
    width: 100%;
    height: 64px;
    position: relative;
    box-sizing: border-box;
}
.error-text{
    width: 100%;
   text-align: center;
    color: #FF3B30;
    margin-bottom: 8px;
    position: absolute;
    bottom: 0px;
}

.error-text img{
    width: 14px;
    height: 14px;
    vertical-align: middle;
}


.login-h{
    height: 471px;;
    position: absolute;
    top: calc(50% - (569px / 2));
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: #C7C7CC;
    font-size:17px;
  }
  ::-moz-placeholder { /* Firefox 19+ */
    color: #C7C7CC; 
    font-size:17px;
  }
  :-ms-input-placeholder { /* IE 10+ */
    color: #C7C7CC; 
    font-size:17px;
  }
  :-moz-placeholder { /* Firefox 18- */
    color: #C7C7CC; 
    font-size:17px;
  }

    /* iPhone5 */
@media (width: 320px){
    .splash-box{
        padding: 12% 8% 8% 8%;
    }
    .main_section_double {
        padding-top: 48px;
        padding-top: calc(48px + (env(safe-area-inset-top)));
    }
  
}
/* galaxy s5 */
@media (min-width: 360px){
    
}
/* iPhone6 */
@media (min-width: 375px) and (min-height: 667px){
   
}
/* iPhone6+ */
@media (width: 414px) and (height: 736px){

}

/* s8+ */
@media (min-width: 360px) and (min-height: 740px){

}

/* iPhone X */
@media (width: 375px) and (height: 812px){
    .splash-box{
        padding: 35% 8% 8% 8%;
    }
}

/* iPad */
@media (min-width: 768px) {
    .frame_view_padding {
        width: 520px;
        margin-left: calc(50% - (520px / 2));
        /* padding-top: 12%; */
    }
    
}

/* iPad Pro */
@media (min-width: 1024px) {
    .frame_view_padding {
        width: 520px;
        margin-left: calc(50% - (520px / 2));
        /* padding-top: 24%; */
    }
    
}

