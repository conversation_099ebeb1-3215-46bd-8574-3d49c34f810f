var col = ["central_line","status","last_modified_username","last_modified_date"]
var name_array = ["UAC", "UVC", "PICC_line", "other_central_line"]

window.onload = function(){
    //alert("test");
    get_birth_date(function(){
        timeline_min_max("birth_date","now");
      });
    setting();
    load();
}

function setting(){
  //info
  $("#info1").click(function(){
      show_info("สายสวนหลอดเลือดดำส่วนกลาง หรือ สายสวนหลอดเลือดแดงส่วนกลาง เช่น UVC, UAC, PICC line หรือ other central line เป็นต้น")
  })
  $("#info2").click(function(){
    show_info("สายสวนหลอดเลือดดำส่วนกลางอื่น ๆ เช่น Intrajugular, subclavian, femoral intravenous catheter หรือ cut down catheter เป็นต้น")
})

  $("input[name='central_line']").on("change",function(){
    if($("input[name='central_line']:checked").val() == "No"){
        $("#central_line_box").slideUp(200)

        for(var i = 0 ; i < name_array.length ; i++){
            var name = name_array[i];
            $("input[name='"+name+"']").prop("checked", false);
            $("input[name='"+name+"']").trigger("change");
        }
    }
    else if($("input[name='central_line']:checked").val() == "Yes"){
        $("#central_line_box").slideDown(200)

        for(var i = 0 ; i < name_array.length ; i++){
            var name = name_array[i];
            $("input[name='"+name+"_box']")
        }
    }
  });

  $(".plus").on("click", function(){
    add_section_date($(this).attr("id").split("plus_")[1]);
  })

  for(var i = 0 ; i < name_array.length ; i++){
    (function () {
        var name = name_array[i];
        $("input[name='"+name+"']").on("change",function(){
            if($(this).prop("checked") == false){
                $("#"+name+"_list, #"+name+"_list + .plus-box").slideUp(200)
                $("#"+name+"_day").text(0)
                
                $("div[name='"+name+"1'] input").val("").trigger("change");

                var index = 2;
                while($("div[name='"+name+index+"']").length > 0){
                    $("div[name='"+name+index+"']").remove()
                    index++;
                }
            }
            else{
                $("#"+name+"_list, #"+name+"_list + .plus-box").slideDown(200)
            }
        });

    }())
  }

  for(var i = 0 ; i < name_array.length ; i++){
    (function () {
        var name = name_array[i];
        date_listener(name);
    }())
  } 
}


function load(callback){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section_table.php",
        data:{num:"5_10",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result.length);
            var status = "done";
            if(result.length!=0){
                console.log(result);
                push_ob_data(result.main[0]);

                for(var i = 0 ; i < name_array.length ; i++){
                    for(var j = 0 ; j < result["table"+(i+1)].length ; j++){
                        if(result["table"+(i+1)][j]["start_date"]==""&&result["table"+(i+1)][j]["stop_date"]==""){
                
                        }
                        else{
                            var temp = ob_to_array(result["table"+(i+1)][j]);
                            temp.shift();
                            
                            if(j > 0) { 
                                add_section_date(name_array[i]); 
                            }
                            push_all_section_data(name_array[i]+(j+1)+"_", temp);
    
                            $("input[name='"+name_array[i]+"']").prop("checked", true);
                            $("input[name='"+name_array[i]+"']").trigger("change")
                        }
                    }
                }
                
                if($("input[name='central_line']:checked").val() == "Yes"){  
                    var checkbox_flag = 0;
                    for(var i = 0 ; i < name_array.length ; i++){
                        var name = name_array[i]

                        if($("input[name='"+name+"'").prop("checked") == true){  
                            checkbox_flag = 1;

                            for(var j = 0 ; j < result["table"+(i+1)].length ; j++){
                                if(result["table"+(i+1)][j]["start_date"]==""||result["table"+(i+1)][j]["stop_date"]==""){
                                    status = "not done";
                                }
                            }
                        }        
                    }

                    if(!checkbox_flag){
                        status = "not done";
                    }
                }
            
                for(var i = 0 ; i < name_array.length ; i++){
                    var name = name_array[i];
                    $("#"+name+"_list input").trigger("change")         
                }
                
            }else{
                status = "not done";
            }
            //alert(status);
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/update_progress.php",
                data:{num:"5_10",status:status},
                success: function (data) {
                    //alert(status);
                    if(!!callback){
                        callback();
                    }
                },
              });
            
              if(localStorage.hospital != ""){
                //   alert("a");
                   $("input").prop("disabled",true);
                   $("textArea").prop("disabled",true);
                   $("select").prop("disabled",true);
                   $(".save-btn").prop("hidden",true);
                   $(".plus").prop("hidden",true);
                   $(".circle-red").prop("hidden",true);
   }

        },
      });
}
function save(){
    var main = get_ob_data(col);

    var data = {
        main: main,
        table: name_array,
        num: "5_10"
      }
    for(var i = 1 ; i <= name_array.length ; i++){
        data["table"+i] = dimensionx(name_array[i-1]);
    }
    var array = {
        UAC_day : $("#UAC_day").html(),
        UVC_day : $("#UVC_day").html(),
        PICC_day : $("#PICC_line_day").html(),
        other_day : $("#other_central_line_day").html(),
        
    };
    ajax("save_ob_section.php",{data:array,num:"5_10_day"},function(){
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_ob_section_table.php",
            data: data,
            success: function (data) {
                load(function(){
                    alert_success({
                        text:"บันทึกสำเร็จ",
                        button_func_1 : function(){
                            change_page("section5.html","back");
                        }
                    });
                });
            },
        });
    });
}

function add_section_date(name){
   var number = $("#" + name + "_list")[0].children.length + 1; 

   $("#" + name + "_list").append('<div name="'+ name+number +'" class="inside-in-with-pad" hidden>'
                                    +'<div class="">วันเริ่มต้น</div>'
                                    +'<input class="input-section input-sec5" name="'+ name+number +'_1" type="text" ontouchend="setting_input(\'date\',this);" data-input="date" data-validate="l"  placeholder="เลือกวัน">'
                                    +'<div class="margin-top8  ">วันสิ้นสุด</div>'
                                    +'<input class="input-section input-sec5" name="'+ name+number +'_2" type="text" ontouchend="setting_input(\'date\',this);" data-input="date" data-validate="r"  placeholder="เลือกวัน">'
                                    +'<div class="circle-red red-sec5" onclick="del(this,'+"'"+name+"_list'"+')"><img src="img/delete.svg"></div>'                                  
                                    +'</div>')

    timeline_min_max("birth_date","now");
    
    $('div[name="'+ name+number +'"').slideDown(200)
   
    $("#"+name+"_list input").unbind("change");
    $("#"+name+"_list input").change(function(){
        date_tricker($(this))
    })
    date_listener(name)           
}  
function del(ob,container){
 //   alert(container);
    var name = container.split("_")[0];
    if(name=="other"){
        name = "other_central_line";
    }
    $(ob.parentElement).slideUp(200);
    setTimeout(function(){
    document.getElementById(container).removeChild(ob.parentElement);

        var temp =  document.getElementById(container).children;
        for(var i  = 0;i<temp.length;i++){
            $(temp[i]).attr("name",name+(i+1));
            temp[i].children[1].name = name+(i+1)+"_1";
            temp[i].children[3].name = name+(i+1)+"_2";
        
        }
        if(  document.getElementById(container).children.length!=0){
             $("#"+name+"_list input").trigger("change");
        }else{
            $("#"+name+"_day").text(0);
        }
    },200);

}
function date_listener(name){
    $("#"+name+"_list input").on("change",function(){
        var index = 1;
        var sum_days = 0;
        while($("div[name='"+name+index+"']"+" input").length == 2){
            var start_date = $("div[name='"+name+index+"']"+" input")[0].value;
            var end_date = $("div[name='"+name+index+"']"+" input")[1].value;
            
            if(!!start_date && !! end_date){
                console.log(diffDate(end_date, start_date, 'd'))
                sum_days += (diffDate(end_date, start_date, 'd'))
            }

            index++;
        }

        $("#"+name+"_day").text(sum_days)
    });
}