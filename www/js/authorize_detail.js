$(document).ready(function(){
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_user.php",
    data: {username: sessionStorage.authorized_username},
    success: function (data) {
        var result = JSON.parse(data);

        if(result.status == 1){
          $("#name").text(result.data.firstname + " " + result.data.lastname);
          $("#username").text(result.data.username);
          $("#role").text(result.data.role);
          $("#register_date").text(result.data.register_date);
          $("#email").text(result.data.email);
          $("#tel").text(result.data.tel);

          if(result.data.status == "certified"){
            $(".approve-box").prop("hidden", false);
          }
        }
        else{
          
        }
    }
  });

  $("#accept_button").on("click",function(){                           
  
    confirm_o({
      text: "คุณต้องการที่จะอนุมัติ “" + $("#name").text() +"” เป็น Collector หรือไม่?",
      header: "ต้องการอนุมัติ",
      button_text_1: "อนุมัติ",
      button_text_2: "ยกเลิก",
      button_func_1: function(){
        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/authorize_approve.php",
          data: {username: sessionStorage.authorized_username},
          success: function (data) {
            var result = JSON.parse(data);

            if (result.status == 1) {
              alert_success({ 
                text: "อนุมัติ “" + $("#name").text() + "” เป็น Collector เรียบร้อยแล้ว", 
                header: "สำเร็จ", 
                button_func_1: function(){
                  change_page("authorize.html", "next");
                  //window.location = "authorize.html";
                }
              })
            }
            else {
              alert_fail({ text: "ไม่สามารถอนุมัติ “" + $("#name").text() + "” ได้", header: "ผิดพลาด" })
            }
          }
        });
      }
    })
  })

  $("#reject_button").on("click",function(){                           
      confirm_o({
        text: "คุณต้องการที่จะยกเลิก “" + $("#name").text() +"” หรือไม่?",
        header: "ต้องการยกเลิก",
        button_text_1: "ตกลง",
        button_text_2: "ไม่ตกลง",
        button_func_1: function(){
            $.ajax({
              type: "POST",
              url: "https://techvernity.com/thainy/php/authorize_reject.php",
              data: {username: sessionStorage.authorized_username},
              success: function (data) {
                var result = JSON.parse(data);
  
                if(result.status == 1){
                  alert_success({
                    text: "ยกเลิก “" + $("#name").text() +"” เรียบร้อยแล้ว", 
                    header: "สำเร็จ",
                    button_func_1: function(){
                      change_page("authorize.html", "next");
                      //window.location = "authorize.html";
                    }
                  })
                }
                else{
                  alert_fail({text: "ไม่สามารถยกเลิก “"+ $("#name").text() + "” ได้", header: "ผิดพลาด"})
                }
              }
            });
        }
      })

  })
});                        
