var col = ["discharge_status","discharge_or_transfer","discharge_date","home_at_age","home_hospital_day","without_accessary","with_feeding_tube","with_oxygen","with_tracheostomy","transfer_hospital_name","transfer_date","transfer_at_age","transfer_hospital_day","post_transfer_disposition","post_discharge_home_date","post_without_accessary","post_with_feeding_tube","post_with_home_oxygen","post_with_tracheostomy","transfer_again_date","readmit_date","dead_date","dead_age","dead_hospital_day","lethal_congenital","immaturity","severe","IVH","pulmonary_hemorrhage","NEC","PIE_or_airleak_syndrome","BPD","extrame_prematurity","severe_RDS","immaturity_other","severe_asphyxia","PPHN","infection","cause_other","unknow","birth_weight","length","length_NA","head_circumference","head_circumference_NA","discharge_growth_status","growth_status"];
window.onload = function(){
   // get_column("thainy","section6");
    console.log(col);
    get_birth_date(function(){
        timeline_min_max("birth_date","now");
      });
    get_first_date();
    get_first_hospital_date();
    get_gastation(function(){
        if(!GA_day||!birth_date_data){
            var GA_day = (7*parseInt(gastation["gestational_age_week"]))+parseInt(gastation["gestational_age_day"]);
            var PMA = getDate(36*7 - GA_day, birth_date_data);
            $("#PMA").html(get_thai_date(new Date(PMA)));
        }
    });
    
    setting();
    load();
}
function setting(){
    //info
    $("input[name='head_circumference']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
      $("input[name='length']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
    $("#info1").click(function(){
        show_info("ผลรวมการรักษาของผู้ป่วยเมื่อ completed registry") 
    })
    $("#info2").click(function(){
        show_info("ให้กรอกส่วนนี้เฉพาะกรณีที่ผู้ป่วย “กลับบ้าน” หรือ “Refer ไป ThaiNy Non-member Hospital” เท่านั้น (ถ้า refer ไปยัง ThaiNy Member Hospital ยังไม่ต้องกรอก section นี้)") 
    })
    $("#info3").click(function(){
        show_info("กรณีกลับบ้านโดยไม่มีอุปกรณ์เครื่องช่วยต่าง ๆ เช่น ไม่มี feeding tube, home oxygen หรือ tracheostomy tube เป็นต้น") 
    })
    $("#info4").click(function(){
        show_info("กรณีส่งไป ThaiNy Non-member Hospital เท่านั้น") 
    })
    $("#info5").click(function(){
        show_info("สถานะของผู้ป่วยในโรงพยาบาลที่ transfer ไป non-member hospital") 
    })
    $("#info6").click(function(){
        show_info("กรณีกลับบ้านโดยไม่มีอุปกรณ์เครื่องช่วยต่าง ๆ เช่น ไม่มี feeding tube, home oxygen หรือ tracheostomy tube เป็นต้น") 
    })
    $("#info7").click(function(){
        show_info("ให้กรอกส่วนนี้เฉพาะกรณีที่ผู้ป่วย “กลับบ้าน” หรือ “Refer ไป ThaiNy Non-member Hospital” เท่านั้น (ถ้า refer ไปยัง ThaiNy Member Hospital ยังไม่ต้องกรอก section นี้)") 
    })
    $("#info8").click(function(){ 
        show_info("<img id='fenton' style='width: 100%;' src='img/fenton_boy.jpg'>"
        +"<span id='fenton_boy_text' style='text-decoration:underline; font-weight:600;'>Boy</span> / " 
        +"<span id='fenton_girl_text'>Girl</span>") 

        $("#fenton_boy_text").click(function(){
            $("#fenton").attr("src", "img/fenton_boy.jpg")
            $(this).css({
                "text-decoration":"underline",
                "font-weight":600
            })
            $("#fenton_girl_text").removeAttr("style")
        })
        $("#fenton_girl_text").click(function(){
            $("#fenton").attr("src", "img/fenton_girl.jpg")
            $(this).css({
                "text-decoration":"underline",
                "font-weight":600
            })
            $("#fenton_boy_text").removeAttr("style")
        })
        
        $("#popup_container").css("padding", "5%");
        $("#popup_box").css("width", "100%");
        $("#popup_box").css("left", "0");
        setTimeout(function(){
            $("#popup_box").css("top", $("#popup_container").height()/2 - $("#popup_box").outerHeight()/2);
        },20)
    })

    var criteria = get_criteria();
    console.log(criteria);

    floatx("birth_weight",0);
    floatx("length",1);
    floatx("head_circumference",1);
    if(criteria["Major anomalies"]=="1"){
        $("#popx").prop("hidden",true);
    }
    if(criteria["HIE"]=="1"){
        $("#popx").prop("hidden",true);
    }
    if(criteria["BW < 1,500g"]=="1"){
        $("#popx").prop("hidden",false);
    }
    if(criteria["GA < 32 weeks GA"]=="1"){
        $("#popx").prop("hidden",false);
    }
    $("input[name='without_accessary']").on("change",function(){
        if($(this).prop("checked")==true){
            dis_ob(col,6,8);
        }
    });
    for(var i =6;i<=8;i++){
        $("input[name='"+col[i]+"']").on("change",function(){
            if($(this).prop("checked")==true){
                dis_ob(col,5,5);
            }
        });
    }

    $("input[name='post_without_accessary']").on("change",function(){
        //alert("test");
        if($(this).prop("checked")==true){
            dis_ob(col,16,18);
        }
    });
    for(var i =16;i<=18;i++){
        $("input[name='"+col[i]+"']").on("change",function(){
            if($(this).prop("checked")==true){
                dis_ob(col,15,15);
            }
        });
    }
    $("input[name='discharge_status']").on("change",function(){
        if($(this).prop("checked")==true&&$(this).val()=="Alive") {
            $("#pop1").slideDown(200);
            $("#pop9").slideUp(200);
            $("input[name='cause_other']").slideUp(200);
            $("input[name='unknow']").slideUp(200);
            dis_ob(col,21,39);
            $("input[name='cause_other_main']").prop("checked",false);
            $("input[name='unknow_main']").prop("checked",false);
           // dis("i",25,48);
        }else if($(this).prop("checked")==true&&$(this).val()=="Dead"){
            dis_ob(col,1,20);
            $("#pop1").slideUp(200);
            $("#pop9").slideDown(200);
            $("#pop2").slideUp(200);
            $("#popx").slideUp(200);
            $("#popx_1").slideUp(200);
            $("#popx_2").slideUp(200);
            $("#popx_3").slideUp(200);
           // dis("i",2,24);
        }
    });
    $("input[name='post_transfer_disposition']").on("change",function(){
        if($(this).val()=="discharge_home"&&$(this).prop("checked")==true){
            $("#popx_1").slideDown(200);
            $("#popx_2").slideUp(200);
            $("#popx_3").slideUp(200);
            dis_ob(col,19,20);
        } else  if($(this).val()=="transfer_again"&&$(this).prop("checked")==true){
            $("#popx_1").slideUp(200);
            $("#popx_2").slideDown(200);
            $("#popx_3").slideUp(200);
            dis_ob(col,14,18);
            dis_ob(col,20,20);
        }
        else  if($(this).val()=="readmit"&&$(this).prop("checked")==true){
            $("#popx_1").slideUp(200);
            $("#popx_2").slideUp(200);
            $("#popx_3").slideDown(200);
            dis_ob(col,14,18);
            dis_ob(col,19,19);
        }else if($(this).prop("checked")==true){
            $("#popx_1").slideUp(200);
            $("#popx_2").slideUp(200);
            $("#popx_3").slideUp(200);
            dis_ob(col,14,18);
            dis_ob(col,19,20);
        }
    });
    $("input[name='discharge_or_transfer']").on("change",function(){
        if($(this).prop("checked")==true&&$(this).val()=="Discharge home"){
            $("#pop2").slideDown(200);
            $("#popx").slideUp(200);
            dis_ob(col,9,19);
        }else if($(this).prop("checked")==true&&$(this).val()=="Transfer to Non-member hospital"){
            $("#pop2").slideUp(200);
            dis_ob(col,2,8);
            $("#popx").slideDown(200);
            //dis("i",3,9);
        }
    });

    $("input[name='immaturity']").on("change",function(){
        if($(this).prop("checked")==true){
            $("#pop10").slideDown(200);
          
        }else{
            $("#pop10").slideUp(200);
            dis_ob(col,26,34);
            $("input[name='immaturity_other']").slideUp(200);
            $("input[name='immaturity_other_main']").prop("checked",false);
        }
    });
   
    
    $("input[name='immaturity_other_main']").on("change",function(){
        if($(this).prop("checked")==true){
            //$("#pop10").slideDown(200);
            $("input[name='immaturity_other']").slideDown(200);
          
        }else{
            //$("#pop10").slideUp(200);
            //dis_ob(col,14,22);
            $("input[name='immaturity_other']").slideUp(200);
            $("input[name='immaturity_other']").val("");
        }
    });

    $("input[name='cause_other_main']").on("change",function(){
        if($(this).prop("checked")==true){
            //$("#pop10").slideDown(200);
            $("input[name='cause_other']").slideDown(200);
            $("input[name='unknow_main']").prop("checked",false);
            $("input[name='unknow']").slideUp(200);
            $("input[name='unknow']").val("");
        }else{
            //$("#pop10").slideUp(200);
            //dis_ob(col,14,22);
            $("input[name='cause_other']").slideUp(200);
            $("input[name='cause_other']").val("");
        }
    });

    $("input[name='unknow_main']").on("change",function(){
    
        if($(this).prop("checked")==true){
            //$("#pop10").slideDown(200);
            $("input[name='unknow']").slideDown(200);
            dis_ob(col,24,38);    
            $("input[name='cause_other_main']").prop("checked",false);  
            $("input[name='cause_other']").slideUp(200);
        }else{
            //$("#pop10").slideUp(200);
    
            $("input[name='unknow']").slideUp(200);
            $("input[name='unknow']").val("");
        }
    });
    for(var i = 24;i<26;i++){
        $("input[name='"+col[i]+"']").on("change",function(){
            if($(this).prop("checked")==true){
                $("input[name='unknow_main']").prop("checked",false);
                $("input[name='unknow']").slideUp(200);
                $("input[name='unknow']").val("");
            }
        });
    }
    for(var i = 35;i<38;i++){
        $("input[name='"+col[i]+"']").on("change",function(){
            if($(this).prop("checked")==true){
                $("input[name='unknow_main']").prop("checked",false);
                $("input[name='unknow']").slideUp(200);
                $("input[name='unknow']").val("");
            }
        });
    }
    $("input[name='discharge_date']").on("change",function(){
    //  alert(birth_date_data);
    //   alert(first_date_data);
    //   alert(first_hospital_date_data);
        if($(this).val()!=""){
            $("input[name='home_at_age']").val(diffDate(birth_date_data,$(this).val(),"d"));
            $("input[name='home_hospital_day']").val(diffDate(first_hospital_date_data,$(this).val(),"d"));
        }
    });

    $("input[name='transfer_date']").on("change",function(){
        // alert(birth_date_data);
        // alert(first_date_data);
            if($(this).val()!=""){
                $("input[name='transfer_at_age']").val(diffDate(birth_date_data,$(this).val(),"d"));
            
                $("input[name='transfer_hospital_day']").val(diffDate(first_hospital_date_data,$(this).val(),"d"));
            }
    });

    $("input[name='dead_date']").on("change",function(){
        // alert(birth_date_data);
        // alert(first_date_data);
            if($(this).val()!=""){
                $("input[name='dead_age']").val(diffDate(birth_date_data,$(this).val(),"d"));
                $("input[name='dead_hospital_day']").val(diffDate(first_hospital_date_data,$(this).val(),"d"));
            }
    });
     
    $("input[name='length_NA']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='length']").val("");
            $("input[name='length']").prop("disabled",true);
        }else{
            $("input[name='length']").prop("disabled",false);
        }
    });  
    $("input[name='head_circumference_NA']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='head_circumference']").val("");
            $("input[name='head_circumference']").prop("disabled",true);
        }else{
            $("input[name='head_circumference']").prop("disabled",false);
        }
    });
} 

function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_hospital.php",
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result);
            $("select[name='i11']").html(' <option value="" selected disabled>เลือกโรงพยาบาล</option>');
            for(var i = 0;i<result.length;i++){
                $("select[name='i11']").append(' <option value="'+result[i]["hospital_name"]+'">'+result[i]["hospital_name"]+'</option>');
            }
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/get_ob_section.php",
                data:{num:"6",hospital:localStorage.hospital},
                success: function (data) {
                    var result2 = JSON.parse(data);
                    console.log(result2);
                    if(result2.length!=0){
                        push_ob_data(result2[0]);
                
                        if(result2[0].unknow !=""){
                        
                            $("input[name='unknow_main']").prop("checked",true);
                            $("input[name='unknow']").slideDown(200);
                        }
                     
                        if(result2[0].cause_other !=""){
                           // alert("test");
                            $("input[name='cause_other_main']").prop("checked",true);
                            $("input[name='cause_other']").slideDown(200);

                        }
                        if(result2[0].immaturity_other !=""){
                            $("input[name='immaturity_other_main']").prop("checked",true);
                             $("input[name='immaturity_other']").slideDown(200);
                        }
                        var status = check_status(result2);
                        
                        var temp = result2[0];
                        console.log(temp);
                        var criteria = get_criteria();
                        if((temp["birth_weight"]=="")&&(criteria["GA < 32 weeks GA"]=="1"||criteria["BW < 1,500g"]=="1")){
                            status = "not done";
                        }
                        if(temp["discharge_status"]==""||temp["growth_status"]=="" ){
                            status = "not done";
                        }
                       // console.log(status);
                        if(temp["discharge_status"]=="Alive"&&temp["discharge_or_transfer"]==""){
                            status = "not done";
                        }
                      //  console.log(status);
                        if(temp["discharge_status"]=="Alive"&&temp["discharge_or_transfer"]=="Discharge home"){
                            if(temp["discharge_date"]==""||temp["home_at_age"]==""||temp["home_hospital_day"]==""){
                                status = "not done";
                            }

                            if(temp["without_accessary"]==""&&temp["with_feeding_tube"]==""&&temp["with_oxygen"]==""&&temp["with_tracheostomy"]==""){
                                status = "not done";
                            }
                        }
                      //  console.log(status);
                        if(temp["discharge_status"]=="Dead"){
                            if(temp["dead_date"]==""||temp["dead_age"]==""||temp["dead_hospital_day"]==""){
                                status = "not done";
                            }

                            if(temp["lethal_congenital"]==""&&temp["immaturity"]==""&&temp["severe_asphyxia"]==""&&temp["PPHN"]==""&&temp["infection"]==""&&temp["cause_other"]==""&&temp["unknow"]==""){
                                status = "not done";
                            }

                            if(temp["immaturity"]!=""&&temp["severe"]==""&&temp["IVH"]==""&&temp["pulmonary_hemorrhage"]==""&&temp["NEC"]==""&&temp["PIE_or_airleak_syndrome"]==""&&temp["BPD"]==""&&temp["extrame_prematurity"]==""&&temp["severe_RDS"]==""&&temp["immaturity_other"]==""){
                                status = "not done";
                            }

                        }
                        if(temp["discharge_or_transfer"]=="Transfer to Non-member hospital"){
                            if(temp["transfer_hospital_name"]==""||temp["transfer_date"]==""||temp["transfer_at_age"]==""||temp["transfer_hospital_day"]==""||temp["post_transfer_disposition"]==""||temp["discharge_growth_status"]==""){
                                status = "not done";
                            }

                            if(temp["post_transfer_disposition"]=="discharge_home"){
                                if(temp["post_discharge_home_date"]==""){
                                    status = "not done"
                                }

                                if(temp["post_without_accessary"]==""&&temp["post_with_feeding_tube"]==""&&temp["post_with_home_oxygen"]==""&&temp["post_with_tracheostomy"]==""){
                                    status = "not done";
                                }
                            }
                            if(temp["post_transfer_disposition"]=="transfer_again"){
                                if(temp["transfer_again_date"]==""){
                                    status = "not done"
                                }
                            }

                            if(temp["post_transfer_disposition"]=="readmit"){
                                if(temp["readmit_date"]==""){
                                    status = "not done"
                                }
                            }
                           

                        }
                        if(temp["length"]==""&&temp["length_NA"]==""){
                            status = "not done"
                        }
                        if(temp["head_circumference"]==""&&temp["head_circumference_NA"]==""){
                            status = "not done"
                        }
                        //alert(status);
                        $.ajax({
                            type: "POST",
                            url: "https://techvernity.com/thainy/php/update_progress.php",
                            data:{num:"6",status:status},
                            success: function (data) {
                                //alert(status);
                            },
                          });
                    }else{
                        $.ajax({
                            type: "POST",
                            url: "https://techvernity.com/thainy/php/update_progress.php",
                            data:{num:"6",status:"not done"},
                            success: function (data) {
                                //alert(status);
                            },
                          });
                    }
                    if(localStorage.hospital != ""){
                        //    alert("a");
                            $("input").prop("disabled",true);
                            $("textArea").prop("disabled",true);
                            $("select").prop("disabled",true);
                            $(".save-btn").prop("hidden",true);
                    }

                },
            });
        },
      });

}
function save(){
    var temp = get_ob_data(col);
    console.log(temp);
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_ob_section.php",
        data:{data:temp,num:"6"},
        success: function (data) {
            var result = JSON.parse(data);
           // console.log(result);
           load();
           alert_success({
            text:"บันทึกสำเร็จ",
            button_func_1 : function(){
                change_page("patient_display.html","back");
            }
        });
        },
      });
}