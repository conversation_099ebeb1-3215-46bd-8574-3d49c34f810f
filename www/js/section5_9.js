var col = ["RDS","pulmonary_air_leak","pneumothorax","PIE","pneumomediastinum","other","other_detail","death","death_date","death_day","death_day2","death_reason","PNA_start_date","PNA_end_date","PNA_diagnosis","PNA_nasal","PMA_start_date","PMA_end_date","PMA_diagnosis","PMA_intubation","PMA_CPAP","PMA_NC_1","PMA_NC_2","PMA_O2","old_definition","old_definition_severe","new_definition","new_definition_severe","PPHN","PPHN_require_pulmonary_vasodilator","PDA","PDA_require_medical_ligation","PDA_require_surgical_ligation","PDA_require_support_care","NEC","NEC_require_surgical_treatment","ROP","maximum_staging","treatment_laser","treatment_cryotherapy","treatment_anti_VEGF","treatment_no","IVH","maximum_grading","require_shunt","PVL"]

window.onload = function(){
    //alert("test");
    get_birth_date(function(){
        get_gastation(function(){
            setting();
            load();
        });
        
        timeline_min_max("birth_date","now");
    });
}

function setting(){
    //info
    $("#info1").click(function(){ 
        show_info("Respiratory distress syndrome") 
    })
    $("#info2").click(function(){ 
        show_info("<img style='width: 100%;' src='img/bpd_draft_info.png'>") 
        $("#popup_container").css("padding", "5%");
        $("#popup_box").css("width", "100%");
        $("#popup_box").css("left", "0");
        setTimeout(function(){
            $("#popup_box").css("top", $("#popup_container").height()/2 - $("#popup_box").outerHeight()/2);
        },20)
    })
    $("#info3").click(function(){ 
        show_info("Other morbidities eg, NEC, IVH, redirection of care, sepsis / infection, etc.") 
    })
    $("#info4").click(function(){ 
        show_info("ไม่ต้องการ respiratory support ทุกชนิด และไม่ต้องการ O<sub>2</sub> supplement") 
    })
    $("#info5").click(function(){ 
        show_info("ไม่ต้องการ respiratory support ทุกชนิด และไม่ต้องการ O<sub>2</sub> supplement") 
    })
    $("#info6").click(function(){ 
        show_info("Patent ductus arteriosus, รวมเฉพาะ hemodynamic significant PDA เท่านั้น") 
    })
    $("#info7").click(function(){ 
        show_info("การให้ยาเฉพาะเพื่อปิด PDA เช่น indomethacin, brufen เป็นต้น") 
    })
    $("#info8").click(function(){ 
        show_info("เฉพาะ NEC stage 2 ขึ้นไปเท่านั้น") 
    })
    $("#info9").click(function(){ 
        show_info("Periventicular leukomalacia") 
    })
    

    var criteria = get_criteria();

    if(criteria["Major anomalies"]=="1"){
        
    }
    if(criteria["HIE"]=="1"){
        $("#h_box, #i_box").prop("hidden",false);
    }
    if(criteria["BW < 1,500g"]=="1"){
        $("#a_box, #b_box, #e_box, #g_box, #h_box, #i_box").prop("hidden",false);
    }
    if(criteria["GA < 32 weeks GA"]=="1"){
        $("#a_box, #b_box, #e_box, #g_box, #h_box, #i_box").prop("hidden",false);
    }

    $("input[name='pulmonary_air_leak']").on("change",function(){
        // alert("test");
         if($(this).val()=="Yes"&&$(this).prop("checked")==true){
             $("#pop1").slideDown(200);
         }else if($(this).val()=="No"&&$(this).prop("checked")==true){
             $("#pop1").slideUp(200);
             $("input[name='pneumothorax']").prop("checked",false);
             $("input[name='PIE']").prop("checked",false);
             $("input[name='pneumomediastinum']").prop("checked",false);
             $("input[name='other']").prop("checked",false);
             $("input[name='other_detail']").val("");
         }
     });
     
    $("input[name='other']").on("change",function(){
      
        if($(this).prop("checked")==true){
            $("input[name='other_detail']").slideDown(200)
        }else{
            $("input[name='other_detail']").slideUp(200)
            $("input[name='other_detail']").val("");
        }
    }); 
    

    if(!gastation || !gastation.gestational_age_day || !gastation.gestational_age_week){
        $("#BPD_text").text("กรุณากรอกค่า GA ใน Section 4 ก่อน")
    }
    else{
        GA_week = gastation.gestational_age_week;
        GA_day = parseInt(gastation.gestational_age_week) * 7 + parseInt(gastation.gestational_age_day);

        if(GA_week < 32){
            $("#BPD_text").text("GA < 32 weeks")
        }
        else{
            $("#BPD_text").text("GA >= 32 weeks")
        }

        $(".BPD_box").slideDown(200);
    }    

    var level_object = [
        {name: "death", parent: null },
        {name: "death_date", parent: "death" },
        {name: "death_day", parent: "death" },
        {name: "death_day2", parent: "death" },
        {name: "death_reason", parent: "death_date" },
        {name: "PNA", parent: null, show_function: function(){
                var PNA_28 = getDate(28, birth_date_data);
                var PNA_31 = getDate(31, birth_date_data);
                $("input[name='PNA_start_date']").val(PNA_28).trigger("change");
                $("input[name='PNA_end_date']").val(PNA_31).trigger("change");
            }
        },
        {name: "PNA_diagnosis", parent: "PNA" },
        {name: "PNA_nasal", parent: "PNA_diagnosis" },
        {name: "PMA", parent: null, show_function: function(){
            if(typeof GA_day !== "undefined"){
                var PMA_36 = getDate(36*7 - GA_day, birth_date_data);
                var PMA_36_3 = getDate(36*7 - GA_day + 2, birth_date_data);
                console.log(GA_day)
                $("input[name='PMA_start_date']").val(PMA_36).trigger("change");
                $("input[name='PMA_end_date']").val(PMA_36_3).trigger("change");
            }   
        }},
        {name: "PMA_diagnosis", parent: "PMA" },
        {name: "PMA_intubation", parent: "PMA_diagnosis" },
        {name: "PMA_CPAP", parent: "PMA_diagnosis" },
        {name: "PMA_NC_1", parent: "PMA_diagnosis" },
        {name: "PMA_NC_2", parent: "PMA_diagnosis" },
        {name: "PMA_O2", parent: "PMA_diagnosis" },
    ]

    
    $("input[name='death']").on("change",function(){
        if($("input[name='death']:checked").val() == "Dead"){
            levelSlideDown("death")
            levelSlideUp("PNA")
            levelSlideUp("PMA")
        }
        else if($("input[name='death']:checked").val() == "Alive"){
            levelSlideDown("PNA")
            levelSlideUp("death")
            levelSlideUp("PMA")
       }
     });

     $("input[name='death_date']").on("change",function(){
        var PNA_day = diffDate($(this).val(), birth_date_data, 'd');
        var PMA_week = parseInt(gastation.gestational_age_week) + Math.floor((parseInt(gastation.gestational_age_day) + PNA_day)/7);
        var PMA_day =  (parseInt(gastation.gestational_age_day) + PNA_day) % 7;
        
        if(!!PNA_day){
            if(PNA_day < 28){
                levelSlideUp("PNA");
                levelSlideUp("PMA");
            }
            else{
                if(PMA_week >= 36){
                    levelSlideDown("PNA");
               }
                else{
                    levelSlideUp("PNA");
                    levelSlideUp("PMA");
                }
            }
            

            if(PNA_day >= 14 && PMA_week < 36){
                levelSlideDown("death_reason")
                $("input[name='death_reason']").trigger("change")
            }
            else{
                levelSlideUp("death_reason")
                $("input[name='death_reason']").trigger("change")
            }
        }

        $("input[name='death_day']").val(PNA_day + " Days PNA");
        $("input[name='death_day2']").val(PMA_week + " Weeks " + PMA_day + " Days PMA");
     });

     $("input[name='death_reason']").on("change",function(){
        var PNA_day = diffDate($("input[name='death_date']").val(), birth_date_data, 'd');
        var PMA_week = parseInt(gastation.gestational_age_week) + Math.floor((parseInt(gastation.gestational_age_day) + PNA_day)/7);

        if(!!PNA_day){
            if(PNA_day >= 28 && PMA_week < 36 &&  $("input[name='death_reason']:checked").length > 0){
                levelSlideDown("PNA");
            }
        }
     });

     $("input[name='PNA_diagnosis']").on("change",function(){
        var PNA_day = diffDate($("input[name='death_date']").val(), birth_date_data, 'd');
        var PMA_week = parseInt(gastation.gestational_age_week) + Math.floor((parseInt(gastation.gestational_age_day) + PNA_day)/7);

        var PNA_value = $("input[name='PNA_diagnosis']:checked").val();

        
       
        if(PNA_value == "Room air"){
            if($("input[name='death']:checked").val() == "Alive" ||  PMA_week >= 36){
                levelSlideDown("PMA")
            }
            else{
                levelSlideUp("PMA")
            }

            levelSlideUp("PNA_nasal")
        }
        else if(PNA_value == "Intubation + ventilator"){ 
            if($("input[name='death']:checked").val() == "Alive" ||  PMA_week >= 36){
                levelSlideDown("PMA")
            }
            else{
                levelSlideUp("PMA")
            }

            levelSlideUp("PNA_nasal")
        }
        else if(PNA_value == "Non-invasive positive pressure support (CPAP, BiPAP, NIPPV, HFNC ≥ 3 LPM)"){
            if($("input[name='death']:checked").val() == "Alive" ||  PMA_week >= 36){
                levelSlideDown("PMA")
            }
            else{
                levelSlideUp("PMA")
            }

            levelSlideUp("PNA_nasal")
        }
        else if(PNA_value == "Nasal canula < 3 LPM"){
            levelSlideUp("PMA")
            levelSlideDown("PNA_nasal")
        }
        else if(PNA_value == "O2 box / O2 flow  (FiO2 ≥ 0.22)"){
            if($("input[name='death']:checked").val() == "Alive" ||  PMA_week >= 36){
                levelSlideDown("PMA")
            }
            else{
                levelSlideUp("PMA")
            }

            levelSlideUp("PNA_nasal")
        }
     });

     $("input[name='PNA_nasal']").on("change",function(){
        var PNA_day = diffDate($("input[name='death_date']").val(), birth_date_data, 'd');
        var PMA_week = parseInt(gastation.gestational_age_week) + Math.floor((parseInt(gastation.gestational_age_day) + PNA_day)/7);

        var PNA_nasal_value = $("input[name='PNA_nasal']:checked").val();
        if(PNA_nasal_value == "FiO2 = 0.21"){
            if($("input[name='death']:checked").val() == "Alive" ||  PMA_week >= 36){
                levelSlideDown("PMA")
            }
            else{
                levelSlideUp("PMA")
            }
        }
        else if(PNA_nasal_value == "FiO2 ≥ 0.22"){
             if($("input[name='death']:checked").val() == "Alive" ||  PMA_week >= 36){
                levelSlideDown("PMA")
            }
            else{
                levelSlideUp("PMA")
            }
        }
        else{
            levelSlideUp("PMA");
        }
     });

     $("input[name='PMA_diagnosis']").on("change",function(){
        var PMA_value = $("input[name='PMA_diagnosis']:checked").val();
        
        if(PMA_value == "Room air"){
            levelSlideUp("PMA_intubation")
            levelSlideUp("PMA_CPAP")
            levelSlideUp("PMA_NC_1")
            levelSlideUp("PMA_NC_2")
            levelSlideUp("PMA_O2")
        }
        else if(PMA_value == "Intubation + ventilator"){
            levelSlideDown("PMA_intubation")
            levelSlideUp("PMA_CPAP")
            levelSlideUp("PMA_NC_1")
            levelSlideUp("PMA_NC_2")
            levelSlideUp("PMA_O2")
        }
        else if(PMA_value == "CPAP / BiPAP / NIPPV/ HFNC ≥ 3 LPM"){
            levelSlideUp("PMA_intubation")
            levelSlideDown("PMA_CPAP")
            levelSlideUp("PMA_NC_1")
            levelSlideUp("PMA_NC_2")
            levelSlideUp("PMA_O2")
        }
        else if(PMA_value == "NC 1.0 - 2.99 LPM"){
            levelSlideUp("PMA_intubation")
            levelSlideUp("PMA_CPAP")
            levelSlideDown("PMA_NC_1")
            levelSlideUp("PMA_NC_2")
            levelSlideUp("PMA_O2")
        }
        else if(PMA_value == "NC ≤ 0.99 LPM"){
            levelSlideUp("PMA_intubation")
            levelSlideUp("PMA_CPAP")
            levelSlideUp("PMA_NC_1")
            levelSlideDown("PMA_NC_2")
            levelSlideUp("PMA_O2")
        }
        else if(PMA_value == "O2 hood / O2 box"){
            levelSlideUp("PMA_intubation")
            levelSlideUp("PMA_CPAP")
            levelSlideUp("PMA_NC_1")
            levelSlideUp("PMA_NC_2")
            levelSlideDown("PMA_O2")
        }
     });




    for(var i = 0 ; i < level_object.length ; i++){
        (function () {
            $("input[name='"+level_object[i].name+"']").on("change",function(){
                check_BPD_definition();
            });
        }())
    }


    //function
    function levelSlideDown(name){
        $("."+ name + "_box").slideDown(200);

        var object = level_object.filter(function(val){
            return val.name == name
        })[0]

        if(!!object && !!object.show_function){
            object.show_function();
        }
    }

    function levelSlideUp(name){
        var classname = "."+name+"_box";

        $(classname).slideUp(200); 
        $(classname + " input[type='text']").val("")
        $(classname + " input[type='date']").val("")
        $(classname + " input").prop("checked", false)

        var children = level_object.filter(function(val){
            return val.parent == name
        })

        for(var i = 0 ; i < children.length ; i++){
            levelSlideUp(children[i].name)
        }
    }

     function check_BPD_definition(){
        var old_def = "-";
        var old_def_severe = "-";
        var new_def = "-";
        var GA_week = gastation.gestational_age_week;
        
        var PNA_day;
        var PMA_week;

        var death_date = $("input[name='death_date']").val();
        if(!!death_date){
            PNA_day = diffDate(death_date, birth_date_data, 'd');
            PMA_week = parseInt(gastation.gestational_age_week) + Math.floor((parseInt(gastation.gestational_age_day) + PNA_day)/7);
        }
        
        if(!!PNA_day || PNA_day == 0 ){
            if(PNA_day <= 27){
                old_def = "Death before timing of BPD Dx"
                new_def = "Death before timing of BPD Dx"
            }

            if(PNA_day >= 14 && PNA_day <= 27){
                if($("input[name='death_reason']:checked").val() == "From persistent parenchymal lung disease+RS failure"){
                    old_def = "Death before timing of BPD Dx"
                    new_def = "III(A)"
               }    
                else if($("input[name='death_reason']:checked").val() == "From other morbidities"){
                    old_def = "Death before timing of BPD Dx"
                    new_def = "Death before timing of BPD Dx"
                }
            }
    
            if(PNA_day >= 28 && PMA_week < 36){
                if($("input[name='death_reason']:checked").val() == "From persistent parenchymal lung disease+RS failure"){
                    old_def_severe = "N/A for Severity"
                    new_def = "III(A)"
               }
                else if($("input[name='death_reason']:checked").val() == "From other morbidities"){
                    old_def_severe = "N/A for Severity"
                    new_def = "Death before timing of BPD Dx"
                }
            }
        }

        var PNA_value = $("input[name='PNA_diagnosis']:checked").val();
        if(PNA_value == "Room air"){
            old_def = "No BPD"
        }
        else if(PNA_value == "Intubation + ventilator"){
            old_def = "Yes"
        }
        else if(PNA_value == "Non-invasive positive pressure support (CPAP, BiPAP, NIPPV, HFNC ≥ 3 LPM)"){
            old_def = "Yes"
        }
        else if(PNA_value == "Nasal canula < 3 LPM"){
            var PNA_nasal_value = $("input[name='PNA_nasal']:checked").val();
            if(PNA_nasal_value == "FiO2 = 0.21"){
                old_def = "No BPD"
            }
            else if(PNA_nasal_value == "FiO2 ≥ 0.22"){
                old_def = "Yes"
            }
        }
        else if(PNA_value == "O2 box / O2 flow  (FiO2 ≥ 0.22)"){
            old_def = "Yes"
        }

        var PMA_value = $("input[name='PMA_diagnosis']:checked").val();
        if(PMA_value == "Room air"){
           old_def_severe = "Mild"
           new_def = "No BPD"
        }
        else if(PMA_value == "Intubation + ventilator"){
            var PMA_value_2 = $("input[name='PMA_intubation']:checked").val();
            if(PMA_value_2 == "FiO2 = 0.21"){
                old_def_severe = "Severe"
                new_def = "II"
            }
            else if(PMA_value_2 == "FiO2 = 0.22 - 0.29"){
                old_def_severe = "Severe"
                new_def = "III"
            }
            else if(PMA_value_2 == "FiO2 = 0.3 - 0.7"){
                old_def_severe = "Severe"
                new_def = "III"
            }
            else if(PMA_value_2 == "FiO2 ≥ 0.71"){
                old_def_severe = "Severe"
                new_def = "III"
            }
        }
        else if(PMA_value == "CPAP / BiPAP / NIPPV/ HFNC ≥ 3 LPM"){
            var PMA_value_2 = $("input[name='PMA_CPAP']:checked").val();
            if(PMA_value_2 == "FiO2 = 0.21"){
                old_def_severe = "Severe"
                new_def = "I"
            }
            else if(PMA_value_2 == "FiO2 = 0.22 - 0.29"){
                old_def_severe = "Severe"
                new_def = "II"
            }
            else if(PMA_value_2 == "FiO2 = 0.3 - 0.7"){
                old_def_severe = "Severe"
                new_def = "III"
            }
            else if(PMA_value_2 == "FiO2 ≥ 0.71"){
                old_def_severe = "Severe"
                new_def = "III"
            }
        }
        else if(PMA_value == "NC 1.0 - 2.99 LPM"){
            var PMA_value_2 = $("input[name='PMA_NC_1']:checked").val();
            if(PMA_value_2 == "FiO2 = 0.21"){
                old_def_severe = "Mild"
                new_def = "No BPD"
            }
            else if(PMA_value_2 == "FiO2 = 0.22 - 0.29"){
                old_def_severe = "Moderate"
                new_def = "I"
            }
            else if(PMA_value_2 == "FiO2 = 0.3 - 0.7"){
                old_def_severe = "Severe"
                new_def = "II"
            }
            else if(PMA_value_2 == "FiO2 ≥ 0.71"){
                old_def_severe = "Severe"
                new_def = "II"
            }
        }
        else if(PMA_value == "NC ≤ 0.99 LPM"){
            var PMA_value_2 = $("input[name='PMA_NC_2']:checked").val();
            if(PMA_value_2 == "FiO2 = 0.21"){
                old_def_severe = "Mild"
                new_def = "No BPD"
            }
            else if(PMA_value_2 == "FiO2 = 0.22 - 0.29"){
                old_def_severe = "Moderate"
                new_def = "I"
            }
            else if(PMA_value_2 == "FiO2 = 0.3 - 0.7"){
                old_def_severe = "Severe"
                new_def = "I"
            }
            else if(PMA_value_2 == "FiO2 ≥ 0.71"){
                old_def_severe = "Severe"
                new_def = "II"
            }
        }
        else if(PMA_value == "O2 hood / O2 box"){
            var PMA_value_2 = $("input[name='PMA_O2']:checked").val();
            if(PMA_value_2 == "FiO2 = 0.22 - 0.29"){
                old_def_severe = "Moderate"
                new_def = "I"
            }
            else if(PMA_value_2 == "FiO2 ≥ 0.3"){
                old_def_severe = "Severe"
                new_def = "II"
            }
        }

        if(GA_week >= 32){
            new_def = "N/A";
        }
        
        if(old_def == "Yes"){
            old_def = old_def + " (" + old_def_severe + ")"
        }
        
        $("div[name='old_definition']").text(old_def).val(old_def);
        $("div[name='new_definition']").text(new_def).val(new_def);

     }


     $("input[name='PPHN']").on("change",function(){
        // alert("test");
         if($(this).val()=="Yes"&&$(this).prop("checked")==true){
             $("#pop3").slideDown(200);
         }else if($(this).prop("checked")==true){
             $("#pop3").slideUp(200)
             $("input[name='PPHN_require_pulmonary_vasodilator']").prop("checked",false);
         }
     });
     $("input[name='PPHN_require_pulmonary_vasodilator']").on("change",function(){
        // alert("test");
        $("input[name='PPHN_require_pulmonary_vasodilator']").prop("checked",false);
        $(this).prop("checked",true);
     });

     $("input[name='PDA']").on("change",function(){
        // alert("test");
         if($(this).val()=="Yes"&&$(this).prop("checked")==true){
             $("#pop4").slideDown(200);
         }else if($(this).prop("checked")==true){
             $("#pop4").slideUp(200)
             $("input[name='PDA_require_medical_ligation']").prop("checked",false);
             $("input[name='PDA_require_surgical_ligation']").prop("checked",false);
             $("input[name='PDA_require_support_care']").prop("checked",false);
         }
     });
     $("input[name='NEC']").on("change",function(){
        // alert("test");
         if($(this).val()=="Yes"&&$(this).prop("checked")==true){
             $("#pop5").slideDown(200);
         }else if($(this).prop("checked")==true){
             $("#pop5").slideUp(200)
             $("input[name='NEC_require_surgical_treatment']").prop("checked",false);
         }
     });

     $("input[name='ROP']").on("change",function(){
        // alert("test");
         if($(this).val()=="Yes"&&$(this).prop("checked")==true){
             $("#pop6").slideDown(200);
         }else if($(this).prop("checked")==true){
             $("#pop6").slideUp(200)
             $("input[name='maximum_staging']").prop("checked",false);
             $("input[name='treatment_laser']").prop("checked",false);
             $("input[name='treatment_cryotherapy']").prop("checked",false);
             $("input[name='treatment_anti_VEGF']").prop("checked",false);
             $("input[name='treatment_no']").prop("checked",false);
         
         }
     });
     $("input[name='treatment_no']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='treatment_laser']").prop("checked",false);
            $("input[name='treatment_cryotherapy']").prop("checked",false);
            $("input[name='treatment_anti_VEGF']").prop("checked",false);
         }
     });
     $("input[name='treatment_laser']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='treatment_no']").prop("checked",false);
         }
     });
     $("input[name='treatment_cryotherapy']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='treatment_no']").prop("checked",false);
         }
     });
     $("input[name='treatment_anti_VEGF']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='treatment_no']").prop("checked",false);
         }
     });

     $("input[name='IVH']").on("change",function(){
        // alert("test");
         if($(this).val()=="Yes"&&$(this).prop("checked")==true){
             $("#pop7").slideDown(200);
         }else if($(this).prop("checked")==true){
             $("#pop7").slideUp(200)
             $("input[name='maximum_grading']").prop("checked",false);
             $("input[name='require_shunt']").prop("checked",false);
            
         
         }
     });

      $("input[name='require_shunt']").on("change",function(){
        // alert("test");
        $("input[name='require_shunt']").prop("checked",false);
        $(this).prop("checked",true);
     });

}
function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"5_9",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result.length);
            if(result.length!=0){
                push_ob_data(result[0]);
                $("div[name='old_definition']").text(result[0].old_definition)
                $("div[name='new_definition']").text(result[0].new_definition)
            }
             if(localStorage.hospital != ""){
                         //   alert("a");
                            $("input").prop("disabled",true);
                            $("textArea").prop("disabled",true);
                            $("select").prop("disabled",true);
                            $(".save-btn").prop("hidden",true);
                            $(".plus").prop("hidden",true);
                            $(".circle-red").prop("hidden",true);
            }
            
        },
      });
}
function save(){
    var temp = get_ob_data(col);
    console.log(temp);

    
     $.ajax({
         type: "POST",
         url: "https://techvernity.com/thainy/php/save_ob_section.php",
         data:{data:temp,num:"5_9"},
         success: function (data) {
             var result = JSON.parse(data);
             console.log(result);
             
             var status = progress_validate();


             $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/update_progress.php",
                data:{num:"5_9",status:status},
                success: function (data) {
                    //alert(status);
                    alert_success({text: "บันทึกสำเร็จ", 
                        button_func_1:  function(){
                            change_page("section5.html","back");
                        } 
                    });
                    
                },
              });
         },
       });
    
}

function show_box_by_change(object){
    for(var i = 0 ; i < object.length ; i++){
        (function () {
            var name = object[i].name;
            var array = object[i].value;
            var children = object.filter(function(val){
                return val.parent == name
            })
            

            $("input[name='"+ name + "']").on("change", function(){
                show_box_listener(name, array);
                for(var j = 0 ; j < children.length ; j++){
                    show_box_listener(children[j].name, children[j].value);
                }
            })  
            
        }());
    }  

    function show_box_listener(name, array){
        var index, length;

        if(!!array){
            index = array.indexOf($("input[name='"+ name + "']:checked").val());
            length = array.length;
        }
        else{
            //checkbox (couple with ..._box_1)
            index = 0;
            length = 1;
        }
        
        for(var i = 0 ; i < length ; i++){
            if(i!= index){
                var classname = "."+name+"_box_"+(i+1);
                $(classname).slideUp(200); 
                $(classname + " input[type='text']").val("");
                $(classname + " input[type='date']").val("");
                $(classname + " input").prop("checked", false);
            }   
        }
        
        var correct_classname = "."+name+"_box_"+ (index+1);

        if($("input[name='"+ name + "']:checked").prop("checked") == true){
            $(correct_classname).slideDown(200); 
        }
        else{
            $(correct_classname).slideUp(200); 
            $(correct_classname + " input[type='text']").val("");
            $(correct_classname + " input[type='date']").val("");
            $(correct_classname + " input").prop("checked", false);
        }
    }
    
}

function progress_validate(){
    var status = "not done"
        var validator = Object();
        
        for(var i = 0 ; i < col.length ; i++){
        validator[col[i]] = 0;
        }


        type1("RDS");
        type2_reverse("pulmonary_air_leak", "Yes", ["pneumothorax", "PIE", "pneumomediastinum", "other", "other_detail"])
        type3(["pneumothorax", "PIE", "pneumomediastinum", "other"])
        type2_reverse("other", null, ["other_detail"]);
        type1("other_detail");
        //BPD
        type_free(["death","death_date","death_day","death_day2","death_reason","PNA_start_date","PNA_end_date","PNA_diagnosis","PNA_nasal","PMA_start_date","PMA_end_date","PMA_diagnosis","PMA_intubation","PMA_CPAP","PMA_NC_1","PMA_NC_2","PMA_O2",,"old_definition_severe","new_definition_severe"])
        
        if($("div[name='old_definition']").text() != "-" && $("div[name='old_definition']").text() != "Yes (-)"){
            validator["old_definition"] = 1
        }
        
        if($("div[name='new_definition']").text() != "-"){
            validator["new_definition"] = 1
        }
        //

        type2_reverse("PPHN", "Yes", ["PPHN_require_pulmonary_vasodilator"])
        type1("PPHN_require_pulmonary_vasodilator");
        type2_reverse("PDA", "Yes", ["PDA_require_medical_ligation","PDA_require_surgical_ligation","PDA_require_support_care"])
        type3( ["PDA_require_medical_ligation","PDA_require_surgical_ligation","PDA_require_support_care"])
        type2_reverse("NEC", "Yes", ["NEC_require_surgical_treatment"])
        type1("NEC_require_surgical_treatment")
        type2_reverse("ROP", "Yes", ["maximum_staging","treatment_laser","treatment_cryotherapy","treatment_anti_VEGF","treatment_no"])
        type1("maximum_staging")
        type3( ["treatment_laser","treatment_cryotherapy","treatment_anti_VEGF","treatment_no"])
        type2_reverse("IVH", "Yes", ["maximum_grading","require_shunt"])
        type1("maximum_grading")
        type1("require_shunt")
        type1("PVL")


    function type1(name){
        var input = $("input[name='"+name+"']");
        var type = input.attr("type");

        if(type == "checkbox" || type == "radio"){
            if(!!$("input[name='"+name+"']:checked").val()){
                validator[name] = 1;
            }
        }
        else{
            if(!!input.val()){
                validator[name] = 1;
            }
        }
    }

    function type2(name, value, children){
        type1(name);
        
        var condition = false;
        
        var input = $("input[name='"+name+"']");
        var type = input.attr("type");

        if(!value) { 
            if(type == "checkbox"){
                condition = $("input[name='"+name+"']").prop("checked");    
            }
            else{
                condition = $("input[name='"+name+"']:checked").length > 0;
            }
        }
        else{
            condition = $("input[name='"+name+"']:checked").val() == value;
        }
        
        if(condition){
            for(var i = 0 ; i < children.length ; i++){
                validator[children[i]] = -1;
            }
        }
    }

    function type2_reverse(name, value, children){
        type1(name);
        
        var condition = false;
        
        var input = $("input[name='"+name+"']");
        var type = input.attr("type");

        if(!value) { 
            if(type == "checkbox"){
                condition = $("input[name='"+name+"']").prop("checked");    
            }
            else{
                condition = $("input[name='"+name+"']:checked").length > 0;
            }
        }
        else{
            condition = $("input[name='"+name+"']:checked").val() == value;
        } 


        if(!condition){
            for(var i = 0 ; i < children.length ; i++){
                
                validator[children[i]] = -1;
            }
        }
    }

    function type3(array){
        for(var i = 0 ; i < array.length ; i++){
            var cut_array = array.filter(function(item) { 
                return item !== array[i];
            }) 
            
            type2(array[i], null, cut_array)
        }
    }

    function type_free(array){
        for(var i = 0 ;i < array.length ; i++){
            validator[array[i]] = -1;
        }
    }

    var check = 1;
    //checker
    for(var i = 0 ; i < col.length ; i++){
        if(validator[col[i]] == 0){
            check = 0;
            break;               
        }
    }

    if(check){
        status = "done";
    }

    console.log(validator)
    return status;
}

function get_GA(success){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_gastation.php",
        success: function (data) {
            var result = JSON.parse(data);
            success(result);
        },
      });
  }