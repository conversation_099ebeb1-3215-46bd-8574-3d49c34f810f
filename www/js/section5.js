window.onload = function(){

}
///beaw//////
function length_json(value){
  var length = 0;
  for(var k in value) if(value.hasOwnProperty(k)) length++;
  return length; 
}
function goto_page(page){
  change_page("section5_"+page+".html", "next");
  //window.location.href = "section5_"+page+".html";
}
function save_1(){
  var ad_tem = document.getElementById("ad_tem").value;
  var check_value = parseFloat(ad_tem).toFixed(1);
  console.log(check_value);
  var complete = "not done";
  if(ad_tem != "" && ad_tem != undefined && ad_tem != NaN){
    complete = "done";
  }
 
  $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/update_process_section.php",
      data:{data:complete,num:"5_1"},
      success: function (data) {
        if(complete == "done"){
        if(check_value >=  25.0 && check_value <=45.0){
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_section5_1.php",
            data: {data : ad_tem},
            success: function (data) {
              alert_success({ 
                text: "บันทึกสำเร็จ", 
                header: "", 
                button_func_1: function(){
                  window.location.reload();
                }
              })
            }
          });
           
          }
          else{
          //  alert("ท่านกรอกข้อมูลผิด Admission temperature ต้องอยู่ในช่วง 28.0 ถึง 42.0");
            alert_fail({ 
              text: "ท่านกรอกข้อมูลผิด Admission temperature ต้องอยู่ในช่วง 28.0 ถึง 42.0", 
              header: "", 
              button_func_1: function(){
                document.getElementById("ad_tem").value = ""
                return;
              }
            })
            return;
          }
         
        }else{
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_section5_1.php",
            data: {data : ad_tem},
            success: function (data) {
              alert_success({ 
                text: "บันทึกสำเร็จ", 
                header: "", 
                button_func_1: function(){
                  window.location.reload();
                }
              })
              
            }
          });
          
        }
      },
    });

 

}
function init_table(name,data,lenght,type1){
  //console.log(data[0].start_date);
  if(lenght < 1){
    return;
  }
  if(lenght == 1){
    //console.log("data:"+data);
    document.getElementById("respiratory_yes").checked = true;

    document.getElementById(name+"_checkbox").checked = true;
    document.getElementById(name).hidden = false;
    document.getElementsByName(name+"_start")[0].value = data[0].start_date;
    document.getElementsByName(name+"_final")[0].value = data[0].stop_date;

    $(document.getElementsByName(name+"_start")).trigger("change")
    $(document.getElementsByName(name+"_final")).trigger("change")
  }
  if(lenght > 1){
    //console.log("data:"+data);
    document.getElementById("respiratory_yes").checked = true;

    document.getElementById(name+"_checkbox").checked = true;
    document.getElementById(name).hidden = false;
    for(var i = 0 ; i< lenght ; i++){
      if(i > 0){
        switch(name){
          case "ven" : {ven_add(); break;}
          case "lfnc" : {lfnc_add(); break;}
          case "invasive" : {invasive_add(); break;}
          default: return;
        }
      }
      document.getElementsByName(name+"_start")[i].value = data[i].start_date;
      document.getElementsByName(name+"_final")[i].value = data[i].stop_date;

      $(document.getElementsByName(name+"_start")).trigger("change")
      $(document.getElementsByName(name+"_final")).trigger("change")
    }

  }
  response1('check');
  set_contain_enable(name);
    
}
function init_table_ox(value1,lenght){
  console.log("work");
  console.log(lenght);
  if(lenght == 1 ){
    
    document.getElementsByName("day1_start")[0].value = value1[0].start_date;
    document.getElementsByName("day1_final")[0].value = value1[0].stop_date;
   // document.getElementsByName("day2_start")[0].value = value1[1].start_date;
    //document.getElementsByName("day2_final")[0].value = value1[1].stop_date;

    $(document.getElementsByName("day1_start")).trigger("change")
    $(document.getElementsByName("day1_final")).trigger("change")
 }
 if(lenght > 1 ){
     //init_table("O2_box",value1,length_json(value1),"twin");
     console.log("work");
     document.getElementById("O2_yes").checked = true;
     for(var i = 0 ; i< lenght-1; i++){
        O2_add();
      } 
      var j = 0;
      for(var k = 0;k< lenght ; k++){
        //console.log(value1[j].start_date+" "+value1[j+1].start_date);
        document.getElementsByName("day1_start")[k].value = value1[j].start_date;
        document.getElementsByName("day1_final")[k].value = value1[j].stop_date;
      //  document.getElementsByName("day2_start")[i].value = value1[j+1].start_date;
       // document.getElementsByName("day2_final")[i].value = value1[j+1].stop_date;
        j++;
      }
      
      $(document.getElementsByName("day1_start")).trigger("change")
      $(document.getElementsByName("day1_final")).trigger("change")
     
     set_contain_enable("O2");
     response2('check');
 }
}
function response1(value){
  //console.log(value);
  if(value == 'check'){
    $('#yes_content').slideDown(200);
    setTimeout(function(){
      document.getElementById("respiratory_yes").checked = true;
    document.getElementById("respiratory_no").checked = false;
   
    document.getElementById("yes_content").style.display  = "block";
    document.getElementById("ven_checkbox").disabled = false;
    document.getElementById("invasive_checkbox").disabled = false;
    document.getElementById("lfnc_checkbox").disabled = false;
    },200);
    
    
    
  
    
    //console.log("yes");
  }
  
  if(value == 'uncheck'){
    $('#yes_content').slideUp(200);
    setTimeout(function(){
      document.getElementById("respiratory_yes").checked = false;
      document.getElementById("respiratory_no").checked = true;
      document.getElementById("yes_content").style.display  = "none";
      document.getElementById("ven_checkbox").disabled = true;
      document.getElementById("invasive_checkbox").disabled = true;
      document.getElementById("lfnc_checkbox").disabled = true;
      
      document.getElementById("ven_checkbox").checked = false;
      document.getElementById("invasive_checkbox").checked = false;
      document.getElementById("lfnc_checkbox").checked = false;
  
      document.getElementById("ven").hidden = true;
      document.getElementById("invasive").hidden = true;
      document.getElementById("lfnc").hidden = true;
      set_contain_disable("ven");
      set_contain_disable("invasive");
      set_contain_disable("lfnc");
      destroy_5_2_all();
      update_day_5_2_all();
      //console.log("no");
    },200);
    
  }

}
function destroy_5_2_all(){
  
  $("#ven_contain").empty();
  $("#invasive_contain").empty();
  $("#lfnc_contain").empty();
 // ven_add();
 var text = "";
 text =   

  '<div class="inside-box-with-pad2" name = "ven_box">'+
   '<div>วันเริ่มต้น</div>'+
     '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "ven_start" ontouchend="setting_input(\'date\',this);" data-validate="l"  onchange="date_tricker(this); diff_date(\'ven_start\',\'ven_final\',\'ven_day\');">'+
     '<div class="margin-top8">วันสิ้นสุด</div>'+
     '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "ven_final" ontouchend="setting_input(\'date\',this);" data-validate="r" onchange="date_tricker(this); diff_date(\'ven_start\',\'ven_final\',\'ven_day\');">'+
     ' <div class="circle-red red-sec5" onclick="del_ven(this)"><img src="img/delete.svg"></div>'+
  '</div>';

  $("#ven_contain").append(text);
  timeline_min_max("birth_date","now");
 // invasive_add();
 text =   
 '<div class="inside-box-with-pad2" name = "invasive_box">'+
 '<div>วันเริ่มต้น</div>'+
 '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "invasive_start" ontouchend="setting_input(\'date\',this);" data-validate="l" onchange="date_tricker(this); diff_date(\'invasive_start\',\'invasive_final\',\'invasive_day\');">'+
 '<div class="margin-top8">วันสิ้นสุด</div>'+
 '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "invasive_final" ontouchend="setting_input(\'date\',this);" data-validate="r" onchange="date_tricker(this); diff_date(\'invasive_start\',\'invasive_final\',\'invasive_day\');">'+
 ' <div class="circle-red red-sec5" onclick="del_invasive(this)"><img src="img/delete.svg"></div>'+
'</div>';

$("#invasive_contain").append(text);
timeline_min_max("birth_date","now");
 // lfnc_add();
 text =   
 '<div class="inside-box-with-pad2" name = "lfnc_box">'+
 '<div>วันเริ่มต้น</div>'+
 '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "lfnc_start" ontouchend="setting_input(\'date\',this);" data-validate="l" onchange="date_tricker(this); diff_date(\'lfnc_start\',\'lfnc_final\',\'lfnc_day\');">'+
 '<div class="margin-top8">วันสิ้นสุด</div>'+
 '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "lfnc_final" ontouchend="setting_input(\'date\',this);" data-validate="r"  onchange="date_tricker(this); diff_date(\'lfnc_start\',\'lfnc_final\',\'lfnc_day\');">'+
 ' <div class="circle-red red-sec5" onclick="del_lfnc(this)"><img src="img/delete.svg"></div>'+
'</div>';

$("#lfnc_contain").append(text);
timeline_min_max("birth_date","now");

}
function destroy_5_3_all(){
  $("#O2_contain").empty();
  var text = "";
  text =   
  '<div class="inside-box-with-pad2" id = "o2_pop" >'+
  '<div>วันเริ่มต้น</div>'+
  '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "day1_start" ontouchend="setting_input(\'date\',this);" data-validate="l"  onchange="date_tricker(this); diff_date(\'day1_start\',\'day1_final\',\'o2_day\');">'+
  '<div class="margin-top8">วันสิ้นสุด</div>'+
  '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "day1_final" ontouchend="setting_input(\'date\',this);" data-validate="r"  onchange="date_tricker(this); diff_date(\'day1_start\',\'day1_final\',\'o2_day\');">'+
  '<div class="circle-red red-sec5" onclick="del_o2(this)"><img src="img/delete.svg"></div>'+
  '</div>';
 
$("#O2_contain").append(text);
timeline_min_max("birth_date","now");
}
function response2(value){
  //console.log(value);
  if(value == 'check'){
    $('#O2_box').slideDown(200);
    
    setTimeout(function(){
      document.getElementById("O2_yes").checked = true;
      document.getElementById("O2_no").checked = false;
     // document.getElementById("").hidden = false;
      set_contain_enable("O2");
    },200);
  
    
    //console.log("yes");
  }
  
  if(value == 'uncheck'){
    $('#O2_box').slideUp(200);
    setTimeout(function(){
      document.getElementById("O2_yes").checked = false;
      document.getElementById("O2_no").checked = true;
      set_contain_disable("O2");
      destroy_5_3_all();
      diff_date('day1_start','day1_final','o2_day');
    },200);
   
  
    //console.log("no");
  }
  if(value == 'none'){
    $('#O2_box').slideUp(200);
    setTimeout(function(){
      document.getElementById("O2_yes").checked = false;
      document.getElementById("O2_no").checked = false;
      set_contain_disable("O2");
      destroy_5_3_all();
      diff_date('day1_start','day1_final','o2_day');
    },200);
   
  
    //console.log("no");
  }

}
function update_day_5_2_all(){
  diff_date('ven_start','ven_final','ven_day');
  diff_date('invasive_start','invasive_final','invasive_day');
  diff_date('lfnc_start','lfnc_final','lfnc_day');
  return 0;
}
////O2//////////////////////
function O2_add(){
  //console.log(document.getElementById("O2_no").checked);
  if( document.getElementById("O2_yes").checked == false){
    return;
  }
   var text = "";
   text =   
   '<div class="inside-box-with-pad2" id = "o2_pop" name="o2_box" hidden>'+
   '<div>วันเริ่มต้น</div>'+
   '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "day1_start" ontouchend="setting_input(\'date\',this);" data-validate="l" onchange="date_tricker(this); diff_date(\'day1_start\',\'day1_final\',\'o2_day\');">'+
   '<div class="margin-top8">วันสิ้นสุด</div>'+
   '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "day1_final" ontouchend="setting_input(\'date\',this);" data-validate="r"  onchange="date_tricker(this); diff_date(\'day1_start\',\'day1_final\',\'o2_day\');">'+
   '<div class="circle-red red-sec5" onclick="del_o2(this)"><img src="img/delete.svg"></div>'+
   '</div>';
  
$("#O2_contain").append(text);
timeline_min_max("birth_date","now");
$("div[name='o2_box']").slideDown(200)

//$("#o2_pop").slideDown(200);


    // document.getElementById("O2_contain").innerHTML += text;
 
 }
 ////////////////////////////////////////////////////
 function ven_response(){
   //console.log( document.getElementById("ven_checkbox").checked);
   if(document.getElementById("ven_checkbox").checked == true){
     //console.log("t");
     set_contain_enable("ven");
   }else{
     //console.log("f");
     set_contain_disable("ven");
   }
 }
////////////////////////////
////ven/////////////////////
var ven_number = 0;
function ven_add(){
 ////console.log(document.getElementById("ven_checkbox").checked);
 if(document.getElementById("ven_checkbox").checked == false){
   return;
 }
  var text = "";
  text =   
 
   '<div class="inside-in-with-pad" name = "ven_box" id = "ven_box'+ven_number+'" hidden>'+
    '<div>วันเริ่มต้น</div>'+
      '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "ven_start" ontouchend="setting_input(\'date\',this);" data-validate="l" onchange="date_tricker(this); diff_date(\'ven_start\',\'ven_final\',\'ven_day\');">'+
      '<div class="margin-top8">วันสิ้นสุด</div>'+
      '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "ven_final" ontouchend="setting_input(\'date\',this);" data-validate="r" onchange="date_tricker(this); diff_date(\'ven_start\',\'ven_final\',\'ven_day\');">'+
      ' <div class="circle-red red-sec5" onclick="del_ven(this)"><img src="img/delete.svg"></div>'+
   '</div>';

 
   
  
    $("#ven_contain").append(text);
    timeline_min_max("birth_date","now");
    $("div[name='ven_box']").slideDown(200);

}

function ven_response(){
  //console.log( document.getElementById("ven_checkbox").checked);
  if(document.getElementById("ven_checkbox").checked == true){
    //console.log("t");
    $('#ven').slideDown(200);
    setTimeout(function(){
      set_contain_enable("ven");
    },200);
   
   
    //set_contain_enable("ven");
  }else{
    //console.log("f");
    $('#ven').slideUp(200);
    setTimeout(function(){
      set_contain_disable("ven");
    },200);
   //set_contain_disable("ven");
    
  }
}
/////////////////////////////////
///intensive////////////////////
function invasive_add(){
  ////console.log(document.getElementById("ven_checkbox").checked);
  if(document.getElementById("invasive_checkbox").checked == false){
    return;
  }
   var text = "";
   text =   
   '<div class="inside-in-with-pad" name = "invasive_box" hidden>'+
   '<div>วันเริ่มต้น</div>'+
   '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "invasive_start" ontouchend="setting_input(\'date\',this);" data-validate="l" onchange="date_tricker(this); diff_date(\'invasive_start\',\'invasive_final\',\'invasive_day\');">'+
   '<div class="margin-top8">วันสิ้นสุด</div>'+
   '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "invasive_final" ontouchend="setting_input(\'date\',this);" data-validate="r" onchange="date_tricker(this); diff_date(\'invasive_start\',\'invasive_final\',\'invasive_day\');">'+
   ' <div class="circle-red red-sec5" onclick="del_invasive(this)"><img src="img/delete.svg"></div>'+
  '</div>';
  $("#invasive_contain").append(text);
  timeline_min_max("birth_date","now");
  $("div[name='invasive_box']").slideDown(200);
    // document.getElementById("invasive_contain").innerHTML += text; 
 }
 function invasive_response(){
  //console.log( document.getElementById("invasive_checkbox").checked);
  if(document.getElementById("invasive_checkbox").checked == true){
    //console.log("t");
    $('#invasive').slideDown(200);
    setTimeout(function(){
      set_contain_enable("invasive");
    },200);
    //set_contain_enable("invasive");
  }else{
    //console.log("f");
    $('#invasive').slideUp(200);
    setTimeout(function(){
      set_contain_disable("invasive");
    },200);
    //set_contain_disable("invasive");
  }
}
////////////////////////////////
///LFNC////////////////////
function lfnc_add(){
  ////console.log(document.getElementById("ven_checkbox").checked);
  if(document.getElementById("lfnc_checkbox").checked == false){
    return;
  }
   var text = "";
   text =   
   '<div class="inside-in-with-pad" name = "lfnc_box" hidden>'+
   '<div>วันเริ่มต้น</div>'+
   '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "lfnc_start" ontouchend="setting_input(\'date\',this);" data-validate="l"  onchange="date_tricker(this); diff_date(\'lfnc_start\',\'lfnc_final\',\'lfnc_day\');">'+
   '<div class="margin-top8">วันสิ้นสุด</div>'+
   '<input class="input-section input-sec5" type="text" data-input="date"  placeholder="เลือกวัน" name = "lfnc_final" ontouchend="setting_input(\'date\',this);" data-validate="r"  onchange="date_tricker(this); diff_date(\'lfnc_start\',\'lfnc_final\',\'lfnc_day\');">'+
   ' <div class="circle-red red-sec5" onclick="del_lfnc(this)"><img src="img/delete.svg"></div>'+
  '</div>';
  $("#lfnc_contain").append(text);
  timeline_min_max("birth_date","now");
  $("div[name='lfnc_box']").slideDown(200);
     //document.getElementById("lfnc_contain").innerHTML += text; 
 }
 function lfnc_response(){
  //console.log( document.getElementById("lfnc_contain").checked);
  if(document.getElementById("lfnc_checkbox").checked == true){
    //console.log("t");
    $('#lfnc').slideDown(200);
    setTimeout(function(){
      set_contain_enable("lfnc");
    },200);
    //set_contain_enable("lfnc");
  }else{
    //console.log("f");
    $('#lfnc').slideUp(200);
    setTimeout(function(){
      set_contain_disable("lfnc");
    },200);
    //set_contain_disable("lfnc");
  }
}
////////////////////////////////
function set_contain_disable(value){
  switch(value){
    case "ven":{
     
      document.getElementById("ven").hidden = true;
      
      for(var d = 0 ;d < document.getElementsByName("ven_start").length; d++ ){
        //console.log("d:"+d);
       
   
        document.getElementsByName("ven_start")[d].disabled = true;
        document.getElementsByName("ven_final")[d].disabled = true;
       
        
        

      }
      break;
    }
    case "invasive" :{
      document.getElementById("invasive").hidden = true;
   
      for(var d = 0 ;d < document.getElementsByName("invasive_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("invasive_start")[d].disabled = true;
        document.getElementsByName("invasive_final")[d].disabled = true;

      }
      break;
    }
    case "lfnc" :{
      document.getElementById("lfnc").hidden = true;
   
      for(var d = 0 ;d < document.getElementsByName("lfnc_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("lfnc_start")[d].disabled = true;
        document.getElementsByName("lfnc_final")[d].disabled = true;

      }
      break;
    }
    case "O2" :{
      document.getElementById("O2_box").hidden = true;
      for(var d = 0 ;d < document.getElementsByName("day1_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("day1_start")[d].disabled = true;
        document.getElementsByName("day1_final")[d].disabled = true;
     //   document.getElementsByName("day2_start")[d].disabled = true;
      //  document.getElementsByName("day2_final")[d].disabled = true;
      }
      break;
    }
    default : return;
  }
}
function set_contain_enable(value){
  switch(value){
    case "ven":{
      document.getElementById("ven").hidden = false;

      for(var d = 0 ;d < document.getElementsByName("ven_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("ven_start")[d].disabled = false;
        document.getElementsByName("ven_final")[d].disabled = false;

      }
      break;
    }
    case "invasive" :{
      document.getElementById("invasive").hidden = false;
   
      for(var d = 0 ;d < document.getElementsByName("invasive_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("invasive_start")[d].disabled = false;
        document.getElementsByName("invasive_final")[d].disabled = false;

      }
      break;
    }
    case "lfnc" :{
      document.getElementById("lfnc").hidden = false;
      for(var d = 0 ;d < document.getElementsByName("lfnc_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("lfnc_start")[d].disabled = false;
        document.getElementsByName("lfnc_final")[d].disabled = false;

      }
      break;
    }
    case "O2" :{
      for(var d = 0 ;d < document.getElementsByName("day1_start").length; d++ ){
        //console.log("d:"+d);
        document.getElementsByName("day1_start")[d].disabled = false;
        document.getElementsByName("day1_final")[d].disabled = false;
      //  document.getElementsByName("day2_start")[d].disabled = false;
      //  document.getElementsByName("day2_final")[d].disabled = false;
      }
      break;
    }
    default : return;
  }
}

function save_section5_2(){
  console.log(document.getElementById("respiratory_no").checked);
  var complete = "not done";
  if(document.getElementById("respiratory_no").checked){
    complete = "done";
    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/update_process_section.php",
      data:{data:complete,num:"5_2"},
      success: function (data) {
        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/save_section5_2_none.php",
          data: {},
          success: function (data) {
            alert_success({ 
              text: "บันทึกสำเร็จ", 
              header: "", 
              button_func_1: function(){
                window.location.reload();
              }
            })
          }
        });
      }
    });
    //console.log(document.getElementById("respiratory_no").checked);
    return;
    /*
    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/save_section5_2_none.php",
      data: {},
      success: function (data) {
          alert("บันทึกสำเร็จ");
          window.location.href = "section5.html";
      }
    });
    return;
    */
  }
  //console.log(document.getElementsByName("ven_start").length);
  //console.log(document.getElementsByName("ven_final").length);
  var V = "";
  var I = "";
  var L = "";
  var v_pass = true;
  var i_pass = true;
  var l_pass = true;
  var all_pass = false;
  var num_v = "No";
  var num_io = "No";
  var num_l = "NO";
 if(( document.getElementById("ven_checkbox").checked||
  document.getElementById("invasive_checkbox").checked ||
  document.getElementById("lfnc_checkbox").checked ) && document.getElementById("respiratory_yes").checked){
    all_pass = true;
  }

  for(var i = 0 ;i < document.getElementsByName("ven_start").length; i++ ){
    //console.log("i:"+i);
    if(document.getElementById("ven_checkbox").checked == false){
    //  v_pass = false;
      break;
      
    }
    if(document.getElementsByName("ven_start")[i].value == "" || document.getElementsByName("ven_start")[i].value == undefined){
    //  alert("ท่านกรอกข้อมูลไม่ครับ ven");
      v_pass = false;
     // return;
     
    }
    if(document.getElementsByName("ven_final")[i].value == "" || document.getElementsByName("ven_final")[i].value == undefined){
    //  alert("ท่านกรอกข้อมูลไม่ครับ ven");
      v_pass = false;
     // return;
      
    }
    V += document.getElementsByName("ven_start")[i].value + ":"+document.getElementsByName("ven_final")[i].value+";";
    //console.log(V);
  //  v_pass = true;
      num_v = "Yes";
   
  }
  for(var i = 0 ;i < document.getElementsByName("invasive_start").length; i++ ){
    //console.log("i:"+i);
    if(document.getElementById("invasive_checkbox").checked == false){
  //    i_pass = false;
      break;
    }
    if(document.getElementsByName("invasive_start")[i].value == "" || document.getElementsByName("invasive_start")[i].value == undefined){
     // alert("ท่านกรอกข้อมูลไม่ครับ invasive");
      i_pass = false;
     // return;
    }
    if(document.getElementsByName("invasive_final")[i].value == "" || document.getElementsByName("invasive_final")[i].value == undefined){
    //  alert("ท่านกรอกข้อมูลไม่ครับ invasive");
      i_pass = false;
     // return;
    }
    I += document.getElementsByName("invasive_start")[i].value + ":"+document.getElementsByName("invasive_final")[i].value+";";
    //console.log(I);
   // i_pass = true;
   num_io = "Yes";
  }

  for(var i = 0 ;i < document.getElementsByName("lfnc_start").length; i++ ){
    //console.log("i:"+i);
    if(document.getElementById("lfnc_checkbox").checked == false){
    //  l_pass = false;
      break;
    }
    if(document.getElementsByName("lfnc_start")[i].value == "" || document.getElementsByName("lfnc_start")[i].value == undefined){
    //  alert("ท่านกรอกข้อมูลไม่ครับ lfnc");
      l_pass = false;
     // return;
    }
    if(document.getElementsByName("lfnc_final")[i].value == "" || document.getElementsByName("lfnc_final")[i].value == undefined){
     // alert("ท่านกรอกข้อมูลไม่ครับ lfnc");
      l_pass = false;
     // return;
    }
    L += document.getElementsByName("lfnc_start")[i].value + ":"+document.getElementsByName("lfnc_final")[i].value+";";
    //console.log(L);
   // l_pass = true;
   
   num_l = "Yes";
  }
  //console.log(v_pass+" "+i_pass+" "+l_pass+" "+all_pass);
  if(v_pass && i_pass && l_pass &&all_pass){
    complete = "done";
      ///V
      //console.log(V+":hello:"+v);
    /*
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_section5_2.php",
        data: {data1 : V ,data2 : I , data3 : L , num_v:num_v , num_io:num_io , num_l:num_l},
        success: function (data) {
            alert("บันทึกสำเร็จ");
            window.location.href = "section5.html";
        }
      });
      */
      
  }else{
    complete = "not done";
   // alert("ท่านกรอกข้อมูลไม่ครับ");
   // return;
  }
  /*
  var v = V.split(":");
  var io = I.split(":");
  var l = L.split(":");
  
  var num_v = v.length;
  var num_io = io.length;
  var num_l = l.length;
  */
  V = V.substring(0, V.length - 1);
  I = I.substring(0, I.length - 1);
  L = L.substring(0, L.length - 1);
console.log(V+"////////"+I+"////////"+L);
console.log(complete);
console.log(num_v+num_io+num_l);
$.ajax({
  type: "POST",
  url: "https://techvernity.com/thainy/php/update_process_section.php",
  data:{data:complete,num:"5_2"},
  success: function (data) {
    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/save_section5_2.php",
      data: {data1 : V ,data2 : I , data3 : L , num_v:num_v , num_io:num_io , num_l:num_l},
      success: function (data) {
        alert_success({ 
          text: "บันทึกสำเร็จ", 
          header: "", 
          button_func_1: function(){
            window.location.reload();
          }
        })
      }
    });
  }
});
}
function save_section5_3(){
  var O = "";
  var num_O = "No";
  var o_pass = true;
  var complete = "not done";
  for(var i = 0 ;i < document.getElementsByName("day1_start").length; i++ ){
    //console.log("i:"+i);
    if(document.getElementById("O2_yes").checked == false){
      break;
    }
    if(document.getElementsByName("day1_start")[i].value == "" || document.getElementsByName("day1_start")[i].value == undefined){
    //alert("ท่านกรอกข้อมูลไม่ครับ O21");
     // return;
     o_pass = false;
    }
   
    if(document.getElementsByName("day1_final")[i].value == "" || document.getElementsByName("day1_final")[i].value == undefined){
      //alert("ท่านกรอกข้อมูลไม่ครับ O22");
      //return;
      o_pass = false;
    }
  
    O += document.getElementsByName("day1_start")[i].value + ":"+document.getElementsByName("day1_final")[i].value+";";
   
    num_O = "yes";
   }
   //console.log(O);
   console.log(o_pass);
   if(o_pass){
     complete = "done";
   }
  O = O.substring(0, O.length - 1);

  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/update_process_section.php",
    data:{data:complete,num:"5_3"},
    success: function (data) {
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_section5_3.php",
        data: {data1 : O,num_O : num_O},
        success: function (data) {
          alert_success({ 
            text: "บันทึกสำเร็จ", 
            header: "", 
            button_func_1: function(){
             // window.location.reload();
            }
          })
        }
      });
    }
  });
  /*
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/save_section5_3.php",
    data: {data1 : O},
    success: function (data) {
        alert("บันทึกสำเร็จ");
        window.location.href = "section5.html";
    }
  });
  */

}

function del_ven(ob){
  $(ob.parentElement).slideUp(200);
  setTimeout(function(){
    document.getElementById("ven_contain").removeChild(ob.parentElement);
    diff_date('ven_start','ven_final','ven_day');
  },200);

}

function del_invasive(ob){
  $(ob.parentElement).slideUp(200);
  setTimeout(function(){
    document.getElementById("invasive_contain").removeChild(ob.parentElement);
    diff_date('invasive_start','invasive_final','invasive_day');
  },200);

}

function del_lfnc(ob){
  $(ob.parentElement).slideUp(200);
  setTimeout(function(){
    document.getElementById("lfnc_contain").removeChild(ob.parentElement);
    diff_date('lfnc_start','lfnc_final','lfnc_day');
  },200);

}
function del_o2(ob){
 
  $(ob.parentElement).slideUp(200);
  setTimeout(function(){
    document.getElementById("O2_contain").removeChild(ob.parentElement);
    diff_date('day1_start','day1_final','o2_day');
    
  },200);
}

function diff_date(name1,name2,tagvalue){

  var lenght1 = document.getElementsByName(name1).length;
  var lenght2 = document.getElementsByName(name2).length;
  var value = 0;
for(var i = 0 ; i< lenght1 ;i++){
  console.log(diffDate(document.getElementsByName(name2)[i].value, document.getElementsByName(name1)[i].value, 'd'));
  value += parseInt(diffDate(document.getElementsByName(name2)[i].value, document.getElementsByName(name1)[i].value, 'd'));
}
if(isNaN(value))value = 0;
text = "";
text += " "+value+" day(s)";
document.getElementById(tagvalue).innerHTML = text;
console.log(value);

}

function isdone(section,returner){
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_progress.php",
    success: function (data) {
        var result = JSON.parse(data);
        console.log(result);
        if(result[0]["section5_"+section] == "done"){
          returner(true);
        }
        else{
          returner(false);
        }
    },
});

}
///////////
    setting();
    load();
    


function setting(){

}

function load(){

 

  
}

function save(){




}

