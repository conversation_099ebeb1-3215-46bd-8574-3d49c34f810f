$(document).ready(function(){
  $("#registration").on("click",function(){
    change_page("registration_2.html", "next");
    //window.location = "registration_2.html";
  })
  
    $("#section1").on("click",function(){
      change_page("section1.html", "next");
      //window.location = "section1.html";
    })
    
    $("#section2").on("click",function(){
      change_page("section2.html", "next");
      //window.location = "section2.html";
    })
  
    $("#section3").on("click",function(){
      change_page("section3.html", "next");
      //window.location = "section3.html";
    })
  
    $("#section4").on("click",function(){
      change_page("section4.html", "next");
      //window.location = "section4.html";
    })
  
    $("#section5").on("click",function(){
      change_page("section5.html", "next");
      //window.location = "section5.html";
    })
  
    $("#section6").on("click",function(){
      change_page("section6.html", "next");
      //window.location = "section6.html";
    })
  })
  
  window.onload = function(){
    if(localStorage.hospital==""){
      change_page("patientlist.html", "next");
      //window.location = "patientlist.html";
    }
    load();
    //alert("test");
  }
  function load(){
      
      
      // $.ajax({
      //     type: "POST",
      //     url: "https://techvernity.com/thainy/php/get_patient_display.php",
      //     data:{hospital:localStorage.hospital},
      //     success: function (data) {
      //         var result = JSON.parse(data);
      //         console.log(result);
      //         document.getElementById("name").innerHTML = result["fullname"];
      //         document.getElementById("TNR").innerHTML = "TN# : "+result["TNR"];
      //         document.getElementById("hospital").innerHTML = "โรงพยาบาล : "+localStorage.hospital;
      //         document.getElementById("time").innerHTML = "วัน/เวลา refer : "+localStorage.time;    
      //         var num = 0;
      //       if(result["section1"]){
      //         document.getElementById("section1x").parentElement.style.cssText += "background-color: #7FC5C6;";
      //         num++;
      //       }
      //       if(result["section2"]&&result["section2_GBS"]&&result["section2_abnormal_serology"]&&result["section2_complication_during_pregnancy"]&&result["section2_intrapartum_complication"]&&result["section2_meternal_medication"]){
      //         document.getElementById("section2x").parentElement.style.cssText += "background-color: #7FC5C6;";
      //         num++;
      //       }
      //       if(result["section3"]){
      //         document.getElementById("section3x").parentElement.style.cssText += "background-color: #7FC5C6;";
      //         num++;
      //       }
      //       if(result["section4"]){
      //         document.getElementById("section4x").parentElement.style.cssText += "background-color: #7FC5C6;";
      //         num++;
      //       }
      //       if(result["section5_1"]&&result["section5_2"]&&result["section5_2_LFNC"]&&result["section5_2_non_invasive"]&&result["section5_2_ventilator"]
      //       &&result["section5_3"]
      //       &&result["section5_4"]
      //       &&result["section5_5"]
      //       &&result["section5_6"]
      //       &&result["section5_7"]
      //       &&result["section5_8"]
      //       &&result["section5_9"]
      //       &&result["section5_10"]&&result["section5_10_PICC_line"]&&result["section5_10_UAC"]&&result["section5_10_UVC"]&&result["section5_10_other_central_line"]
      //       &&result["section5_11"]
      //       &&result["section5_12"]){
      //         document.getElementById("section5x").parentElement.style.cssText += "background-color: #7FC5C6;";
      //         num++;
      //       }
      //       if(result["section6"]){
      //         document.getElementById("section6x").parentElement.style.cssText += "background-color: #7FC5C6;";
      //         num++;
      //       }
      //       var  per = (100/6)*num;
      //       document.getElementById("percent").innerHTML = per.toFixed(2)+"%";
      //       document.getElementById("progress_bar").style.cssText += "width: " + per.toFixed(2)+"%";
      //     },  
  
      //   });
    
       $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/get_patient_display.php",
          data:{hospital:localStorage.hospital},
          success: function (data) {
              var result = JSON.parse(data);
              console.log(result);
              document.getElementById("name").innerHTML = result["fullname"];
              document.getElementById("TNR").innerHTML = "TN# : "+result["TNR"];
              document.getElementById("hospital").innerHTML = "โรงพยาบาล : "+localStorage.hospital;
              document.getElementById("time").innerHTML = "วัน/เวลา refer : "+localStorage.time;    
         
        },  

      });

      
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_progress.php",
        data:{hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            var num = 0;
            if(result[0]["section1"]=="done"){
              document.getElementById("section1x").parentElement.style.cssText += "background-color: #7FC5C6;";
              num++;
           }
            if(result[0]["section2"]=="done"){
               document.getElementById("section2x").parentElement.style.cssText += "background-color: #7FC5C6;";
               num++;  
            }
            if(result[0]["section3"]=="done"){
              document.getElementById("section3x").parentElement.style.cssText += "background-color: #7FC5C6;";
              num++;
            }
           if(result[0]["section4"]=="done"){
            document.getElementById("section4x").parentElement.style.cssText += "background-color: #7FC5C6;";
            num++; 
          }
           if(result[0]["section5_1"]=="done"&&
           result[0]["section5_2"]=="done"&&
           result[0]["section5_3"]=="done"&&
           result[0]["section5_4"]=="done"&&
           result[0]["section5_5"]=="done"&&
           result[0]["section5_6"]=="done"&&
           result[0]["section5_7"]=="done"&&
           result[0]["section5_8"]=="done"&&
           result[0]["section5_9"]=="done"&&
           result[0]["section5_10"]=="done"&&
           result[0]["section5_11"]=="done"&&
           result[0]["section5_12"]=="done"){
            document.getElementById("section5x").parentElement.style.cssText += "background-color: #7FC5C6;";
            num++; 
          }
          if(result[0]["section6"]=="done"){
            document.getElementById("section6x").parentElement.style.cssText += "background-color: #7FC5C6;";
            num++;
          }
           
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/get_registation_progress.php",
            success: function (data) {
                var result = JSON.parse(data);
                 console.log(result[0]["status"]);
                 if(result[0]["status"] == "done"){
                  document.getElementById("register").parentElement.style.cssText += "background-color: #7FC5C6;";
                  num++; 
                }
               // alert(data);
               var  per = (100/7)*num;
              //  if(num==7){
              //    document.getElementById("submit").hidden = false;
              //  }
               document.getElementById("percent").innerHTML = per.toFixed(2)+"%";
               document.getElementById("progress_bar").style.cssText += "width: " + per.toFixed(2)+"%";
            
            },
          });
          
        },
    });
  
      
  }
  function reject(){
    confirm_o({
      text: "ต้องการปฎิเสธการ Refer หรือไม่",
      header: "ปฎิเสธการ Refer",
      button_text_1: "ตกลง",
      button_text_2: "ไม่ตกลง",
      button_func_1: function(){
        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/reject_refer.php",
          data:{hospital:localStorage.hospital},
          success: function (data) {
              // var result = JSON.parse(data);
              // console.log(result);
              change_page("referlist.html", "back");
            //  window.location = "referlist.html";
          },
        });
      }
    })
  }

  function accept(){
    confirm_o({
      text: "ต้องการยอมรับ Refer หรือไม่",
      header: "ยอมรับการ Refer",
      button_text_1: "ตกลง",
      button_text_2: "ไม่ตกลง",
      button_func_1: function(){
        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/refer_duplicate.php",
          data:{hospital:localStorage.hospital},
          success: function (data) {
              // var result = JSON.parse(data);
              // console.log(result);
              change_page("patientlist.html", "next");
            //  window.location = "referlist.html";
          },
        });
      }
    })
  }