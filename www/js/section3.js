var col = ["delivery_mode","vaginal","intication","1_min_score","1_min_NA","5_min_score","5_min_NA","10_min_score","10_min_NA","resuscitation","CPAP","PPV","intubation","chest_compression","epinephrine","cord_blood","cord_blood_pH","cord_blood_pCO2","cord_blood_HCO2","cord_blood_BE","delayed_cord_clamping"];
window.onload= function(){
    setting();
    load();

}
function setting(){
     //info
    $("#info1").click(function(){ 
        show_info("การช่วยกู้ชีพหลังเกิด ที่ advance กว่า initial steps") 
    })
    $("#info2").click(function(){ 
        show_info("ให้กรอกเป็นทศนิยม 2 ตำแหน่ง ถ้าค่าต่ำมากจนวัดไม่ได้ ให้กรอกโดยใช้เครื่องหมาย “<” เช่น < 6.00") 
    })
    $("#info3").click(function(){ 
        show_info("Delayed cord clamping = การชะลอการหนีบสายสะดือหลังเกิดอย่างน้อย 30-60 วินาที<br/>Cord milking = การรีดสายสะดือเพื่อเพิ่มเลือดเข้าสู่ทารกหลังเกิดทันที") 
    })

    floatx("1_min_score",0);
    floatx("5_min_score",0);
    floatx("10_min_score",0);

   
    floatx("cord_blood_pCO2",1);
    floatx("cord_blood_HCO2",1);
    floatx("cord_blood_BE",1);
    $("input[name='1_min_score']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
      $("input[name='5_min_score']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
      $("input[name='10_min_score']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
    $("input[name='cord_blood_pH']").on("change",function(){
        if(parseFloat($(this).val())<5.50||parseFloat($(this).val())>8.00){
            alert_fail({text : "กรุณากรอกข้อมูลในช่วง 5.50 - 8.00",
                button_func_1 :function(){
                    $("input[name='cord_blood_pH']").val("");
            }});
        }else{
           // alert("test");
           
            var val = $(this).val();
            var less = false;
            if(val.match("<")){
                //alert("test");
                less = true;
                val = val.split("<")[1];
            }

            val = parseFloat(val);
            val = val.toFixed(2);
            if(less){
                val = "<"+val;
            }
            $(this).val(val);
        }
    });
    $("input[name='cord_blood_pH']").on("keydown",function(e){
    //    alert(e.key);
            if(e.key!="Backspace"&&e.key!="Shift"&&e.key!="<"&&e.key!="."&&e.key!=" "&&!e.key.toString().match("[0-9]")){
                $(this).val($(this).val().toString().substring(0,$(this).val().length-1));
            }   
    });
   $("input[name='1_min_score']").on("change",function(){
       if(parseInt($(this).val())<0||parseInt($(this).val())>10){
            alert_fail({text : "กรุณากรอกข้อมูลในช่วง 0-10",
                        button_func_1 :function(){
                            $("input[name='1_min_score']").val("");
                        }});
       }
   });
   $("input[name='5_min_score']").on("change",function(){
    if(parseInt($(this).val())<0||parseInt($(this).val())>10){
         alert_fail({text : "กรุณากรอกข้อมูลในช่วง 0-10",
                     button_func_1 :function(){
                         $("input[name='5_min_score']").val("");
                     }});
    }
});
$("input[name='10_min_score']").on("change",function(){
    if(parseInt($(this).val())<0||parseInt($(this).val())>10){
         alert_fail({text : "กรุณากรอกข้อมูลในช่วง 0-10",
                     button_func_1 :function(){
                         $("input[name='10_min_score']").val("");
                     }});
    }
});
   $("input[name='delivery_mode']").on("change",function(){
      // alert("test");
        if($(this).val()=="Vaginal"&&$(this).prop("checked")==true){
            $("#pop1").slideDown(200);
            $("#pop_new").slideUp(200);
            $("input[name='intication']").val("");
        }else if($(this).val()=="Caesarean section"&&$(this).prop("checked")==true){
            $("#pop1").slideUp(200);
            $("#pop_new").slideDown(200);
            $("input[name='vaginal']").prop("checked",false);
        }
   });
   $("input[name='1_min_NA']").on("change",function(){
    // alert("test");
    if($(this).prop("checked")==true){
        $("input[name='1_min_score']").val("");
        $("input[name='1_min_score']").prop("disabled",true);
    }else{
        $("input[name='1_min_score']").prop("disabled",false);
    }
  });
  
  $("input[name='5_min_NA']").on("change",function(){
    // alert("test");
    if($(this).prop("checked")==true){
        $("input[name='5_min_score']").val("");
        $("input[name='5_min_score']").prop("disabled",true);
    }else{
        $("input[name='5_min_score']").prop("disabled",false);
    }
  });

  $("input[name='10_min_NA']").on("change",function(){
    // alert("test");
    if($(this).prop("checked")==true){
        $("input[name='10_min_score']").val("");
        $("input[name='10_min_score']").prop("disabled",true);
    }else{
        $("input[name='10_min_score']").prop("disabled",false);
       
    }
  });

  $("input[name='resuscitation']").on("change",function(){
    // alert("test");
    if($(this).val()=="Yes"&&$(this).prop("checked")==true){
        $("#pop2").slideDown(200);
    }else if($(this).prop("checked")==true){
        $("#pop2").slideUp(200);
        $("input[name='CPAP']").prop("checked",false);
        $("input[name='PPV']").prop("checked",false);
        $("input[name='intubation']").prop("checked",false);
        $("input[name='chest_compression']").prop("checked",false);
        $("input[name='epinephrine']").prop("checked",false);
    }
  });

  $("input[name='cord_blood']").on("change",function(){
    // alert("test");
    if($(this).val()=="Available"&&$(this).prop("checked")==true){
        $("#pop3").slideDown(200);
    }else if($(this).prop("checked")==true){
        $("#pop3").slideUp(200);
        $("input[name='cord_blood_pH']").val("");
        $("input[name='cord_blood_pCO2']").val("");
        $("input[name='cord_blood_HCO2']").val("");
        $("input[name='cord_blood_BE']").val("");
      //  $("input[name='delayed_cord_clamping']").val("");
    }
  });
  if(localStorage.hospital != ""){
    //alert(localStorage.hospital);
    $("input").prop("disabled",true);
    $("textArea").prop("disabled",true);
    $("select").prop("disabled",true);
    $(".save-btn").prop("hidden",true);
    $(".plus").prop("hidden",true);
}


}
function load(callback){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"3",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            var status = "done";
            if(result.length!=0){
                console.log(result);
                push_ob_data(result[0]);
                
            
                if(result[0]["delivery_mode"]==""||result[0]["resuscitation"]==""||result[0]["cord_blood"]==""||result[0]["delayed_cord_clamping"]==""){
                    status = "not done";
                    //alert(status);
                }
                if(result[0]["delivery_mode"]=="Vaginal"&&result[0]["vaginal"]==""){
                    status = "not done";
                  //  alert(status);
                }
                if(result[0]["1_min_score"]==""&&result[0]["1_min_NA"]==""){
                    status = "not done";
                }
                if(result[0]["5_min_score"]==""&&result[0]["5   _min_NA"]==""){
                    status = "not done";
                }
                if(result[0]["10_min_score"]==""&&result[0]["10_min_NA"]==""){
                    status = "not done";
                }
                if(result[0]["resuscitation"]=="Yes"){
                    if(result[0]["CPAP"]==""&&result[0]["PPV"]==""&&result[0]["intubation"]==""&&result[0]["chest_compression"]==""&&result[0]["epinephrine"]==""){
                        status = "not done";
                    }
                }
                if(result[0]["cord_blood"]=="Available"){
                    if(result[0]["cord_blood_pH"]==""){
                        status = "not done";
                    }
                }
               // alert(status);
            }else{
                status = "not done";
            }
            console.log(status);
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/update_progress.php",
                data:{num:"3",status:status},
                success: function (data) {
                    //alert(status);
                    if(!!callback){
                        callback();
                    }
                },
              });
        },
      });
}
function save(){
    var temp = get_ob_data(col);
    console.log(temp);
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_ob_section.php",
        data:{data:temp,num:"3"},
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            load(function(){
                alert_success({
                    text:"บันทึกสำเร็จ",
                    button_func_1 : function(){
                        change_page("patient_display.html","back");
                    }
                });
            });
          
          
        },
      });
}