var col = ["never","feeding","start_date","age"];
window.onload = function(){
    //alert("test");
    get_birth_date(function(){
        timeline_min_max("birth_date","now");
      });
    setting();
    load();
    
}
function setting(){
    //info
    $("#info1").click(function(){ 
        show_info("วันแรกของชีวิตที่เริ่มให้นม <br/>*รวม trophic feeding <br/>*แต่ไม่รวม oral/mouth care") 
    })

    
   $("input[name='start_date']").on("change",function(){
       if($(this).val()!=""&&!!birth_date_data){
            $("input[name='age']").val("Age " +diffDate(birth_date_data,$(this).val(),"d")+" day(s)");
       }else{
        $("input[name='age']").val("");
       }
   });
    $("input[name='feeding']").on("change",function(){
        if($(this).prop("checked")==true&&$(this).val()=="Yes"){
           // $("input[name='never']").prop("checked",false);
            $("input[name='start_date']").prop("disabled",false);
           // $("input[name='age']").prop("disabled",false);
        }else if($(this).prop("checked")==true&&$(this).val()=="No"){
           // $("input[name='feeding']").prop("checked",false);
            $("input[name='start_date']").prop("disabled",true);
            $("input[name='age']").prop("disabled",true);
            $("input[name='start_date']").val("").trigger("change");
            $("input[name='age']").val("");
        }
    
    });
}
function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"5_7",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result.length);
            var status = "done";
            if(result.length!=0){
                push_ob_data(result[0]);
                var temp = result[0];
                if(temp["feeding"]==""){
                    status = "not done";
                }

                if(temp["feeding"]=="Yes"&&(temp["start_date"]==""||temp["age"]=="")){
                    status = "not done";
                }
            }
            else{
                status = "not done";
            }
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/update_progress.php",
                data:{num:"5_7",status:status},
                success: function (data) {
                    //alert(status);
                },
              });
            if(localStorage.hospital != ""){
                //alert(localStorage.hospital);
                $("input").prop("disabled",true);
                $("textArea").prop("disabled",true);
                $("select").prop("disabled",true);
                $(".save-btn").prop("hidden",true);
                $(".plus").prop("hidden",true);
            }
        },
      });
}
function save(){
    var temp = get_ob_data(col);
    console.log(temp);
    var complete = "not done";
    //console.log(document.getElementsByName("ino1")[0].checked);
    // if(document.getElementsByName("never")[0].checked){
    //   complete = "done";
    // }else if(document.getElementsByName("feeding")[0].checked){
    //     if(document.getElementsByName("start_date")[0].value != "" && document.getElementsByName("age")[0].value != ""  ){
    //         complete = "done";
    //     }
    // }
    // console.log(complete);

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/update_process_section.php",
        data:{data:complete,num:"5_7"},
        success: function (data) {
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/save_ob_section.php",
                data:{data:temp,num:"5_7"},
                success: function (data) {
                    var result = JSON.parse(data);
                    load();
                    alert_success({
                        text:"บันทึกสำเร็จ",
                        button_func_1 : function(){
                            change_page("section5.html","back");
                        }
                    });
                },
              });
        }
      });

      
 }
 