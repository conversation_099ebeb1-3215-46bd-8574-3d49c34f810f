$(document).ready(function(){
  $("#registration").on("click",function(){
    change_page("registration_2.html", "next");
    //window.location = "registration_2.html";
  })

  $("#section1").on("click",function(){
    change_page("section1.html", "next");
   // window.location = "section1.html";
  })
  
  $("#section2").on("click",function(){
    change_page("section2.html", "next");
    //window.location = "section2.html";
  })

  $("#section3").on("click",function(){
    change_page("section3.html", "next");
    //window.location = "section3.html";
  })

  $("#section4").on("click",function(){
    change_page("section4.html", "next");
    //window.location = "section4.html";
  })

  $("#section5").on("click",function(){
    change_page("section5.html", "next");
    //window.location = "section5.html";
  })

  $("#section6").on("click",function(){
    change_page("section6.html", "next");
    //window.location = "section6.html";
  })
})

window.onload = function(){
  /////
  if(localStorage.hospital != ""){
    change_page("patient_display_refer.html", "next");
    //window.location = "patient_display_refer.html";
  }
  load();
  //alert("test");
}
function load(){
      
  // $.ajax({
  //   type: "POST",
  //   url: "https://techvernity.com/thainy/php/check_session.php",
  //   success: function (data) {
  //     alert(data);
  //   },  

  // });
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_patient_display.php",
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            document.getElementById("name").innerHTML = result["fullname"];
            document.getElementById("TNR").innerHTML = "TN# : "+result["TNR"];
            document.getElementById("HN").innerHTML = "HN : "+result["HN"];
            document.getElementById("mom").innerHTML = "มารดา : "+result["mother_fullname"];
           
         
        },  

      });

      
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_progress.php",
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            var num = 0;
            if(result[0]["section1"]=="done"){
              document.getElementById("section1x").parentElement.style.cssText += "background-color: #7FC5C6;";
              num++;
           }
            if(result[0]["section2"]=="done"){
               document.getElementById("section2x").parentElement.style.cssText += "background-color: #7FC5C6;";
               num++;  
            }
            if(result[0]["section3"]=="done"){
              document.getElementById("section3x").parentElement.style.cssText += "background-color: #7FC5C6;";
              num++;
            }
           if(result[0]["section4"]=="done"){
            document.getElementById("section4x").parentElement.style.cssText += "background-color: #7FC5C6;";
            num++; 
          }
           if(result[0]["section5_1"]=="done"&&
           result[0]["section5_2"]=="done"&&
           result[0]["section5_3"]=="done"&&
           result[0]["section5_4"]=="done"&&
           result[0]["section5_5"]=="done"&&
           result[0]["section5_6"]=="done"&&
           result[0]["section5_7"]=="done"&&
           result[0]["section5_8"]=="done"&&
           result[0]["section5_9"]=="done"&&
           result[0]["section5_10"]=="done"&&
           result[0]["section5_11"]=="done"&&
           result[0]["section5_12"]=="done"){
            document.getElementById("section5x").parentElement.style.cssText += "background-color: #7FC5C6;";
            num++; 
          }
          if(result[0]["section6"]=="done"){
            document.getElementById("section6x").parentElement.style.cssText += "background-color: #7FC5C6;";
            num++;
          }
           
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/get_registation_progress.php",
            success: function (data) {
                var result = JSON.parse(data);
                 console.log(result[0]["status"]);
                 if(result[0]["status"] == "done"){
                  document.getElementById("register").parentElement.style.cssText += "background-color: #7FC5C6;";
                  num++; 
                }
               // alert(data);
               var  per = (100/7)*num;
              
               document.getElementById("percent").innerHTML = per.toFixed(2)+"%";
               document.getElementById("progress_bar").style.cssText += "width: " + per.toFixed(2)+"%";
               if(localStorage.hospital == ""){
                    $.ajax({
                      type: "POST",
                      url: "https://techvernity.com/thainy/php/check_refer.php",
                      success: function (data) {
                          // var result = JSON.parse(data);
                          // console.log(result);
                        // alert(data);
                          if(data==0){
                            document.getElementById("refer").hidden = false;
                            if(num==7){
                              document.getElementById("submit").hidden = false;
                            }
                          }else{
                            document.getElementById("cancel").hidden = false;
                            $("#registration").unbind("click");
                            $("#section1").unbind("click");
                            $("#section2").unbind("click");
                            $("#section3").unbind("click");
                            $("#section4").unbind("click");
                            $("#section5").unbind("click");
                            $("#section6").unbind("click");
                            $("img[src='img/next3.svg']").prop("hidden",true);
                            document.getElementById("submit").hidden = true;
                          }
                      },
                    });
                }else{
                  //alert("referer");
                    document.getElementById("cancel").hidden = false;
                    document.getElementById("refer").hidden = false;
                    
                    ////////////// กรณี รอรับ refer
                }
              },
          });
          
        },
    });

    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/set_refer_to_refer2.php",
      success: function (data) {
     
      }  
    });
     
}
function cancel(){
  confirm_o({
    text: "ยืนยันการยกเลิกการย้ายผู้ป่วย?",
    header: "ยืนยัน",
    button_text_1: "ตกลง",
    button_text_2: "ยกเลิก",
    button_func_1: function(){
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/cancel_refer.php",
        success: function (data) {
            // var result = JSON.parse(data);
            // console.log(result);
            window.location.reload();
        },
      });
   
    }
    
  });
  // if(confirm("Are you sure?")){
  //   $.ajax({
  //     type: "POST",
  //     url: "https://techvernity.com/thainy/php/cancel_refer.php",
  //     success: function (data) {
  //         // var result = JSON.parse(data);
  //         // console.log(result);
  //         window.location.reload();
  //     },
  //   });
  // }
}
function submit(){
  confirm_o({
    text: "ยืนยันการ submit ข้อมูล (ข้อมูลที่ submit แล้วจะไม่สามารถแก้ไขได้)",
    header: "ยืนยัน",
    button_text_1: "ตกลง",
    button_text_2: "ยกเลิก",
    button_func_1: function(){
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/submit.php",
      
        success: function (data) {
            alert_success({text:"submit สำเร็จ",button_func_1 : function(){
              change_page("patientlist.html", "next");
             // window.location = "patientlist.html";
            }});
         
        },
      });
   
    }
    
  });
  // if(confirm("ยืนยันการ submit ข้อมูล (ข้อมูลที่ submit แล้วจะไม่สามารถแก้ไขได้)")){
  //   $.ajax({
  //     type: "POST",
  //     url: "https://techvernity.com/thainy/php/submit.php",
    
  //     success: function (data) {
  //         alert("submit สำเร็จ");
  //         window.location = "patientlist.html";
  //     },
  //   });
  // }
}