var patient_list;

window.onload = function(){
    localStorage.hospital = "";
    // alert("test");
    var status = "active";
    load(status);
    check_refer();

    $("#add").on("click",function(){
        window.location = "registry_criteria1.html";
    })

    
    $("#refer").on("click",function(){
        window.location = "web_referlist.html";
    })

    $("#active").on("click",function(){
        status = "active";
        load(status);
        document.getElementById("active").className = "web-choose";
        document.getElementById("inactive").className = "";
        $("#active img").attr("src", "img/stargreen.svg");
        $("#inactive img").attr("src", "img/stargray.svg");

        $("#active .footer-text").addClass("text-green")
        $("#active .footer-text").removeClass("text-gray")

        $("#inactive .footer-text").addClass("text-gray")
        $("#inactive .footer-text").removeClass("text-green") 
    })

    $("#inactive").on("click",function(){
        status = "inactive";
        document.getElementById("active").className = "";
        document.getElementById("inactive").className = "web-choose";
        load(status);

        $("#active img").attr("src", "img/stargray.svg");
        $("#inactive img").attr("src", "img/stargreen.svg");

        $("#active .footer-text").removeClass("text-green")
        $("#active .footer-text").addClass("text-gray")

        $("#inactive .footer-text").removeClass("text-gray")
        $("#inactive .footer-text").addClass("text-green") 
    })

    $("#search").on("keyup", function(){
        console.log("Hello");
        var value = $(this).val();
        var result = patient_list.filter(function(element) {
            return element.fullname.indexOf(value) != -1 ||
                    element.TNR.indexOf(value) != -1;
          });

          $("#p_list").empty();
          for(var i = 0 ; i < result.length; i++){
            add(result[i].fullname, result[i].TNR);
        }
    });
   
    $("#sort").on("click",function(){
        $("#popup-sort").show();
    })

    $("#filter-cancel").on("click",function(){
        $("#popup-sort").hide();
    })
    $("#filter-accept").on("click",function(){
        $("#popup-sort").hide();

        var type = ""
        var sort_by = "";

        if($("#filter-name").is(":checked")) type = "fullname"
        if($("#filter-TN").is(":checked")) type = "TNR"
        if($("#filter-dateAdmit").is(":checked")) type = "created_date"
        
        if($("#sort-min").is(":checked")) sort_by = "ASC"
        if($("#sort-max").is(":checked")) sort_by = "DESC"

        sort(type, sort_by, status);
    })

    //$("#filter-name").attr('checked')
    
}



function load(status){
    document.getElementById("p_list").innerHTML = "";

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_patient_list.php",
        data:{status:status},
        success: function (data) {
            var result = JSON.parse(data);
            patient_list = result;
            console.log(result);

            for(var i = 0 ; i < result.length; i++){
                add(result[i].fullname, result[i].TNR);
            }
           // document.getElementById("active_count").innerHTML += "จำนวนผู้ป่วย Active "+result.length+" คน"; 
        },
      });
}
function add(fullname,TNR){
    var text = '<div class="patient_display each-list">'
                           +'<div class="name-patient">'+fullname+'</div>'
                           +'<div class="tn-patient">TN#: '+TNR+'</div>'
                           +'<div class="save-btn">กำลังบันทึก</div>'
                           +'<img src="img/next2.svg">'
                       +'</div>';

    document.getElementById("p_list").innerHTML += text;

    $(".patient_display").on("click",function(){
        window.location = "web_patient_display.html"
    })
}

function sort(type, sort_by, status){
    document.getElementById("p_list").innerHTML = "";

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_patient_sort.php",
        data:{status:status, type:type, sort_by:sort_by},
        success: function (data) {
            var result = JSON.parse(data);
            patient_list = result;
            console.log(result);

            for(var i = 0 ; i < result.length; i++){
                add(result[i].fullname, result[i].TNR);
            }
        },
      });
}

function check_refer(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_refer_list.php",
        data:{},
        success: function (data) {
            var result = JSON.parse(data);
            var refer_count = result.length;
           
            if(refer_count > 0){
                $("#refer_count").text(refer_count);
                $("#refer").show();
            }
        },
      });
}


   

