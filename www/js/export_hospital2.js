function export_hospita(){
    var s1 = document.getElementById("section1").checked;
    var s2 = document.getElementById("section2").checked; 
    var s3 = document.getElementById("section3").checked;
    var s4 = document.getElementById("section4").checked;
    var s5 = document.getElementById("section5").checked;
    var s6 = document.getElementById("section6").checked;
    if(s1 || s2 || s3 || s4 || s5 || s6){
       console.log("check");
    }else{
        alert_fail({
            text:"โปรดเลือก Section ที่ต้องการ",
            button_func_1: function(){
            return;
            }
          });
          return;
    }


   
    var TNR = "20190100025";
    var  dataarray = [];
    dataarray.push(TNR);
    dataarray.push(s1.toString());
    dataarray.push(s2.toString());
    dataarray.push(s3.toString());
    dataarray.push(s4.toString());
    dataarray.push(s5.toString());
    dataarray.push(s6.toString());
    var sender = JSON.stringify(dataarray);
    console.log(sender);

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/export_hospital_patient_check.php",
        data: {
          TNR : TNR,
          mydata : sender
        },
        success: function (data) {
  
            //console.log("http://techvernity.com/thainy/php/export_hospital_patient.php?"+data);
            //window.open('http://techvernity.com/thainy/php/export_hospital_patient.php?'+data);
            //window.location.href = 'http://techvernity.com/thainy/php/export_hospital_patient.php?'+data;
            cordova.InAppBrowser.open('http://techvernity.com/thainy/php/export_hospital_patient.php?'+data, '_blank', "enableViewportScale=yes,location=no,hidenavigationbuttons=yes,closebuttoncaption=ปิด");
        },
      });
     
}
function click_section(){
    var s1 = document.getElementById("section1").checked;
    var s2 = document.getElementById("section2").checked; 
    var s3 = document.getElementById("section3").checked;
    var s4 = document.getElementById("section4").checked;
    var s5 = document.getElementById("section5").checked;
    var s6 = document.getElementById("section6").checked;
   
    if(s1 && s2 && s3 && s4 && s5 && s6){
        document.getElementById("all_section").checked = true;
    }else{
        document.getElementById("all_section").checked = false;
    }
}
function click_all(){

    var sa = document.getElementById("all_section").checked;
    if(sa){
        document.getElementById("section1").checked = true;
        document.getElementById("section2").checked = true;
        document.getElementById("section3").checked = true;
        document.getElementById("section4").checked = true;
        document.getElementById("section5").checked = true;
        document.getElementById("section6").checked = true;
    }else{
        document.getElementById("section1").checked = false;
        document.getElementById("section2").checked = false;
        document.getElementById("section3").checked = false;
        document.getElementById("section4").checked = false;
        document.getElementById("section5").checked = false;
        document.getElementById("section6").checked = false;
    }
}