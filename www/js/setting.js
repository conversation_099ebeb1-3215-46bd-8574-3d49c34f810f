$(document).ready(function(){

    document.addEventListener("deviceready", checkBioAuthen, false);

    if(!!localStorage.BioAuthenStatus){
        if(localStorage.BioAuthenStatus=="on"){
            $('#switch4').animate(
                {
                    marginLeft: "15px"
    
                }, {
                    duration: 150,
                    specialEasing: {
                        bottom: "easeOutQuart"
                    }
                });
            document.getElementById("switch3").style.backgroundColor = '#33d574';
            localStorage.BioAuthenStatus = "on";
        }
    }else {
        localStorage.BioAuthenStatus = "off";
    }

  $("#change_pin").on("click",function(){
      //old passcode
      get_pin_page_change();
      $("#pin_back").show();
      $("#pin_back").click(function(){
          pin_page_hide();
      })
  })
  
  $("#setting-changepassword").on("click",function(){
    change_page("change_password1.html", "next");
    //window.location = "change_password1.html";
  })
  
});



