window.onload = function(){
  setting();
  load();
}
function setting(){
  // ---------------------5_4---------------------
  $("input[name='ino1']").on("change",function(){});
}
function load(){
  //--------------5_4-----------------
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section5_4.php",
    data:{hospital:localStorage.hospital,num:"5_4"},
    success: function (data) {
        var result1 = JSON.parse(data);
        console.log(result1);
        if(result1.length!=0){
            push_all_section_data("ino",result1[0]);
        }
        if(localStorage.hospital != ""){
          //alert(localStorage.hospital);
          $("input").prop("disabled",true);
          $("textArea").prop("disabled",true);
          $("select").prop("disabled",true);
          $(".save-btn").prop("hidden",true);
          $(".plus").prop("hidden",true);
      }
      }
  });
}

function save(){
  //--------------5_4-----------------
  var temp = get_all_data("ino");
  console.log(temp);
  var complete = "not done";
  //console.log(document.getElementsByName("ino1")[0].checked);
  if(document.getElementsByName("ino1")[0].checked || document.getElementsByName("ino1")[1].checked){
    complete = "done";
  }

  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/update_process_section.php",
    data:{data:complete,num:"5_4"},
    success: function (data) {
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_section5_4.php",
        data:{data:JSON.stringify(temp),num:"5_4"},
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            alert_success({text: "บันทีกสำเร็จ", 
              button_func_1: function(){ 
                change_page("section5.html","back");
              } 
            });
        }
      });
    }
  });
}