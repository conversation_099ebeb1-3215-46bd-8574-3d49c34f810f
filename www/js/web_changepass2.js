$(document).ready(function(){
    var regex = /[A-Za-z0-9!@#$%^&*_+.]/g;
  
    $(".save-web-btn").on("click",function(){
      var input_pass= $("#input-register").val();
      var submit_pass= $("#input-register2").val();

      if(input_pass.match(regex).length == input_pass.length  && input_pass.length>=8){
        localStorage.master_input_pass=input_pass;
        if(localStorage.master_input_pass==submit_pass){

          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/change_password.php",
            data: {
                password: input_pass
            },
            success: function (data) {
                var result = JSON.parse(data);
                if(result.status == 1){

                  alert_success({
                      text: "เปลี่ยนรหัสผ่านสำเร็จ", 
                      button_func_1: function(){
                        window.location = "home.html"
                      }
                  })

                }
                else{
                  alert_fail({text: "เกิดข้อผิดพลาดในระบบ", header: "ไม่ถูกต้อง"}) 
                }
            },
          });
          
        }
        else{
          alert_fail({text:"รหัสผ่านของคุณไม่ตรงกัน"})
        }
      }
      else{
        alert_fail({text:"รหัสผ่านไม่ตรงกับรูปแบบที่กำหนด"})
      }
    })
})
// 