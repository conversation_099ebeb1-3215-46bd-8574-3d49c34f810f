var date_of_birth = "";
var g = "";
var a = "";
window.onload = function(){
    //alert("test");
    setting();
    load();
}
function setting(){
    timeline_min_max(null,"now")

    $("input[name='i8']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
      $("input[name='i9']").on("touchstart",function(){
        //alert($(this).prop("disabled"));
        if($(this).prop("disabled")==true&&!localStorage.hospital){
          alert_fail({
            text : "กรุณาเลือก N/A ออกก่อน",
          }); 
        }
      });
    //info
    $("#info1").click(function(){ 
        show_info("ให้ใช้อายุครรภ์ที่น่าเชื่อถือมากที่สุดตามวิจารณญาณของผู้ประเมิน") 
    })
    $("#info2").click(function(){ 
        show_info("<img id='fenton' style='width: 100%;' src='img/fenton_boy.jpg'>"
        +"<span id='fenton_boy_text' style='text-decoration:underline; font-weight:600;'>Boy</span> / " 
        +"<span id='fenton_girl_text'>Girl</span>") 

        $("#fenton_boy_text").click(function(){
            $("#fenton").attr("src", "img/fenton_boy.jpg")
            $(this).css({
                "text-decoration":"underline",
                "font-weight":600
            })
            $("#fenton_girl_text").removeAttr("style")
        })
        $("#fenton_girl_text").click(function(){
            $("#fenton").attr("src", "img/fenton_girl.jpg")
            $(this).css({
                "text-decoration":"underline",
                "font-weight":600
            })
            $("#fenton_boy_text").removeAttr("style")
        })
        
        $("#popup_container").css("padding", "5%");
        $("#popup_box").css("width", "100%");
        $("#popup_box").css("left", "0");
        setTimeout(function(){
            $("#popup_box").css("top", $("#popup_container").height()/2 - $("#popup_box").outerHeight()/2);
        },20)
    })

    floatx("i7",0);
    floatx("i8",1);
    floatx("i9",1);
    $("input[name='i5']").on("change",function(){
        if(parseInt($(this).val())<20||parseInt($(this).val())>45){
            alert_fail({text : "กรุณากรอกข้อมูลในช่วง 20-45",
            button_func_1 :function(){
                $("input[name='i5']").val("");
        }});
        }
    });
    $("input[name='len_na']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='i8']").val("");
            $("input[name='i8']").prop("disabled",true);
        }else{
            $("input[name='i8']").prop("disabled",false);
        }
    });
    $("input[name='cir_na']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='i9']").val("");
            $("input[name='i9']").prop("disabled",true);
        }else{
            $("input[name='i9']").prop("disabled",false);
        }
    });
    $("input[name='i6']").on("change",function(){
        if(parseInt($(this).val())<0||parseInt($(this).val())>6){
            alert_fail({text : "กรุณากรอกข้อมูลในช่วง 0-6",
            button_func_1 :function(){
                $("input[name='i6']").val("");
        }});
        }
    });
    $("input[name='i3']").on("change",function(){
        if($(this).val()=="Higher_order"){
            $("input[name='Higher_order_etc']").prop("disabled",false);
            
        }else{
            $("input[name='Higher_order_etc']").prop("disabled",true);
            $("input[name='Higher_order_etc']").val("");
        }
    });
}
function load(callback){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_section1.php",
        data:{num:"4",hospital:localStorage.hospital},
        success: function (data) {
            var result2 = JSON.parse(data);
            console.log(result2);
            if(result2.length!=0){
                var tempx = result2[0];
               // var status = check_status(tempx,1);
               var status = "done"; 
               if(tempx["DOB"]==""||tempx["TOB"]==""||tempx["gender"]==""||tempx["gestational_age_week"]==""||tempx["gestational_age_day"]==""||tempx["birth_weight"]==""||tempx["growth_status"]==""){
                    status = "not done";
                }

                if(tempx["length"]==""&&tempx["length_NA"]==""){
                    status = "not done";
                }
                if(tempx["head_circumference"]==""&&tempx["head_circumference_NA"]==""){
                    status = "not done";
                }

                console.log(status);
                $.ajax({
                    type: "POST",
                    url: "https://techvernity.com/thainy/php/update_progress.php",
                    data:{num:"4",status:status},
                    success: function (data) {
                        //alert(status);
                        if(!!callback){
                            callback();
                        }
                    },
                  });
                date_of_birth = result2[0]["DOB"];
                g = result2[0]["gestational_age_week"];
                a =  result2[0]["gestational_age_day"];
                var len_na  = result2[0]["length_NA"];
                var cir_na  = result2[0]["head_circumference_NA"];
                result2[0] = ob_to_array(result2[0]);
                console.log(result2[0]);
                array_insert(result2[0],4,"");
                array_cut(result2[0],12);
                array_cut(result2[0],10);
                push_all_section_data("i",result2[0]);
                if(len_na!=""){
                    $("input[name='len_na']").prop("checked",true);
                    $("input[name='len_na']").trigger("change");
                }
                if(cir_na!=""){
                    $("input[name='cir_na']").prop("checked",true);
                    $("input[name='len_na']").trigger("change");
                }
            }else{
                $.ajax({
                    type: "POST",
                    url: "https://techvernity.com/thainy/php/update_progress.php",
                    data:{num:"4",status:"not done"},
                    success: function (data) {
                        //alert(status);
                        if(!!callback){
                            callback();
                        }
                    },
                  });
            }
            if(localStorage.hospital != ""){
                //    alert("a");
                    $("input").prop("disabled",true);
                    $("textArea").prop("disabled",true);
                    $("select").prop("disabled",true);
                    $(".save-btn").prop("hidden",true);
            }
        },
      });
}
function save(){
    var temp = get_all_data("i");
    var check_len = "";
    var check_cir = "";
    if($("input[name='len_na']").prop("checked")==true){
        check_len = "N/A";
    }
    if($("input[name='cir_na']").prop("checked")==true){
        check_cir = "N/A";
    }

    console.log(temp);
    if(date_of_birth!=temp[0]||g!=temp[4]||a!=temp[5]){
        confirm_o({text:"การเปลี่ยนแปลงข้อมูลวันเกิดหรือ GA จะส่งผลต่อข้อมูลอื่นๆในระบบ", header:"ยืนยันการบันทึกข้อมูล", button_text_1: "ยืนยัน", button_text_2: "ไม่ยืนยัน", button_func_1: function(){
        
    
        var temp = get_all_data("i");
     //   console.log(temp);
        array_cut(temp,2);
        array_insert(temp,8,[check_cir]);
        array_insert(temp,7,[check_len]);
        console.log(temp);
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_section1.php",
            data:{data:JSON.stringify(temp),num:"4"},
            success: function (data) {
                //var result = JSON.parse(data);
                console.log(data);
                alert_success({
                    text:"บันทึกสำเร็จ",
                    button_func_1 : function(){
                        change_page("patient_display.html","back");
                    }
                });
            },
        });
        }});
    }else{
        var temp = get_all_data("i");
       // console.log(temp);
        array_cut(temp,2);
        array_insert(temp,8,[check_cir]);
        array_insert(temp,7,[check_len]);
        console.log(temp);
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_section1.php",
            data:{data:JSON.stringify(temp),num:"4"},
            success: function (data) {
                //var result = JSON.parse(data);
                console.log(data);
                load(function(){
                    alert_success({
                        text:"บันทึกสำเร็จ",
                        button_func_1 : function(){
                            change_page("patient_display.html","back");
                        }
                    });
                });
                
            },
        });
    }
}