
window.onload = function(){
  setting();
  load();
}
function setting(){
  $("input[name='ino1']").on("change",function(){});
  // ---------------------web_5_4---------------------
  $("#back-section5-btn").on("click",function(){
    window.location="web_section5.html";
  });
  //list_bar
  $("#patient_list").on("click",function(){
    window.location="web_patientlist.html";
  });
  $("#track_tn").on("click",function(){
    window.location="web_search_patient.html";
  });
  $("#export").on("click",function(){
    window.location="web_export_data.html";
  });
  $("#help").on("click",function(){
    window.location="web_help_and_support1.html";
  });
  $("#approve").on("click",function(){
    window.location="web_authorize.html";
  });
  $("#setting").on("click",function(){
    window.location="setting.html";
  });
  $("#logout").on("click",function(){
    window.location = "login.html";
  });

  $("input[name='a5_1']").on("change",function(){});
  $("input[name='a5_2']").on("change",function(){});

  $("input[name='a5_3']").on("change",function(){
    $("#pop1").prop("disabled",true);
    if($("input[name='a5_3']:checked").val()=="Yes"){
      $("#pop1").slideDown(200);
    }else if($("input[name='a5_3']:checked").val()=="No"){
      $("#pop1").slideUp(200);
      $("input[name='a5_4").val("");
      $("input[name='a5_5").val("");
      $("input[name='a5_6").val("");
      $("input[name='a5_7").val("");
    }
  });
  $("input[name='select-date']").on("change",function(){

  });

  $("input[name='a5_8']").on("change",function(){
    $("#pop2").prop("disabled",true);
    if($("input[name='a5_8']:checked").val()=="Yes"){
      $("#pop2").slideUp(200);
      $("input[name='a5_9").val("");
      $("input[name='a5_10").val("");
    }else if($("input[name='a5_8']:checked").val()=="No"){
      $("#pop2").slideDown(200);
    }
  });

  
    $("input[name='a5_18']").on("change",function(){
      if($(this).prop("checked")==true){
        for(var i = 11 ;i<18;i++){
          $("input[name='a5_"+i+"']").prop("checked",false);
        }
      }
    });
    for(var i = 11;i<18;i++){
      $("input[name='a5_"+i+"']").on("change",function(){
        $("input[name='a5_18']").prop("checked",false);
      });
    }
    $("input[name='Other_Seizure_etc']").prop("disabled",true);
    $("input[name='a5_17']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='Other_Seizure_etc']").prop("disabled",false);
        $("input[name='a5_18']").prop("checked",false);
      }else if($(this).prop("checked")==false){
        $("input[name='Other_Seizure_etc']").prop("disabled",true);
        $("input[name='Other_aEEG_etc").val("");
      }
    });
    

    for(var i = 20;i<25;i++){
      $("input[name='a5_"+i+"']").on("change",function(){
        $("input[name='Other_aEEG_etc']").prop("disabled",false);
      });
    }

    $("input[name='a5_20']").on("change",function(){
      $("#pop3").prop("disabled",true);
      $("input[name='Other_aEEG_etc']").prop("disabled",true);
      if($("input[name='a5_20']:checked").val()=="Yes"){
        $("#pop3").slideDown(200);
        $("input[name='a5_25']").on("change",function(){
          if($(this).prop("checked")==false){
            $("input[name='Other_aEEG_etc']").prop("disabled",true);
            $("input[name='Other_aEEG_etc").val("");
            // $("input[name='e6']").prop("checked",false);
          }
          else if($(this).prop("checked")==true){
            $("input[name='Other_aEEG_etc']").prop("disabled",false);
          }
        });
      }else if($("input[name='a5_20']:checked").val()=="No"){
        $("#pop3").slideUp(200);
        for(var i = 21;i<26;i++){
          $("input[name='a5_"+i+"']").prop("checked",false);
        }
        $("input[name='Other_aEEG_etc").val("");
        $("input[name='Other_aEEG_etc").prop("disabled",true);
      }
    });
}
function load(){
  //--------------5_12-----------------
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section5_12.php",
    data:{num:"5_12"},
    success: function (data) {
        var result3 = JSON.parse(data);
        console.log(result3);
        if(result3.length!=0){
          
            push_all_section_data("a5_",result3[0]);
        }
      }
    });
}

function save(){
  //--------------5_12-----------------
  var temp2 = get_all_data("a5_");
  console.log(temp2);
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/save_section5_12.php",
    data:{data:JSON.stringify(temp2),num:"5_12"},
    success: function (data) {
        var result3 = JSON.parse(data);
        console.log(result3);
        alert_success({text: "บันทีกสำเร็จ", button_func_1: function(){ window.location.reload(); } });
    }
  })
}