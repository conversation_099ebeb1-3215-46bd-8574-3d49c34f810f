var col = ["surfactant_use"];
window.onload = function(){
 
    load();
    
}
function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"5_5",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result.length);
            if(result.length!=0){
                push_ob_data(result[0]);
            }
            if(localStorage.hospital != ""){
                //alert(localStorage.hospital);
                $("input").prop("disabled",true);
                $("textArea").prop("disabled",true);
                $("select").prop("disabled",true);
                $(".save-btn").prop("hidden",true);
                $(".plus").prop("hidden",true);
            }
        },
      });
}
function save(){
   var temp = get_ob_data(col);
   console.log(temp);
   var complete = "not done";
  //console.log(document.getElementsByName("ino1")[0].checked);
  if(document.getElementsByName("surfactant_use")[0].checked || document.getElementsByName("surfactant_use")[1].checked){
    complete = "done";
  }
   $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/update_process_section.php",
    data:{data:complete,num:"5_5"},
    success: function (data) {
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_ob_section.php",
            data:{data:temp,num:"5_5"},
            success: function (data) {
                var result = JSON.parse(data);
                console.log(result);
                alert_success({
                    text:"บันทึกสำเร็จ",
                    button_func_1 : function(){
                        change_page("section5.html","back");
                    }
                });
            },
          });
    },
  });
  
}
