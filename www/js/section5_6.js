window.onload = function(){
  get_birth_date(function(){
    timeline_min_max("birth_date","now");
  });
  setting();
  load();
}
function setting(){
  
  // ---------------------5_6---------------------
  var number = $(".add-dynamic")[0].children.length + 1; 
  
  $("input[name='TPN1_1']").on("change",function(){
    cal_day();
  });
  $("input[name='TPN1_2']").on("change",function(){
    cal_day();
  });
  $("#plus-btn").click(function(){
    number = $(".add-dynamic")[0].children.length + 1;
   // $(".add-dynamic").append('<div class="inside-box-with-pad2" name="TPN'+number+'"><div>วันเริ่มต้น</div><input class="input-section" type="date" data-input="date"  name="TPN'+number+'_1" placeholder="เลือกวัน" ><div class="margin-top8">วันสิ้นสุด</div> <input class="input-section" type="date" data-input="date"  name="TPN'+number+'_2" placeholder="เลือกวัน" ></div>');
   $(".add-dynamic").append('<div name="TPN'+number+'" class="inside-box-with-pad2" hidden>'
   +'<div class="">วันเริ่มต้น</div>'
   +'<input class="input-section input-sec5" name="TPN'+number +'_1" type="text" data-input="date" data-validate="l" placeholder="เลือกวัน" ontouchend="setting_input(\'date\',this);">'
   +'<div class="margin-top8  ">วันสิ้นสุด</div>'
   +'<input class="input-section input-sec5" name="TPN'+number +'_2" type="text" data-input="date" data-validate="r" placeholder="เลือกวัน" ontouchend="setting_input(\'date\',this);">'
   +'<div class="circle-red red-sec5" onclick="del(this)"><img src="img/delete.svg"></div>'                                  
   +'</div>'); 
   $("div[name='TPN"+number+"']").slideDown(200);
   
   timeline_min_max("birth_date","now");

   $("input[name='TPN"+number+"_1']").on("change",function(){
      date_tricker($(this))
      cal_day();
    });
    $("input[name='TPN"+number+"_2']").on("change",function(){
      date_tricker($(this))
      cal_day();
    });

    });
  $("input[name='pn1']").on("change",function(){ 
    $("#days").html(0);
    if($("input[name='pn1']:checked").val()=="Yes"){
     
     
        // $("input[name='TPN1_1']").prop("disabled",false);
        // $("input[name='TPN1_2']").prop("disabled",false);
        $(".input-section" ).prop("disabled",false);
        $(".input-section").val("");
        $("#pop1").slideDown(200);
        $("#plus-btn").trigger("click")
       // $(".add-dynamic").append('<div class="inside-box-with-pad2" name="TPN'+number+'"><div>วันเริ่มต้น</div><input class="input-section" type="date" data-input="date"  name="TPN'+number+'_1" placeholder="เลือกวัน" ><div class="margin-top8">วันสิ้นสุด</div> <input class="input-section" type="date" data-input="date"  name="TPN'+number+'_2" placeholder="เลือกวัน" ></div>');
        
      console.log(1)
    }else if($("input[name='pn1']:checked").val() == "No"){
        // $("input[name='TPN1_1']").prop("disabled",true);
        $(".input-section" ).prop("disabled",true);
        $(".input-section").val("");
        $(".add-dynamic").html("");
        $("#pop1").slideUp(200);
        // $("input[name='TPN1_1']").val("");

      console.log(2)
    }
  });

  $(".section5_6-btn").on("click",function(){
    var temp1 = dimensionx("TPN");
    console.log(temp1);
  })
}
function cal_day(){
  var day = 0;
  for(var i = 1;document.getElementsByName("TPN"+i)[0]!=undefined;i++){
    var day1 = $("input[name='TPN"+i+"_1']").val();
    var day2 = $("input[name='TPN"+i+"_2']").val();
    if(day1!=""&&day2!=""){
      day += parseInt(diffDate(day1,day2,"d")); 
    }
  }
  $("#days").html(day);
}
function load(callback){
  //--------------5_6------------------
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section5_6.php",
    data:{num:"5_6",hospital:localStorage.hospital},
    success: function (data) {
        var result2 = JSON.parse(data);
        console.log(result2);
        if(result2.length!=0){
          $(".add-dynamic").empty()
          
            $("input[value='Yes']").prop("checked",true);
            $("input[value='Yes']").trigger("change");
            for(var i = 0 ; i < result2.length ; i++){
                var number = $(".add-dynamic")[0].children.length + 1; 
                if(i > 0)
             $("#plus-btn").click();

              push_all_section_data("TPN"+(i+1)+"_",result2[i]);
              cal_day()
            }
           
        } else{
          $("input[value='No']").prop("checked",true);
          $("input[value='No']").trigger("change");
        }
        if(localStorage.hospital != ""){
          //alert(localStorage.hospital);
          $("input").prop("disabled",true);
          $("textArea").prop("disabled",true);
          $("select").prop("disabled",true);
          $(".save-btn").prop("hidden",true);
          $(".plus").prop("hidden",true);
      }
      var status = "done";
      for(var i = 0;i<result2.length;i++){
          if(result2[i]["start_date"]==""||result2[i]["stop_date"]==""){
            status = "not done";
          }
      }
      //alert(status);

      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/update_progress.php",
        data:{num:"5_6",status:status},
        success: function (data) {
            //alert(status);
            if(!!callback){
              callback();
            }
        },
      });
      }
  });
}
function del(ob){
    $(ob.parentElement).slideUp(200);
    //alert( ob.parentElement.className);
    setTimeout(function(){
      document.getElementsByClassName("add-dynamic")[0].removeChild(ob.parentElement);
      var temp = document.getElementsByClassName("add-dynamic")[0].children;
      for(var i  = 0;i<temp.length;i++){
        $(temp[i]).attr("name","TPN"+(i+1));
        temp[i].children[1].name = "TPN"+(i+1)+"_1";
        temp[i].children[3].name = "TPN"+(i+1)+"_2";
      
      }
      cal_day();
    },200);

}
function save(){
  //--------------5_6-----------------
  var temp1 = dimensionx("TPN");
  console.log(temp1);
  var array = {
    TPN_day : $("#days").html(),
  };
  console.log(array);
  ajax("save_ob_section.php",{data:array,num:"5_6_days"},function(){
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_section5_6.php",
        data:{data:JSON.stringify(temp1),num:"5_6"},
        success: function (data) {
          //  var result2 = JSON.parse(data);
            //console.log(result2);
            load(function(){
              alert_success({text: "บันทีกสำเร็จ", button_func_1: function(){ 
                
                change_page("section5.html","back");
              } 
            });     
            });
           
        }
      })
    });
}