window.onload = function(){
  setting();
  load();
}
function setting(){
  // ---------------------5_6---------------------
  $(".input-section" ).prop("disabled",true);
  var number = $(".add-dynamic")[0].children.length + 1; 

  $("input[name='pn1']").on("change",function(){
    if($("input[name='pn1']:checked").val()=="Yes"){
      $("#plus-btn").click(function(){
        $(".add-dynamic").append('<div class="inside-gray1 margin-top8 clearfix" name="TPN'+number+'"><div class="width50 margin-right60"><span class="section2-span4"><div class="margin-right8">วันเริ่มต้น</div><input class="input-section" name="TPN'+number+'_1" type="date" data-input="date"  placeholder="เลือกวัน"></span></div><div class="width50"><span class="section2-span4"><div class=" margin-right8">วันสิ้นสุด</div><input class="input-section" name="TPN'+number+'_2" type="date" data-input="date"  placeholder="เลือกวัน"></span></div></div>');
      });
      $(".input-section" ).prop("disabled",false);
        
      console.log(1)
    }else if($("input[name='pn1']:checked").val() == "No"){
        // $("input[name='TPN1_1']").prop("disabled",true);
        $(".input-section" ).prop("disabled",true);
        $(".input-section").val("");
        // $("input[name='TPN1_1']").val("");

      console.log(2)
    }
  });

  $(".section5_6-btn").on("click",function(){
    var temp1 = dimensionx("TPN");
    console.log(temp1);
  })
}
function load(){
  //--------------5_6------------------
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section5_6.php",
    data:{num:"5_6"},
    success: function (data) {
        var result2 = JSON.parse(data);
        console.log(result2);
        if(result2.length!=0){
            $("input[value='Yes']").prop("checked",true)
            for(var i = 0 ; i < result2.length ; i++){
                var number = $(".add-dynamic")[0].children.length + 1; 
                if(i > 0)
              $(".add-dynamic").append('<div class="inside-box-with-pad2" name="TPN'+number+'"><div>วันเริ่มต้น</div><input class="input-section" type="date" data-input="date"  name="TPN'+number+'_1" placeholder="เลือกวัน" ><div class="margin-top8">วันสิ้นสุด</div> <input class="input-section" type="date" data-input="date"  name="TPN'+number+'_2" placeholder="เลือกวัน" ></div>');

              push_all_section_data("TPN"+(i+1)+"_",result2[i]);
            }
        } 
        if(localStorage.hospital != ""){
          //alert(localStorage.hospital);
          $("input").prop("disabled",true);
          $("textArea").prop("disabled",true);
          $("select").prop("disabled",true);
          $(".save-btn").prop("hidden",true);
          $(".plus").prop("hidden",true);
      }
      }
  });
}

function save(){
  //--------------5_6-----------------
  var temp1 = dimensionx("TPN");
  console.log(temp1);
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/save_section5_6.php",
    data:{data:JSON.stringify(temp1),num:"5_6"},
    success: function (data) {
        var result2 = JSON.parse(data);
        console.log(result2);
        alert_success({text: "บันทีกสำเร็จ", button_func_1: function(){ window.location.reload(); } });      
    }
  })
}