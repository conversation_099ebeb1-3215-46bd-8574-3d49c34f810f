var flag = false;
$("input").on("change",function(){
    flag = true;
});
setInterval(function(){
    if(flag){
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/get_concurance.php",
            data:{update:"update"},
            success: function (data) {
                console.log("change"+data);
            },
          });
        flag = false;
    }else{
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/get_concurance.php",
            success: function (data) {
                    console.log("nope"+data);
                     if(data==0){
                        alert_fail({
                            header : "ขออภัย",
                            text : "ท่านไม่ได้ทำการบันทึกข้อมูลผู้ป่วยคนนี้เกิน 10 นาที กรุณาเริ่มบันทึกใหม่",
                            button_func_1 : function(){
                                change_page("patientlist.html", "back");
                            }
                        });
                       
                         //window.location = "patientlist.html";
                     }
            },
          });
    }
},10000);