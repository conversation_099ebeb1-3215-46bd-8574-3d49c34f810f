
window.onload = function(){
  //  alert("test");
 
  setting();
  load();
 
}
function setting(){
  //info
  $("#info1").click(function(){ 
    show_info("Gravida (G) = ครรภ์นี้เป็นครรภ์ที่เท่าไหร่"
              + "<br/>Parity (P) = จำนวนครรภ์ที่คลอดโดยไม่แท้งก่อนครรภ์นี้"
              + "<br/>Abortion (A) = จำนวนครรภ์ที่แท้งก่อนครรภ์นี้"
              + "<br/><span style='font-weight:600'>(ตัวเลขสรุปจะเป็น G-1 = P+A)</span>") 
  })
  $("#info2").click(function(){ 
    show_info("ภาวะเลือดออกผิดปกติทางช่องคลอดในช่วงก่อนคลอด เช่น placental previa, placental abruption เป็นต้น") 
  })
  $("#info3").click(function(){ 
    show_info("Meconium stained amniotic fluid คือ ภาวะที่มีขี้เทาในน้ำคร่ำ") 
  })
  $("#info4").click(function(){ 
    show_info("Intrauterine growth restriction คือ ภาวการณ์เจริญเติบโตของทารกในครรภ์น้อยผิดปกติ") 
  })
  $("#info5").click(function(){ 
    show_info("ยาฆ่าเชื้อที่ให้แก่มารดาในช่วงก่อนคลอด <span style='font-weight:600'>ไม่รวม antibiotics</span> ที่ให้เพื่อเตรียม pre-operation") 
  })
  $("#info6").click(function(){ 
    show_info("การให้ prenatal steroid หรือ dexamethasone ที่ไม่ครบ course ก่อนคลอด (1 course คือ total dose 24 mg)") 
  })
  $("#info7").click(function(){ 
    show_info("การให้ prenatal steroid หรือ dexamethasone ที่ครบอย่างน้อย 1 course ก่อนคลอด (1 course คือ total dose 24 mg)") 
  })
  $("#info8").click(function(){ 
    show_info("ยาอื่น ๆ ที่ไม่ใช่ tocolytic drug") 
  })

  floatx("i1",0);
  floatx("i3",0);
  floatx("i5",0);
  floatx("i7",0);
  // $("input[name='i1']").on("change",function(){
  //   //alert($(this).val().toString().match("\\."));
  //   if($(this).val().toString().match("\\.")){
  //    alert_fail({
  //      text : "กรุณากรอกจำนวนเต็ม",
  //       button_func_1 : function(){
  //         $("input[name='i1']").val("");
  //       },
  //    });
  //   }
  // });
  $("input[name='i1']").on("touchstart",function(){
    //alert($(this).prop("disabled"));
    if($(this).prop("disabled")==true&&!localStorage.hospital){
      alert_fail({
        text : "กรุณาเลือก N/A ออกก่อน",
      }); 
    }
  });
  $("input[name='i3']").on("touchstart",function(){
    //alert($(this).prop("disabled"));
    if($(this).prop("disabled")==true&&!localStorage.hospital){
      alert_fail({
        text : "กรุณาเลือก N/A ออกก่อน",
      }); 
    }
  });
  $("input[name='i5']").on("touchstart",function(){
    //alert($(this).prop("disabled"));
    if($(this).prop("disabled")==true&&!localStorage.hospital){
      alert_fail({
        text : "กรุณาเลือก N/A ออกก่อน",
      }); 
    }
  });
  $("input[name='i7']").on("touchstart",function(){
    //alert($(this).prop("disabled"));
    if($(this).prop("disabled")==true&&!localStorage.hospital){
      alert_fail({
        text : "กรุณาเลือก N/A ออกก่อน",
      }); 
    }
  });
  $("input[name='i2']").on("change",function(){
    if($(this).prop("checked")!=true){
      $("input[name='i1']").prop("disabled",false);
    }else{
      $("input[name='i1']").prop("disabled",true);
      $("input[name='i1']").val("");
     // $("input[name='i4']").prop("checked",true);
    }
  });
  $("#NAshow").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='i4']").prop("checked",true);
      $("input[name='i6']").prop("checked",true);
      $("input[name='i8']").prop("checked",true);
    }else{
      $("input[name='i4']").prop("checked",false);
      $("input[name='i6']").prop("checked",false);
      $("input[name='i8']").prop("checked",false);
    }
    $("input[name='i4']").trigger("change");
    $("input[name='i6']").trigger("change");
    $("input[name='i8']").trigger("change");
  });
  $("input[name='i4']").on("change",function(){
    if($(this).prop("checked")!=true){
      $("input[name='i3']").prop("disabled",false);
    }else{
      $("input[name='i3']").prop("disabled",true);
      $("input[name='i3']").val("");
    }
  });
  $("input[name='i6']").on("change",function(){
    if($(this).prop("checked")!=true){
      $("input[name='i5']").prop("disabled",false);
    }else{
      $("input[name='i5']").prop("disabled",true);
      $("input[name='i5']").val("");
    }
  });

  $("input[name='i8']").on("change",function(){
    if($(this).prop("checked")!=true){
      $("input[name='i7']").prop("disabled",false);
    }else{
      $("input[name='i7']").prop("disabled",true);
      $("input[name='i7']").val("");
    }
  });
  $("input[name='i3']").on("change",function(){
    check_GPA($(this));
  });
  $("input[name='i5']").on("change",function(){
    check_GPA($(this));
  });
  $("input[name='i7']").on("change",function(){
    check_GPA($(this));
  });
  // $("input[name='ab5']").on("change",function(){

  //   if($(this).prop("checked")==true){
  //     $("input[name='other_etc']").slideDown(200);
  //   }else{
  //     $("input[name='other_etc']").slideUp(200);
  //     $("input[name='other_etc']").val("");
  //   }
  // });
  $("input[name='ab5']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_etc']").slideDown(200);
      $("input[name='ab6']").prop("checked",false);
    }
    else{
      
      $("input[name='Other_etc']").slideUp(200);
      $("input[name='Other_etc']").val("");
    }
  });
  for(var i = 1 ;i<5;i++){
    $("input[name='ab"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
       
        $("input[name='ab6']").prop("checked",false);
      }
    });
  }
  for(var i = 1 ;i<11;i++){
    $("input[name='comp"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='comp11']").prop("checked",false);
      }
    });
  }

  for(var i = 1 ;i<12;i++){
    $("input[name='intra"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='intra12']").prop("checked",false);
      }
    });
  }
  for(var i = 1 ;i<11;i++){
    $("input[name='intra"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='intra12']").prop("checked",false);
        $("input[name='intra11']").prop("checked",false);
      }
    });
  }
  for(var i = 2 ;i<5;i++){
    $("input[name='gbs"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='gbs1']").prop("checked",false);
      }
    });
  }
  for(var i = 1 ;i<8;i++){
    $("input[name='me"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='me8']").prop("checked",false);
      }
    });
  }
  $("input[name='ab6']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_etc']").slideUp(200);
      $("input[name='Other_etc']").val("");
      $("input[name='ab1']").prop("checked",false);
      $("input[name='ab2']").prop("checked",false);
      $("input[name='ab3']").prop("checked",false);
      $("input[name='ab4']").prop("checked",false);
      $("input[name='ab5']").prop("checked",false);
    }
  });
  $("input[name='gbs2']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='gbs3']").prop("checked",false);
    }
  });
  $("input[name='gbs3']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='gbs2']").prop("checked",false);
    }
  });
  $("input[name='comp10']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 0;i<10;i++){
        $("input[name='comp"+i+"']").prop("checked",false);
      }
      $("input[name='comp11']").prop("checked",false);
      $("#pop1").slideUp(200);
      $("input[name='Other2_etc']").slideUp(200);
      $("input[name='Other2_etc']").val("");
      $("input[name='Maternal_drug_etc']").slideUp(200);
      $("input[name='Maternal_drug_etc']").val("");
      $("input[name='Higher_order_etc']").slideUp(200);
      $("input[name='Higher_order_etc']").val("");
    }
  });

  $("input[name='comp6']").on("change",function(){
    if($(this).val()=="Higher_order"){
      $("input[name='Higher_order_etc']").slideDown(200);
    }else{
      $("input[name='Higher_order_etc']").slideUp(200);
      $("input[name='Higher_order_etc']").val("");
    }
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp8']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Maternal_drug_etc']").slideDown(200);
    
    }else{
      $("input[name='Maternal_drug_etc']").slideUp(200);
      $("input[name='Maternal_drug_etc']").val("");
    

    }
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp9']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other2_etc']").slideDown(200);
    
    }else{
      $("input[name='Other2_etc']").slideUp(200);
      $("input[name='Other2_etc']").val("");
    

    }
    $("input[name='comp10']").prop("checked",false);
  });
  $("input[name='comp5']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop1").slideDown(200);
    }else{
      $("#pop1").slideUp(200);
      $("input[name='comp6']").prop("checked",false);
      $("input[name='Higher_order_etc']").slideUp(200);
      $("input[name='Higher_order_etc']").val("");
    }
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp1']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp2']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp3']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp4']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp7']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });



  $("input[name='intra12']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1;i<12;i++){
        $("input[name='intra"+i+"']").prop("checked",false);
      }
      $("input[name='intra16']").prop("checked",false);
      $("input[name='Other_organism_etc']").slideUp(200);
      $("input[name='Other_organism_etc']").val("");
      $("input[name='Other3_etc']").slideUp(200);
      $("input[name='Other3_etc']").val("");
      $("#pop2").slideUp(200);
    }
  });
  $("input[name='intra10']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other3_etc']").slideDown(200);
      
    }else{
      $("input[name='Other3_etc']").slideUp(200);
      $("input[name='Other3_etc']").val("");
    }
    $("input[name='intra15']").prop("checked",false);

  });
  

  $("input[name='intra9']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_organism_etc']").slideDown(200);
   
    }else{
      $("input[name='Other_organism_etc']").slideUp(200);
      $("input[name='Other_organism_etc']").val("");
    }
    $("input[name='intra15']").prop("checked",false);

  });
  $("input[name='intra6']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop2").slideDown(200);
    }else{
      $("#pop2").slideUp(200);
      $("input[name='intra7']").prop("checked",false);
      $("input[name='intra8']").prop("checked",false);
      $("input[name='intra9']").prop("checked",false);
      $("input[name='Other_organism_etc']").slideUp(200);
      $("input[name='Other_organism_etc']").val("");
    }
    $("input[name='intra15']").prop("checked",false);

  });
  for(var i = 1 ;i<6;i++){
    $("input[name='intra"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='intra15']").prop("checked",false);
      }
    });
  }
  for(var i = 10 ;i<14;i++){
    $("input[name='intra"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='intra15']").prop("checked",false);
      }
    });
  }

  $("input[name='me7']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1; i<7;i++){
        $("input[name='me"+i+"']").prop("checked",false);
      }
      $("input[name='Other4_etc']").val("");
      $("input[name='me8']").prop("checked",false);
      $("input[name='Other4_etc']").slideUp(200);
      $("#pop3").slideUp(200);
      $("#pop4").slideUp(200);
      $("div[name='other_list']").slideUp(200);
      $("#list").html("");
      add();
      
    }

  });

  $("input[name='me8']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1; i<8;i++){
        $("input[name='me"+i+"']").prop("checked",false);
      }
      $("input[name='Other4_etc']").val("");
      $("input[name='Other4_etc']").slideUp(200);
      $("#pop3").slideUp(200);
      $("#pop4").slideUp(200);
      $("div[name='other_list']").slideUp(200);
      $("#list").html("");
      add();
    }
  });

  $("input[name='me1']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop3").slideDown(200);
      
    }else{
      $("input[name='me2']").prop("checked",false);
      $("#pop3").slideUp(200);
    }
    $("input[name='me7']").prop("checked",false );
  });

  $("input[name='me3']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop4").slideDown(200);
    }else{
      $("input[name='me4']").prop("checked",false);
      $("#pop4").slideUp(200);
    }
    $("input[name='me7']").prop("checked",false );
  });

  $("input[name='me5']").on("change",function(){

    $("input[name='me7']").prop("checked",false );
  });

  $("input[name='me6']").on("change",function(){
    
    if($(this).prop("checked")==true){
      $("div[name='other_list']").slideDown(200);
    }else{
      $("div[name='other_list']").slideUp(200);
      $("#list").html("");
      add();
    
    }
    $("input[name='me7']").prop("checked",false );
  });

  $("input[name='ab4']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_etc']").prop("checked",false);
      $("input[name='ab1']").prop("checked",false);
      $("input[name='ab2']").prop("checked",false);
      $("input[name='ab3']").prop("checked",false);
      $("input[name='ab6']").prop("checked",false);
      $("input[name='ab5']").prop("checked",false);
      $("input[name='Other_etc']").slideUp(200);
      $("input[name='Other_etc']").val("");
    }
  });
  
  for(var i = 1 ; i<7 ;i++ ){
    if(i!=4){
      $("input[name='ab"+i+"']").on("change",function(){
        if($(this).prop("checked")==true){
          $("input[name='ab4']").prop("checked",false);
        }
      });
    }
  }

  $("input[name='comp11']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1;i<11;i++){
        $("input[name='comp"+i+"']").prop("checked",false);
      }
      $("input[name='Other_etc']").prop("checked",false);
      $("#pop1").slideUp(200);
      $("input[name='Other2_etc']").slideUp(200);
      $("input[name='Other2_etc']").val("");
      $("input[name='Maternal_drug_etc']").slideUp(200);
      $("input[name='Maternal_drug_etc']").val("");
      $("input[name='Higher_order_etc']").slideUp(200);
      $("input[name='Higher_order_etc']").val("");
    }
  });

  $("input[name='intra11']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1;i<11;i++){
        $("input[name='intra"+i+"']").prop("checked",false);
      }
      $("#pop2").slideUp(200);
      $("input[name='Other_organism_etc']").slideUp(200);
      $("input[name='Other_organism_etc']").val("");
      $("input[name='Other3_etc']").slideUp(200);
      $("input[name='Other3_etc']").val("");
    
    }
  });
  $("input[name='gbs1']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 2;i<5;i++){
        $("input[name='gbs"+i+"']").prop("checked",false);
      }
      $("#pop2").slideUp(200);
      $("input[name='Other_organism_etc']").slideUp(200);
      $("input[name='Other_organism_etc']").val("");
      $("input[name='Other5_etc']").slideUp(200);
      $("input[name='Other5_etc']").val("");
    
    }
  });

  $("input[name='gbs4']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other5_etc']").slideDown(200);
    }else{
      $("input[name='Other5_etc']").slideUp(200);
      $("input[name='Other5_etc']").val("");
    }
  });

}
function check_GPA(ob){
  var g = $("input[name='i3']").val();
  var p = $("input[name='i5']").val();
  var a = $("input[name='i7']").val();
  if(g!=""&&p!=""&&a!=""){
    g = parseInt(g);
    p = parseInt(p);
    a = parseInt(a);
    if(g!=p+a+1){
      alert_fail({text:"กรุณาตรวจสอบข้อมูล",button_func_1 : function(){
        $(ob).val("");
      }});
    }else{
    //  alert(g+" "+p+" "+a);
    }
  }else{
   // alert("test");
  }
}
function add(){
  $("#list").append( '<div class="bar-delete-list-input"  hidden><div class="content_box">'
  +'<input class="input-section margin-btm" type="text" placeholder="Please specify" name = "otherx1">'
  +'<div class="circle-red" onclick = "del(this)"><img src="img/delete.svg"></div>'
+'</div></div>');
  $(".bar-delete-list-input").slideDown(200);
}
function del(ob){
  $(ob.parentNode.parentNode).slideUp(200);
  setTimeout(function(){
    document.getElementById("list").removeChild(ob.parentNode.parentNode);
  },200);

}
function load(onSuccess){
  var temp1,temp2,temp3,temp4,temp5,temp6,temp7;
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section1.php",
    data:{num:"2",hospital:localStorage.hospital},
    success: function (data) {
        var result2 = JSON.parse(data);
        temp1 = result2;
        console.log(result2);
        if(result2.length!=0){
            push_all_section_data("i",result2[0]);
            if(result2[0]["G_NA"]!=""){
              $("#NAshow").prop("checked",true);
            }
        }
        

        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/get_section2.php",
          data:{num:"2_2",hospital:localStorage.hospital},
          success: function (data) {
              var result2 = JSON.parse(data);
              temp2 = result2;
              console.log(result2);
              if(result2.length!=0){
                  push_all_section_data("ab",result2[0]);
              }
              $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/get_section2.php",
                data:{num:"2_3",hospital:localStorage.hospital},
                success: function (data) {
                    var result2 = JSON.parse(data);
                    temp3 = result2;
                    console.log(result2);
                    if(result2.length!=0){
                        push_all_section_data("comp",result2[0]);
                        if(result2[0]["number"]=="Twin"){
                          $($("input[name='comp6']")[0]).prop("checked",true);
                          $($("input[name='comp6']")[0]).trigger("change");
                        }
                    }
                    $.ajax({
                      type: "POST",
                      url: "https://techvernity.com/thainy/php/get_section2.php",
                      data:{num:"2_4",hospital:localStorage.hospital},
                      success: function (data) {
                          var result2 = JSON.parse(data);
                          temp4 = result2;
                          console.log(result2);
                          if(result2.length!=0){
                              push_all_section_data("intra",result2[0]);
                          }
                          $.ajax({
                            type: "POST",
                            url: "https://techvernity.com/thainy/php/get_section2.php",
                            data:{num:"2_6",hospital:localStorage.hospital},
                            success: function (data) {
                                var result2 = JSON.parse(data);
                                temp6 = result2;
                                console.log(result2);
                                if(result2.length!=0){
                                    push_all_section_data("gbs",result2[0]);
                                }
                            $.ajax({
                              type: "POST",
                              url: "https://techvernity.com/thainy/php/get_section2.php",
                              data:{num:"2_5",hospital:localStorage.hospital},
                              success: function (data) {
                                  var result2 = JSON.parse(data);
                                  temp5 = result2;
                                  console.log(result2);
                                  if(result2.length!=0){
                                      push_all_section_data("me",result2[0]);
                                  }
                                  $.ajax({
                                    type: "POST",
                                    url: "https://techvernity.com/thainy/php/get_section2_other.php",
                                    data:{hospital:localStorage.hospital},
                                    success: function (data) {
                                        var result2 = JSON.parse(data);
                                        console.log(result2);
                                        temp7 = result2;
                                        for(var i = 0;i<result2.length;i++){
                                          if(i!=0){
                                            add();
                                          }
                                          document.getElementsByName("otherx1")[i].value = result2[i]["other"];
                                        }
                                        

                                        var status = "done";
                                       if(temp1.length==""||temp2.length==0||temp3.length==0||temp4.length==0||temp5.length==0||temp6.length==0){
                                           status = "not done";
                                        }else{
                                          var status1 = "done"; 
                                          //console.log(temp1);
                                          if(temp1[0]["age"]==""&&temp1[0]["age_NA"]==""){
                                            status1 = "not done";
                                          }
                                          if(temp1[0]["G"]==""&&temp1[0]["G_NA"]==""){
                                            status1 = "not done";
                                          }
                                         // alert(temp1[0]["P_NA"]=="");
                                          if(temp1[0]["P"]==""&&temp1[0]["P_NA"]==""){
                                            status1 = "not done";
                                          }
                                          if(temp1[0]["A"]==""&&temp1[0]["A_NA"]==""){
                                            status1 = "not done";
                                          }
                                         // alert(status1);

                                          var status2 = check_status(temp2);  
                                          var status3 = check_status(temp3);
                                          if(temp3[0]["multiple_gestation"]!=""&&temp3[0]["number"]==""&&status3=="done"){
                                            status3 = "not done";
                                          }
                                          var status4 = check_status(temp4);  
                                          var status5 = check_status(temp5);   
                                          var status6 = check_status(temp6);  
                                          console.log(status1);
                                          console.log(status2);
                                          console.log(status3);
                                          console.log(status4);
                                          if(temp5[0]["antibiotics"]!=""&&temp5[0]["time"]==""){
                                            status5 = "not done";
                                          }
                                          if(temp5[0]["dexamamthasone_or_prenatal_steroid"]!=""&&temp5[0]["course"]==""){
                                            status5 = "not done";
                                          }
                                    
                                          if(temp5[0]["other"]!=""&&temp7.length==0){
                                            status7 = "not done";
                                          }else{
                                            status7 = "done";
                                          }
                                          console.log(status5);
                                          console.log(status6);
                                          console.log(status7);
                                          if(status1=="done"&&status2=="done"&&status3=="done"&&status4=="done"&&status5=="done"&status6=="done"&&status7=="done"){
                                            status = "done";
                                          }else{
                                            status = "not done";
                                          }
                                        }
                                        if(localStorage.hospital != ""){
                                          //alert(localStorage.hospital);
                             
                                          $("input").prop("disabled",true);
                                          $("textArea").prop("disabled",true);
                                          $("select").prop("disabled",true);
                                          $(".save-btn").prop("hidden",true);
                                          $(".plus").prop("hidden",true);
                                        }
                                        $.ajax({
                                          type: "POST",
                                          url: "https://techvernity.com/thainy/php/update_progress.php",
                                          data:{num:"2",status:status},
                                          success: function (data) {
                                              //alert(status);
                                              if(!!onSuccess)  onSuccess(); 
                                          },
                                        });
                                      },
                                  });
                              },
                            });
                          },
                        });
                      },
                    });
                },
              });
          },
        });
    },
  });
}


function save(){
    var temp = get_all_data("i");
    console.log(temp);
    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/save_section2.php",
      data:{data:JSON.stringify(temp),num:"2"},
      success: function (data) {
          var result = JSON.parse(data);
          console.log(result);
          
          //.location.reload();

          var temp2 = get_all_data("ab");
          console.log(temp2);
      
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_section2.php",
            data:{data:JSON.stringify(temp2),num:"2_2"},
            success: function (data) {
                var result = JSON.parse(data);
                console.log(result);
               
               // window.location.reload();
               var temp3 = get_all_data("comp");
               console.log(temp3); 

               $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/save_section2.php",
                data:{data:JSON.stringify(temp3),num:"2_3"},
                success: function (data) {
                    var result = JSON.parse(data);
                    console.log(result);
                   
                   // window.location.reload();
                   var temp4 = get_all_data("intra");
                    console.log(temp4); 

                    $.ajax({
                      type: "POST",
                      url: "https://techvernity.com/thainy/php/save_section2.php",
                      data:{data:JSON.stringify(temp4),num:"2_4"},
                      success: function (data) {
                          var result = JSON.parse(data);
                          console.log(result);
                        
                        // window.location.reload();
                        var temp5 = get_all_data("me");
                        console.log(temp5);

                        $.ajax({
                          type: "POST",
                          url: "https://techvernity.com/thainy/php/save_section2.php",
                          data:{data:JSON.stringify(temp5),num:"2_5"},
                          success: function (data) {
                              var result = JSON.parse(data);
                              console.log(result);
                            
                            // window.location.reload();
                            var temp6 = get_all_data("gbs");
                            console.log(temp6); 

                            $.ajax({
                              type: "POST",
                              url: "https://techvernity.com/thainy/php/save_section2.php",
                              data:{data:JSON.stringify(temp6),num:"2_6"},
                              success: function (data) {
                                  var result = JSON.parse(data);
                                  console.log(result);
                                  
                                  var temp_other = get_all_data("otherx");
                                  console.log(temp_other);
                                  var otherx = Array();
                                  if(temp_other.length > 0){
                                    for(var i = 0;i<temp_other[0].length;i++){
                                      //alert(temp_other[0][i]);
                                      if(temp_other[0][i]!=""){
                                        otherx.push(temp_other[0][i]);  
                                      }
                                    }
                                  }
                                  temp_other[0] = otherx;
                                  $.ajax({
                                    type: "POST",
                                    url: "https://techvernity.com/thainy/php/save_section2_other.php",
                                    data:{data:JSON.stringify(temp_other)},
                                    success: function (data) {
                                        // var result = JSON.parse(data);
                                        // console.log(result);
                                        load(function(){
                                          alert_success({
                                            text:"บันทึกสำเร็จ",
                                            button_func_1 : function(){
                                              change_page("patient_display.html","back");
                                            }
                                          });
                                        });
                                      
                                    },
                                  });
                                
                              },
                            });
                          },
                        });
                      },
                    });
                },
              });
            },
          });
      },
    });

   

  //  alert("บันทีกสำเร็จ");
  //  window.location.reload();
}