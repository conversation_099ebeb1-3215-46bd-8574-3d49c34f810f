var status = "done";
var col1 = ["no_documented","CLASSI","proven_sepsis","early_neonatal_sepsis","late_neonatal_sepsis","neonatal_menigitis","VAP","UTI","other"];
window.onload = function(){
    //alert("test");
    get_column("thainy","section5_11");
    setting();
    load();
}
function load(callback){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"5_11",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result.length);
            if(result.length!=0){
                push_ob_data(result[0]);
              //  alert(result[0]["other"]);
                if(result[0]["other"]!=""){
                    $("input[name='other_main']").prop("checked",true);
                    $("input[name='other']").slideDown(200);
                }
                status = check_status(result);
                console.log(status);
            }else{
                status = "not done";
            }
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/get_ob_section.php",
                data:{num:"5_11_detail",hospital:localStorage.hospital},
                success: function (data) {
                    var result = JSON.parse(data);
                    //console.log(result.length);
                    if(result.length!=0){
                        console.log(result);
                        var title = ["class","detail","late","neo","uti","vap"];
                        for(var i = 0;i<result.length;i++){
                            var temp = result[i];
                            push_all_section_data(title[i],temp);
                        }
                    }
                    $.ajax({
                        type: "POST",
                        url: "https://techvernity.com/thainy/php/update_progress.php",
                        data:{num:"5_11",status:status},
                        success: function (data) {
                            //alert(status);
                            if(!!callback){
                                callback();
                            }
                        },
                      });
                    
                    if(localStorage.hospital != ""){
                        //alert(localStorage.hospital);
                        $("input").prop("disabled",true);
                        $("textArea").prop("disabled",true);
                        $("select").prop("disabled",true);
                        $(".save-btn").prop("hidden",true);
                        $(".plus").prop("hidden",true);
                    }
                },
              });
        },
      });
}
function setting(){
   //info
   $("#info1").click(function(){
        show_info("Central Line-associated Blood Stream Infection โดยมี Positive hemoculture 2 วันหลังใส่ จนถึง1 วันหลัง Off (ดังรูป)") 
   })
   $("#info2").click(function(){
    show_info("การติดเชื้อในกระแสเลือด ที่มี positive hemoculture") 
})
$("#info3").click(function(){
    show_info("การติดเชื้อในกระแสเลือดภายใน 3 วันแรกของชีวิต (อายุ <= 3 วัน)") 
})
$("#info4").click(function(){
    show_info("การติดเชื้อในกระแสเลือดหลังอายุ 3 วัน (อายุ > 3 วัน)") 
}
)
$("#info5").click(function(){
    show_info("การติดเชื้อในเยื่อหุ้มสมอง โดยที่มี Positive CSF culture") 
})
$("#info6").click(function(){
    show_info("Ventilator-associated pneumonia คือ Pneumonia ในผู้ป่วยที่ใส่เครื่องช่วยหายใจ") 
})
$("#info7").click(function(){
    show_info("Urinary tract infection, Positive urine culture > 10<sup>4</sup> (สำหรับ urinary catheterization) หรือ > 10<sup>3</sup> (สำหรับ suprapubic aspiration)") 
})


    $("input[name='no_documented']").on("change",function(){
        if($(this).prop("checked")==true){
            for(var i = 1;i<col1.length;i++){
                $("input[name='"+col1[i]+"']").prop("checked",false);
                $("input[name='"+col1[i]+"']").trigger("change");
            }
            $("input[name='other_main']").prop("checked",false);
            $("input[name='other_main']").trigger("change");
        }
    });
    for(var i = 1;i<col1.length;i++){
        $("input[name='"+col1[i]+"']").on("change",function(){
            if($(this).prop("checked")==true){
                 $("input[name='no_documented']").prop("checked",false);
            }
        });
    }
    $("input[name='other_main']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='no_documented']").prop("checked",false);
            $("input[name='other']").slideDown(200);
       }else{
        $("input[name='other']").slideUp(200);
        $("input[name='other']").val("");
       }
    });
    $("input[name='proven_sepsis']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='no_documented']").prop("checked",false);
            $("#pop1").slideDown(200);
       }else{
            $("#pop1").slideUp(200);
            $("input[name='early_neonatal_sepsis']").prop("checked",false);
            $("input[name='late_neonatal_sepsis']").prop("checked",false);

            $("input[name='early_neonatal_sepsis']").trigger("change");
            $("input[name='late_neonatal_sepsis']").trigger("change");
       }
    });

    $("input[name='neonatal_menigitis']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='no_documented']").prop("checked",false);
            $("#pop2").slideDown(200);
       }else{
            $("#pop2").slideUp(200);
            dis("neo",1,14);
       }
    });
    $("input[name='CLASSI']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='no_documented']").prop("checked",false);
            $("#popinx").slideDown(200);
       }else{
            $("#popinx").slideUp(200);
            dis("class",1,14);
       }
    });
    $("input[name='class14']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='Other_x_etc']").slideDown(200);
        }else{
            $("input[name='Other_x_etc']").slideUp(200);
            $("input[name='Other_x_etc']").val("");
        }
    });
    $("input[name='VAP']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='no_documented']").prop("checked",false);
            $("#pop3").slideDown(200);
       }else{
            $("#pop3").slideUp(200);
            dis("vap",1,14);
       }
    });

    $("input[name='UTI']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='no_documented']").prop("checked",false);
            $("#pop4").slideDown(200);
       }else{
            $("#pop4").slideUp(200);
            dis("uti",1,14);
       }
    });

    $("input[name='early_neonatal_sepsis']").on("change",function(){
            if($(this).prop("checked")==true){
                $("#popin1").slideDown(200);
            }else if($(this).prop("checked")==false){
                $("#popin1").slideUp(200);
                dis("detail",0,14);
            }
    });
    $("input[name='late_neonatal_sepsis']").on("change",function(){
            if($(this).prop("checked")==true){
                $("#popin2").slideDown(200);
            }else if($(this).prop("checked")==false){
                $("#popin2").slideUp(200);
                dis("late",0,14);
            }
    });
    $("input[name='detail14']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='Other_1_etc']").slideDown(200);
        }else if($(this).prop("checked")==false){
            $("input[name='Other_1_etc']").slideUp(200);
            $("input[name='Other_1_etc']").val("");
        }
    });
    $("input[name='late14']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='Other_2_etc']").slideDown(200);
        }else if($(this).prop("checked")==false){
            $("input[name='Other_2_etc']").slideUp(200);
            $("input[name='Other_2_etc']").val("");
        }
    });
    $("input[name='neo14']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='Other_3_etc']").slideDown(200);
        }else if($(this).prop("checked")==false){
            $("input[name='Other_3_etc']").slideUp(200);
            $("input[name='Other_3_etc']").val("");
        }
    });
    $("input[name='vap14']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='Other_4_etc']").slideDown(200);
        }else if($(this).prop("checked")==false){
            $("input[name='Other_4_etc']").slideUp(200);
            $("input[name='Other_4_etc']").val("");
        }
    });
    $("input[name='uti14']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='Other_5_etc']").slideDown(200);
        }else if($(this).prop("checked")==false){
            $("input[name='Other_5_etc']").slideUp(200);
            $("input[name='Other_5_etc']").val("");
        }
    });
}
function save(){
    var ob1 = get_ob_data(col1);

    var detail1 = get_all_data("detail");
    var detail2 = get_all_data("late");
    var detail3 = get_all_data("neo");
    var detail4 = get_all_data("vap");
    var detail5 = get_all_data("uti");
    var detail6 = get_all_data("class");

    console.log(ob1);
    console.log(detail1);
    console.log(detail2);
    console.log(detail3);
    console.log(detail4);
    console.log(detail5);
    console.log(detail6);

    var temp = [];
    temp.push(detail1);
    temp.push(detail2);
    temp.push(detail3);
    temp.push(detail4);
    temp.push(detail5);
    temp.push(detail6);
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_ob_section_5_11.php",
        data:{data:JSON.stringify(temp)},
        success: function (data) {
            // var result = JSON.parse(data);
            // console.log(result);
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/save_ob_section.php",
                data:{data:ob1,num:"5_11"},
                success: function (data) {
                    var result = JSON.parse(data);
                    console.log(result);
                    load(function(){
                        alert_success({
                            text:"บันทึกสำเร็จ",
                            button_func_1 : function(){
                                change_page("section5.html","back");
                            }
                        });
                    });
              
                },
              });
        },
      });
    
}