var check = false
window.onload = function(){
    load();
    //alert("test");
    setting();
  }
  function setting(){
      $("input[name=radio1]").on("change",function(){
          if($(this).val()=="Member Hospital"){
            $("select[name='destination_hospital']").prop("disabled",false);
          }else{
            $("select[name='destination_hospital']").prop("disabled",true);
            $("select[name='destination_hospital']").val("");
          }
          check = true;
      });
  }
  function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_hospital.php",
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
           // alert(result.length);
            $("select[name='destination_hospital']").html(' <option value = "" selected disabled>เลือกโรงพยาบาล</option>');
            for(var i = 0;i<result.length;i++){
                $("select[name='destination_hospital']").append(' <option value="'+result[i]["hospital_name"]+'">'+result[i]["hospital_name"]+'</option>');
            }
        },
    });
      
      $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/get_patient_display.php",
          success: function (data) {
              var result = JSON.parse(data);
              console.log(result);
              document.getElementById("name").innerHTML = result["fullname"];
              document.getElementById("TNR").innerHTML = "TN# : "+result["TNR"];
              document.getElementById("HN").innerHTML = "HN : "+result["HN"];
              document.getElementById("mom").innerHTML = "มารดา : "+result["mother_fullname"];
              document.getElementById("date").innerHTML = get_thai_date(new Date(getDate(0)))+" -"+get_time();
          },  
  
        });
  }

  function refer(){
      var hospital = document.getElementsByName("destination_hospital")[0].value;
      var status = "waiting";
     
      if(!check){
        alert_fail({text:"please select destination hospital"});
      }
      else if(document.getElementsByName("radio1")[0].checked==true&&hospital == ""){
        alert_fail({text:"please select destination hospital"});
      }else{
        if(document.getElementsByName("radio1")[1].checked==true){
            hospital = "Non-member Hospital";
            status = "non-mem refer";
        }
        localStorage.destination = hospital;
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/refer.php",
            data:{hospital:hospital,time:document.getElementById("date").innerHTML,status:status},
            success: function (data) {
                var result = JSON.parse(data);
                console.log(result);
               // alert("refer success!!");
               change_page("refer_send3.html", "next");
              //  window.location = "refer_send3.html";
            },  
    
          });
      }
  }