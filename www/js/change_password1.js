$(document).ready(function(){  
  $("#save-btn").on("click",function(){
    var password = $("#password").val();

    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/check_password.php",
      data: {
        password: password
      },
      success: function (data) {
          var result = JSON.parse(data);

          if(result.status == 1){
            change_page("change_password2.html", "next");
            //window.location = "change_password2.html"
          }
          else{
            alert_fail({text: "ดูเหมือนว่ารหัสผ่านที่คุณกรอก<br/>ไม่ถูกต้อง", header: "ไม่ถูกต้อง"})
          }
      }
    });
  })
})
// 