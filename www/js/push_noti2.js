// Add to index.js or the first page that loads with your app.
// For Intel XDK and please add this to your app.js.
var noti_id ="";

document.addEventListener('deviceready', function () {
    // Enable to debug issues.
    // window.plugins.OneSignal.setLogLevel({logLevel: 4, visualLevel: 4});
    
    var notificationOpenedCallback = function(jsonData) {
      console.log('notificationOpenedCallback: ' + JSON.stringify(jsonData));
    };
  
    window.plugins.OneSignal
      .startInit("************************************")
      .handleNotificationOpened(notificationOpenedCallback)
      .endInit();

    
  window.plugins.OneSignal.getPermissionSubscriptionState(function(status) {
    status.permissionStatus.hasPrompted; // Bool
    status.permissionStatus.status; // iOS only: Integer: 0 = Not Determined, 1 = Denied, 2 = Authorized
    status.permissionStatus.state; //Android only: Integer: 1 = Authorized, 2 = Denied
  
    status.subscriptionStatus.subscribed; // Bool
    status.subscriptionStatus.userSubscriptionSetting; // Bool
    status.subscriptionStatus.userId; // String: OneSignal Player ID
    status.subscriptionStatus.pushToken; // String: Device Identifier from FCM/APNs
    console.log(status.subscriptionStatus.userId)

    localStorage.noti_id = status.subscriptionStatus.userId;
  });
 
  }, false);

  Date.prototype.addDays = function(days) {
    this.setDate(this.getDate() + parseInt(days));
    return this;
  };
//  push_noti();



function update_noti_key(){
  console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    $.ajax({
        //Warning_file_by_beaw.php
        
        type: "POST",
        url: "http://techvernity.com/thainy/php/check_noti_key.php",
        data:{token:localStorage.token,user_id:localStorage.noti_id,hos_id:localStorage.hos_id,p_id:localStorage.p_id}
           ,
        success: function (data) {

            console.log(data);

            var result = JSON.parse(data);
            if(data=="1")
            {
              
                    console.log("add_new")
            }else{
              console.log(result.user_id);
                if(result.user_id.length!=0)
            {                
                var multi_key = result.user_id.split(","); 
                    console.log(multi_key.length)
                var duplicate = 0;
                    for(var i =0 ; i<=multi_key.length-1;i++)
                    {   
                       console.log(multi_key[i]+"//"+localStorage.noti_id) 
                        if(multi_key[i] == localStorage.noti_id )
                        {
                            duplicate++;
                        }
                    }

                    if(duplicate == 0)
                    {
                        multi_key.push(localStorage.noti_id);
                        console.log(multi_key.toString());
                        $.ajax({
                            //Warning_file_by_beaw.php
                            
                            type: "POST",
                            url: "http://techvernity.com/thainy/php/update_noti_key.php",
                            data:{user_id:multi_key.toString(),username:result.username,hos_id:localStorage.hos_id}
                               ,
                            success: function (data) {
                              console.log(data);
                            },
                            error: function (jqXHR, exception) {
                                var msg = ajaxError(jqXHR, exception);
                               
                            }
                        });


                    }

                    
                    
                }else{
                    $.ajax({
                        //Warning_file_by_beaw.php
                        
                        type: "POST",
                        url: "http://techvernity.com/thainy/php/update_noti_key.php",
                        data:{user_id:localStorage.noti_id,username:result.username}
                           ,
                        success: function (data) {
                          console.log(data);
                        },
                        error: function (jqXHR, exception) {
                            var msg = ajaxError(jqXHR, exception);
                           
                        }
                    });
                }
            }
        },
        error: function (jqXHR, exception) {

        //    update_noti_key();
            var msg = ajaxError(jqXHR, exception);
           
        }
    });


}

function push_noti ()
{
  
 
    $.ajax({
      //Warning_file_by_beaw.php
      
      type: "POST",
      url: "http://techvernity.com/thainy/php/aa_noti.php",
      data:{message:"Test Before"}
         ,
      success: function (data) {
        console.log(data);
      },
      error: function (jqXHR, exception) {
          var msg = ajaxError(jqXHR, exception);
         
      }
  });

}

function get_user_id(TNR,type)
{
    $.ajax({
        //Warning_file_by_beaw.php
        //noti_30d
        type: "POST",
        url: "http://techvernity.com/thainy/php/load_user_id.php",
        data:{hos_id:localStorage.hos_id}
           ,
        success: function (data) {
          var group_user="" ;
          var result = JSON.parse(data)
          for(var i =0;i<result.length;i++)
          {
             var n_user_id = result[i].user_id.split(',');
                 for(var x=0;x<n_user_id.length;x++)
                 {
                     if(group_user.length==0)
                     {   group_user = n_user_id[x];
                     }else{
                        group_user = group_user+","+n_user_id[x];
                     }
                    
                 } 
                 

          }

          if(type=="30d"){
            set_noti_30d(TNR,group_user);
          }else{
            if(type=="15d")
            {
                set_noti_15d(TNR,group_user);
            }else{
                if(type=="1y")
                {
                    first_noti_1y(TNR,group_user);
                }
            }

          }
          
         
        },
        error: function (jqXHR, exception) {
            var msg = ajaxError(jqXHR, exception);
           
        }
    });


}






function set_noti_15d(TNR,group_user){    
 var n_date =  new Date().addDays(15);
  console.log(n_date)
    $.ajax({
        //Warning_file_by_beaw.php
        //noti_30d
        type: "POST",
        url: "http://techvernity.com/thainy/php/noti_affer.php",
        data:{message:"ผู้ป่วย TNR:"+TNR+" ไม่ถูกอัพเดทข้อมูล 15 วันมาแล้ว",ids:group_user,date:n_date}
           ,
        success: function (data) {

           
            var result = JSON.parse(data);
                 console.log(result)
                 console.log(result.id);
            $.ajax({
                //Warning_file_by_beaw.php
                //noti_30d
                type: "POST",
                url: "http://techvernity.com/thainy/php/save_noti_id_15d.php",
                data:{TNR:TNR,noti_id:result.id}
                   ,
                success: function (data) {
                  console.log(data);
                 
                },
                error: function (jqXHR, exception) {
                    var msg = ajaxError(jqXHR, exception);
                   
                }
            });
          
        },
        error: function (jqXHR, exception) {
            var msg = ajaxError(jqXHR, exception);
           
        }
    });

}


function cancel_noti(TNR,type){

    $.ajax({
        
        type: "POST",
        url: "http://techvernity.com/thainy/php/cancel_noti.php",
        data:{TNR:TNR,type:type}
           ,
        success: function (data) {
          console.log(data);
         
        },
        error: function (jqXHR, exception) {
            var msg = ajaxError(jqXHR, exception);
           
        }
    });


}




function set_noti_30d(TNR,group_user){    
 
    var n_date =  new Date().addDays(30);
    console.log(n_date)
      $.ajax({
          //Warning_file_by_beaw.php
          //noti_30d
          type: "POST",
          url: "http://techvernity.com/thainy/php/noti_affer.php",
          data:{message:"ผู้ป่วย TNR:"+TNR+" ไม่ถูกอัพเดทข้อมูล 30 วันมาแล้ว",ids:group_user,date:n_date}
             ,
          success: function (data) {
  
             
              var result = JSON.parse(data);
                   console.log(result)
                   console.log(result.id);
              $.ajax({
                  //Warning_file_by_beaw.php
                  //noti_30d
                  type: "POST",
                  url: "http://techvernity.com/thainy/php/save_noti_id_30d.php",
                  data:{TNR:TNR,noti_id:result.id}
                     ,
                  success: function (data) {
                    console.log(data);
                   
                  },
                  error: function (jqXHR, exception) {
                      var msg = ajaxError(jqXHR, exception);
                     
                  }
              });
            
          },
          error: function (jqXHR, exception) {
              var msg = ajaxError(jqXHR, exception);
             
          }
      });
  
}


function first_noti_1y(TNR,group_user){

    var n_date =  new Date().addDays(365);
    console.log(n_date)
     console.log(group_user)
      $.ajax({
          //Warning_file_by_beaw.php
          //noti_30d
          type: "POST",
          url: "http://techvernity.com/thainy/php/noti_affer.php",
          data:{message:"ผู้ป่วย TNR:"+TNR+" อายุครบ 1 ปี กรุณาปิดเคส",ids:group_user,date:n_date}
             ,
          success: function (data) {
  
             
              var result = JSON.parse(data);
                   console.log(result)
                   console.log(result.id);
                   $.ajax({
                    //Warning_file_by_beaw.php
                    //noti_30d
                    type: "POST",
                    url: "http://techvernity.com/thainy/php/save_noti_id_1y.php",
                    data:{TNR:TNR,noti_id:result.id,o_date:n_date}
                       ,
                    success: function (data) {
                      console.log(data);
                     
                    },
                    error: function (jqXHR, exception) {
                        var msg = ajaxError(jqXHR, exception);
                       
                    }
                });
            
          },
          error: function (jqXHR, exception) {
              var msg = ajaxError(jqXHR, exception);
             
          }
      });

}


function noti_1y_refer(TNR,group_user){

    var n_date =  new Date().addDays(365);
    console.log(n_date)
    $.ajax({
        //Warning_file_by_beaw.php
        //noti_30d
        type: "POST",
        url: "http://techvernity.com/thainy/php/get_1y_date.php",
        data:{TNR:TNR,noti_id:result.id,o_date:n_date}
           ,
        success: function (data) {
          console.log(data);
         
        },
        error: function (jqXHR, exception) {
            var msg = ajaxError(jqXHR, exception);
           
        }
    });





      $.ajax({
          //Warning_file_by_beaw.php
          //noti_30d
          type: "POST",
          url: "http://techvernity.com/thainy/php/noti_affer.php",
          data:{message:"ผู้ป่วย TNR:"+TNR+" อายุครบ 1 ปี กรุณาปิดเคส",ids:group_user,date:n_date}
             ,
          success: function (data) {
  
             
              var result = JSON.parse(data);
                   console.log(result)
                   console.log(result.id);
                   $.ajax({
                    //Warning_file_by_beaw.php
                    //noti_30d
                    type: "POST",
                    url: "http://techvernity.com/thainy/php/save_noti_id_1y.php",
                    data:{TNR:TNR,noti_id:result.id,o_date:n_date}
                       ,
                    success: function (data) {
                      console.log(data);
                     
                    },
                    error: function (jqXHR, exception) {
                        var msg = ajaxError(jqXHR, exception);
                       
                    }
                });
            
          },
          error: function (jqXHR, exception) {
              var msg = ajaxError(jqXHR, exception);
             
          }
      });

}