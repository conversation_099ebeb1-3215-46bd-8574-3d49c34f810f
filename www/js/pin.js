var pin_page = function(){
  var user_pin = "";
    var count = 0;
        if(user_pin.length!=6){
            $("#one-btn").on("click",function(){
                user_pin += 1;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#two-btn").on("click",function(){
                user_pin += 2;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#three-btn").on("click",function(){
                user_pin += 3;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#four-btn").on("click",function(){
                user_pin += 4;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#five-btn").on("click",function(){
                user_pin += 5;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#six-btn").on("click",function(){
                user_pin += 6;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#seven-btn").on("click",function(){
                user_pin += 7;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#eight-btn").on("click",function(){
                user_pin += 8;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#nine-btn").on("click",function(){
                user_pin += 9;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            })
            $("#zero-btn").on("click",function(){
                user_pin += 0;
                console.log(user_pin);
                $("#dot"+count).css("background-color","white");
                count++;
                if(user_pin==localStorage.master_pin){
                    $(".pin-box").hide();
                }
            }) 
            $("#delete-btn").on("click",function(){
              user_pin = user_pin.substring[0,count];
              console.log(user_pin);
              $("#dot"+count).css("background-color","#7FC5C6");
              count--;
            })    
        }
}