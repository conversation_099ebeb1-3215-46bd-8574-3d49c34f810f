var col = ["severity_within_6_hr","severity_the_worst","receive_TH","cooling_start_at_date","cooling_start_at_time","after_birth","temperature","complete_cooling","specify","duration","seizure","seizure_phenobarbital","seizure_phenytoin","seizure_midazolam","seizure_levetiracetam","seizure_topiramate","seizure_diazepam","seizure_other_main","seizure_none","start_anticonvulsant","aEEG_or_EGG_done","normal_background","abnormal","aEEG_seizure","result_other"];
window.onload = function(){
  get_birth_date(function(){
    timeline_min_max("birth_date","now");
  });
  get_birth_datetime();
  setting();
  load();
}
function setting(){
  $("#info1").click(function(){
    show_info("Hypoxic ischemic encephalopathy") 
})
$("#info2").click(function(){
  show_info("Severity based on NICHD protocol") 
})
$("#info3").click(function(){
  show_info("Severity based on NICHD protocol") 
})
$("#info4").click(function(){
  show_info("Therapeutic hypothermia") 
})
    floatx("temperature",1);
    // ------------------5_12-------------------
    console.log(col);
    $("input[name='receive_TH']").on("change",function(){
        if($(this).prop("checked")==true&&$(this).val()=="Yes"){
            $("#pop1").slideDown(200);
        }else{
            $("#pop1").slideUp(200);
            dis_ob(col,3,9);
            $("#pop2").slideUp(200);
        }
    });

    $("input[name='cooling_start_at_date'], input[name='cooling_start_at_time']").on("change", function(){
       var cooling_start_at_date = $("input[name='cooling_start_at_date']").val();
       var cooling_start_at_time = $("input[name='cooling_start_at_time']").val();
       var cooling_date;

       if(!!cooling_start_at_date && !!cooling_start_at_time){
          cooling_date = cooling_start_at_date + " " + cooling_start_at_time;
       }

       var cooling_time = diffTime(birth_datetime_data, cooling_date, 'm');
       var cooling_text = padLeft(Math.floor(cooling_time/60),'2') + ":" + padLeft(cooling_time%60,2)
       
       if(isNaN(cooling_time)){
        cooling_text = "";
       }
       
       if(new Date(cooling_date) < new Date(birth_datetime_data)){
         cooling_text = "-" + cooling_text; 
       }
       $("input[name='after_birth'").val(cooling_text)
    })

    $("input[name='temperature']").on("change",function(){
      if(parseFloat($(this).val())<25||parseFloat($(this).val())>45){
          alert_fail({text : "กรุณากรอกข้อมูลในช่วง 25-45",
          button_func_1 :function(){
            $("input[name='temperature']").val("");
        }});
      }
    });
    
    $("input[name='complete_cooling']").on("change",function(){
      if($(this).prop("checked")==true&&$(this).val()=="No"){
          $("#pop2").slideDown(200);
      }else{
          $("#pop2").slideUp(200);
          dis_ob(col,8,9);
       
      }
    });

    floatx("duration",0);
    
    $("input[name='duration']").on("change",function(){
      if(parseInt($(this).val())<=0||parseInt($(this).val())>=72){
          alert_fail({text : "กรุณากรอกข้อมูลในช่วง 0-71",
          button_func_1 :function(){
            $("input[name='duration']").val("");
        }});
      }
    });


    
    $("input[name='seizure']").on("change",function(){
      if($(this).prop("checked")==true&&$(this).val()=="Yes"){
          $("#pop3").slideDown(200);
      }else{
        $("input[name='seizure_other_main']").prop("checked",false);
        $("input[name='seizure_other']").slideUp(200);
          $("#pop3").slideUp(200);
          dis_ob(col,11,19);
      }
      cal_drug()
    });

    $("input[name='seizure_other_main']").on("change",function(){
      if($(this).prop("checked")==true){
          $("div[name='other_list']").slideDown(200);
          dis_ob(col,18,18);
      }else{
          $("#list").empty();
          add();
          $("div[name='other_list']").slideUp(200);
          //dis_ob(col,17,17);
      }
      cal_drug()
    });
    $("input[name='seizure_none']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='seizure_other']").slideUp(200);
        dis_ob(col,11,17);
      }
      cal_drug()
    });
    for(var i = 11;i<=16;i++){
      $("input[name='"+col[i]+"']").on("change",function(){
        cal_drug()
        if($(this).prop("checked")==true){
          dis_ob(col,18,18);
        }
      });
    }
    $("input[name='aEEG_or_EGG_done']").on("change",function(){
      if($(this).prop("checked")==true&&$(this).val()=="Yes"){
          $("#pop4").slideDown(200);
      }else{
        $("input[name='result_other_main']").prop("checked",false);
        $("input[name='result_other']").slideUp(200);
          $("#pop4").slideUp(200);
          dis_ob(col,21,24);
      }
    });

    $("input[name='result_other_main']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='result_other']").slideDown(200);
     
      }else{
        $("input[name='result_other']").val("");
        $("input[name='result_other']").slideUp(200);
      }
    });
    $("input[name='normal_background']").on("change",function(){
      if($(this).prop("checked")==true){
        dis_ob(col,22,22);
      }
    });
    $("input[name='abnormal']").on("change",function(){
      if($(this).prop("checked")==true){
        dis_ob(col,21,21);
      }
    });
    
}
function load(callback){
 //--------------5_12-----------------
 $.ajax({
  type: "POST",
  url: "https://techvernity.com/thainy/php/get_section5_12.php",
  data:{num:"5_12",hospital:localStorage.hospital},
  success: function (data) {
      var result3 = JSON.parse(data);
      console.log(result3);
      var status = "done";
      if(result3.length!=0){
          push_ob_data(result3[0]);

          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/get_section5_12_other.php",
            data:{hospital:localStorage.hospital},
            success: function (data) {
                var result2 = JSON.parse(data);
                console.log(result2);
                for(var i = 0;i<result2.length;i++){
                  if(i!=0){
                    add();
                  }
                  document.getElementsByName("otherx1")[i].value = result2[i]["other"];
                }
         

          var temp = result3[0];
          if(temp["severity_within_6_hr"]==""||temp["severity_the_worst"]==""||temp["receive_TH"]==""||temp["seizure"]==""||temp["aEEG_or_EGG_done"]==""){
            status = "not done";
          }
          console.log(status);
          if(temp["receive_TH"]=="Yes"){
            if(temp["cooling_start_at_date"]==""||temp["cooling_start_at_time"]==""||temp["after_birth"]==""||temp["temperature"]==""||temp["complete_cooling"]==""){
              status = "not done";
            }
          }
          console.log(status);
          if(temp["complete_cooling"]=="No"){
            if(temp["specify"]==""||temp["duration"]==""){
              status = "not done";
            }
          }
          console.log(status);
          if(temp["seizure"]=="Yes"){
            if(temp["start_anticonvulsant"]==""){
              status = "not done";
              var num = temp["start_anticonvulsant"];
              var n = num.toFixed(1);
            }
            console.log(status);
            if(temp["seizure_phenobarbital"]==""&&
            temp["seizure_phenytoin"]==""&&
            temp["seizure_midazolam"]==""&&
            temp["seizure_levetiracetam"]==""&&
            temp["seizure_topiramate"]==""&&
            temp["seizure_diazepam"]==""&&
            temp["seizure_other"]==""&&
            temp["seizure_none"]==""){
              status = "not done";
            }
          }
          console.log(status);

          if(temp["aEEG_or_EGG_done"]=="Yes"){
            if(temp["normal_background"]==""&&
            temp["abnormal"]==""&&
            temp["aEEG_seizure"]==""&&
            temp["result_other"]==""){
              status = "not done";
            }
          }
          console.log(status);
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/update_progress.php",
            data:{num:"5_12",status:status},
            success: function (data) {
                //alert(status);
                if(!!callback){
                  callback();
                }

            },
          });
        }
      })
     }
      else{
        status = "not done";
        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/update_progress.php",
          data:{num:"5_12",status:status},
          success: function (data) {
              //alert(status);
              if(!!callback){
                callback();
              }
          },
        });
      }
    //console.log(status + "จ้ะ");
      
      if(localStorage.hospital != ""){
        //alert(localStorage.hospital);
        $("input").prop("disabled",true);
        $("textArea").prop("disabled",true);
        $("select").prop("disabled",true);
        $(".save-btn").prop("hidden",true);
        $(".plus").prop("hidden",true);
    }
    }
  });
}

function save(){
  //--------------5_12-----------------
  var temp = get_ob_data(col);
  console.log(temp);
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/save_ob_section.php",
    data:{num:"5_12",data:temp},
    success: function (data) {
        
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_section5_12_other.php",
        data:{data:JSON.stringify(get_all_data("otherx"))},
        success: function (data) {
          load(function(){
            alert_success({
              text:"บันทึกสำเร็จ",
              button_func_1 : function(){
                change_page("section5.html","back");
              }
            });
          });
          
        }
      })
    }
    });
}

function add(){
  $("#list").append( ' <div class="bar-delete-list-input" hidden><div class="content_box">'
  + '<input class="input-section margin-btm" type="text" placeholder="ชื่อยา" name="otherx1" >'
  + '<div class="circle-red" onclick = "del(this)"><img src="img/delete.svg"></div>'
  + '</div></div>');

  $(".bar-delete-list-input").slideDown(200);
  $(".bar-delete-list-input input").unbind("change");
  $(".bar-delete-list-input input").unbind("keyup");
  $(".bar-delete-list-input input").on("change", function(){
    cal_drug()
  });
  $(".bar-delete-list-input input").on("keyup", function(){
    cal_drug()
  });
}

function del(ob){
  $(ob.parentNode.parentNode).slideUp(200, function(){
      document.getElementById("list").removeChild(ob.parentNode.parentNode);
      cal_drug()
  })
}

function cal_drug(){
  var sum_drug = 0
  for(var i = 11;i<=16;i++){
      if($("input[name='"+col[i]+"']").prop("checked")==true){
          sum_drug++;
      }
  }

 var otherx = get_all_data("otherx")[0]

 for(var i=0 ; i < otherx.length ; i++){
    if(!!otherx[i]){
        sum_drug++;
    }
 }

  $("input[name='start_anticonvulsant']").val(sum_drug)
}