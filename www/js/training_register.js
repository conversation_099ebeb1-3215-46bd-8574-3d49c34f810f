window.onload = function(){
    load();

}

function save_register(){
    var hospital_name = document.getElementById("hospital_name").value;
    //console.log(hospital_name);
  //  var position = document.getElementById("position").value;
  var position = "";
  var radios = document.getElementsByName('position');

for (var i = 0, length = radios.length; i < length; i++) {
    if (radios[i].checked) {
        position = radios[i].value;
        break;
    }
}

    console.log(position);
    var name = document.getElementById("name").value;
    var surname  = document.getElementById("surname").value;
    var dob = document.getElementById("dob").value;
    var tel =  document.getElementById("tel").value;
    var email =  document.getElementById("email").value;
    var username = document.getElementById("user").value;
    var password = document.getElementById("pass").value;
    var c_password = document.getElementById("c_pass").value;
    if(hospital_name == "" || name == "" || surname == "" || position== "" || tel == "" ){
        alert_fail({text: "คุณกรอกข้อมูลไม่ครบ", header: "ผิดพลาด"});
        return;
    }
    if(dob == "" || email == "" || username == "" || password == "" || c_password == ""){
        alert_fail({text: "รหัสผ่านของคุณไม่ตรงกัน", header: "ผิดพลาด"});
        return;
    }
    
    user_check(username,password,c_password,function(result){
        if(result){
            console.log("success");
            var dataarray = [];
            dataarray.push(username);
            dataarray.push(password);
            dataarray.push(name);
            dataarray.push(surname);
            dataarray.push(email);
            dataarray.push(tel);
             dataarray.push(hospital_name);
             dataarray.push(position);
             dataarray.push("certified");
             


             var data1 = JSON.stringify(dataarray);
            
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/training_add_new_user.php",
                data: {mydata : data1},
                success: function (data) {
                   if(data == "ok"){
                    alert_success({ 
                        text: "บันทึกสำเร็จ", 
                        button_func_1: function(){
                            change_page("training_register.html", "next");
                        }
                      });
                    }else{
                        return;
                    }
                }
              });
              

        }else{
            console.log("Error");
            //alert_fail({text: "เกิดข้อผิดพลาดของระบบโปรดลองใหม่อีกครั้ง", header: "ผิดพลาด"});
            
        }
    });
    
  
}
function user_check(username,password,c_password, func){
    
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/training_usercheck.php",
        data: { username : username},
        success: function (data) {
            if(data == "pass"){
                if( password.match("^(?=.*[a-z])(?=.*[A-Z])(?=.*?[0-9]).{6,12}$") ){
                    if(password == c_password){
                        console.log("0");
                        func(true);
                     }else{
                         alert_fail({text: "รหัสผ่านของคุณไม่ตรงกัน", header: "ผิดพลาด"});
                         console.log("1");
                         func(false);
                     }
                 }else{
                     alert_fail({text: "รหัสผ่านของคุณไม่ถูกต้อง (ต้องมีตัวอักษรภาษาอังกฤษ พิมพ์เล็ก พิมพ์ใหญ่และตัวเลข อย่างน้อย 1 ตัว ความยาว 6-12 ตัวอักษรยืนยันรหัสผ่าน (Password))", header: "ผิดพลาด"});
                     console.log("2");
                     func(false);
             
                 }
            }else{
                alert_fail({text: "ชื่อผู้ใช้นี้ถูกใช้งานเเล้ว", header: "ผิดพลาด"});
                console.log("3");
                func(false);
            }
        
            console.log("4");
        }
      });
     // console.log("5");
  
}
function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_hospital.php",
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            $("select[name='hospital_name']").html(' <option value="" selected disabled>เลือกโรงพยาบาล</option>');
            $("select[name='discharge_hospital']").html(' <option value="" selected disabled>เลือกโรงพยาบาล</option>');
 
            for(var i = 0;i<result.length;i++){
                $("select[name='hospital_name']").append(' <option value="'+result[i]["hospital_name"]+'">'+result[i]["hospital_name"]+' ('+result[i]["hospital_number"]+')</option>');
                $("select[name='discharge_hospital']").append(' <option value="'+result[i]["hospital_name"]+'">'+result[i]["hospital_name"]+' ('+result[i]["hospital_number"]+')'+'</option>');
           
            }

           
        },
      });
}