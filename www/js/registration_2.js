  window.onload = function(){
  load();
 // $("input").prop("disabled",true);
 // $("textArea").prop("disabled",true);
 // $("select").prop("disabled",true);
 // $(".save-btn").prop("hidden",true);
}
function load(){
  
  document.getElementById("ID").disabled = true;
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_patient_data.php",
    success: function (data) {
       var temp = JSON.parse(data);
       console.log("temp:"+ temp[0]);

       var checker_string = temp[0].infant_id;

       document.getElementById("ID").value = temp[0].infant_id;
       if(checker_string[checker_string.length-1] == "ε"){
        var text =temp[0].infant_id;
       // console.log("text"+text);
       document.getElementById("ID").value =  text.substring(0, text.length-1);
        document.getElementById("ID").disabled = false;
       
        var temp15 =
       " <div class='save-btn' onclick = 'set_to_permanent()' >"+
        "<span class='set_center'>Change to permanent infant ID</span>"+
         "</div>";
         document.getElementById("getpermanent").innerHTML  += temp15;
        
      }
      
      document.getElementById("date").value = temp[0].DOB;
      $("#date").trigger("change")
       if(temp.length!=0){
       // push_ob_data(temp[0]);
        
        if(temp[0].sex=="male"){
          document.getElementById("M1").className = "choose-fm";
          document.getElementById("F1").className = "unchoose-fm";
        }else if(temp[0].sex=="female"){
          document.getElementById("M1").className = "unchoose-fm";
          document.getElementById("F1").className = "choose-fm";
        }
        else{
          document.getElementById("M1").className = "unchoose-fm";
          document.getElementById("F1").className = "unchoose-fm";
        }

        if(temp[0].mother_id !="" && temp[0].mother_id != undefined ){
          $('#check-idcard').slideDown(200);
          document.getElementById("check-idcard").value = temp[0].mother_id;
          document.getElementById("idcard").checked = true;
          
        }
        if(temp[0].mother_passport!="" && temp[0].mother_passport != undefined ){
          $('#check-passport').slideDown(200);
          document.getElementById("check-passport").value = temp[0].mother_passport;
          document.getElementById("idpass").checked = true;
        
        }
      
       }
      
       document.getElementById("name").value  =   temp[0].fullname;
       document.getElementById("HN").value  =   temp[0].HN;
       document.getElementById("ethnic").value  =   temp[0].ethnic;
       document.getElementById("mothername").value  =   temp[0].mother_fullname;
       document.getElementById("address").value  =   temp[0].mother_address;
       document.getElementById("tel").value  =   temp[0].mother_tel;

        document.getElementById("contactName").value =  temp[0].contact_person_name;
        document.getElementById("contactRelate").value =  temp[0].	relation;
        document.getElementById("contactTel").value =  temp[0].contact_person_tel;
        document.getElementById("contactOther").value =  temp[0].other_contact;
       
        
    },
  });
  if(localStorage.hospital != ""){
   // alert(localStorage.hospital);

    $("input").prop("disabled",true);
    $("textArea").prop("disabled",true);
    $("select").prop("disabled",true);
    $(".save-btn").prop("hidden",true);
    $(".plus").prop("hidden",true);
    $("#M1").attr("onclick","");
    $("#F1").attr("onclick","");
  }

}

$(document).ready(function(){

  $('#idcard').on("click",function(){
      if($('#idcard').prop("checked") == true){
          $('#check-idcard').slideDown(200);
      }
      else if($('#idcard').prop("checked") == false){
          $('#check-idcard').slideUp(200);
      }
  });

  $('#idpass').on("click",function(){
      if($('#idpass').prop("checked") == true){
          $('#check-passport').slideDown(200);
      }
      else if($('#idpass').prop("checked") == false){
          $('#check-passport').slideUp(200);
      }
  });
 
});
var name = document.getElementById("name").value;
var hn = document.getElementById("HN").value;
//var id = document.getElementById("ID").value;
var DOB = document.getElementById("date").value;
var  sex1 = '';
var ethnic = document.getElementById("ethnic").value;
var mothername = document.getElementById("mothername").value;
var motherID = "";
if(document.getElementById("idcard").ischecked){
  motherID = document.getElementById("check-idcard").value;
}
var motherpass = "";
if(document.getElementById("idpass").ischecked){
  motherpass = document.getElementById("check-passport").value;
}
var address = document.getElementById("address").value;
var mothertel = document.getElementById("tel").value;
var cname = document.getElementById("contactName").value;
var crelation = document.getElementById("contactRelate").value;
var ctel = document.getElementById("contactTel").value;
var cother = document.getElementById("contactOther").value;
        
        /*
        document.getElementById("M1").classList.remove('choose-fm');
        document.getElementById("M1").classList.add('unchoose-fm');
        document.getElementById("F1").classList.remove('choose-fm');
        document.getElementById("F1").classList.add('unchoose-fm');
        */
        //var gender_status = "";
        //istemp
          function choose(sex){
            console.log(sex+" "+sex1);
              if(sex == 'male' ){
      if(sex1 == 'male'){
        document.getElementById("M1").classList.remove('choose-fm');
        document.getElementById("M1").classList.add('unchoose-fm');
        sex1 = '';
        return;
      }
              document.getElementById("F1").classList.remove('choose-fm');
              document.getElementById("F1").classList.add('unchoose-fm');
              document.getElementById("M1").classList.remove('unchoose-fm');
              document.getElementById("M1").classList.add('choose-fm');
              sex1 = 'male';
              
              }
         
              if(sex == 'female' ){
                if(sex1 == 'female'){
                  
                  document.getElementById("F1").classList.remove('choose-fm');
                  document.getElementById("F1").classList.add('unchoose-fm');
                  sex1 = '';
                  return;
                }
                document.getElementById("M1").classList.remove('choose-fm');
                document.getElementById("M1").classList.add('unchoose-fm');
                document.getElementById("F1").classList.remove('unchoose-fm');
                document.getElementById("F1").classList.add('choose-fm');
                sex1 = 'female';
              }
        
          }
          function updateandsend(){
            var name = document.getElementById("name").value;
            var hn = document.getElementById("HN").value;
            var id = document.getElementById("ID").value;
            var DOB = document.getElementById("date").value;
           
            var ethnic = document.getElementById("ethnic").value;
            var mothername = document.getElementById("mothername").value;
            var motherID = "";
            if(document.getElementById("idcard").checked){
              motherID = document.getElementById("check-idcard").value;
            }
            var motherpass = "";
            if(document.getElementById("idpass").checked){
              motherpass = document.getElementById("check-passport").value;
            }
            var address = document.getElementById("address").value;
            var mothertel = document.getElementById("tel").value;
            var cname = document.getElementById("contactName").value;
            var crelation = document.getElementById("contactRelate").value;
            var ctel = document.getElementById("contactTel").value;
            var cother = document.getElementById("contactOther").value;
            if((document.getElementById("idcard").checked)){
              if(motherID == ""){
                alert_fail({
                  text: "กรอกข้อมูลไม่ครบ ",
                });
                return;
              }
            }else{
              motherID = "";
            }
               //session2.2
          if((document.getElementById("idpass").checked)){
              if(motherpass == ""){
                alert_fail({
                  text: "กรอกข้อมูลไม่ครบ",
                });
                return;
              }
            }else{
              motherpass = "";
            }
           // console.log("clear"+sex1+":"+document.getElementById("idcard").checked +motherID+":"+document.getElementById("idpass").checked +motherpass);
            var dateObj = new Date();
            var month = dateObj.getUTCMonth() + 1; //months from 1-12
            var day = dateObj.getUTCDate();
            var year = dateObj.getUTCFullYear();
           var dateCreate = year+"-"+month+"-"+day;
           console.log(dateCreate);
           var dataarray = [];
           dataarray.push(DOB);
           dataarray.push(sex1);
           dataarray.push(name);
           dataarray.push(hn);
           dataarray.push(ethnic);
           dataarray.push(mothername);
           dataarray.push(motherID);
           dataarray.push(motherpass);
           dataarray.push(address);
            dataarray.push(mothertel);
           dataarray.push(cname);
           dataarray.push(crelation);
            dataarray.push(ctel);
            dataarray.push(cother);
            console.log(dataarray);
            var dataJ = JSON.stringify(dataarray);
            $.ajax({
              type: "POST",
              url: "https://techvernity.com/thainy/php/update_register_except_ID.php",
            data: {
              data : dataJ
            },
            success: function (data) {
           if(data == 'ok'){
            alert_success({text:"บันทึกสำเร็จ", button_func_1: function(){
      
              change_page("patient_display.html", "next");
            }});
           // change_page("patient_display.html", "next");
           }
            
             // window.location = link;
            
            },
            });
          }
function update_registerstatus(TNR,hospital2,id_temp,id_per,link2){
console.log(TNR + " "+id_temp,+" "+id_per);

$.ajax({
  type: "POST",
  url: "https://techvernity.com/thainy/php/register_progress.php",
data: {
  TNR : TNR,
  temporary : id_temp,
  permanent : id_per
},
success: function (data) {
console.log("php:"+data+"link:"+link2);
  change_page(link2, "next");
 // window.location = link;

},
});
}
function checkvalue(){
var   limit = document.getElementById("ID").value;
if(limit.match(/^[0-9]+$/) != null){
  console.log("ok");
}else{
 
  alert_fail({
    text:"สำหรับตัวเลขเท่านั้น",button_func_1:function(){
      document.getElementById("ID").value = "";
    }
  });
 
}
if(limit.length >13){
  limit = limit.substring(0,13);
  document.getElementById("ID").value = limit;
  alert_fail({
    text:"หมายเลขบัตรประชาชน ต้องไม่เกิน13หลักเเละต้องเป็นตัวเลขเท่านั้น"
  });
}
document.getElementById("ID").value = limit;
}


function set_to_permanent(){
  var id = document.getElementById("ID").value;

  confirm_o({
    text: "เมื่อกดยืนยัน หมายเลขนี้ถูกตั้งเป็นหมายเลขถาวร ไม่สามารถเปลี่ยนเเปลงได้อีก",
    header: "ยืนยันเลขประจำตัวของเด็ก",
    button_text_1: "ตกลง",
    button_text_2: "ยกเลิก",
    button_func_1: function(){
      if(id == '' || id == "" || id == undefined){
        alert_success({text:"กรุณากรอกให้ครบ"});
        return;
      }
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/checker_id.php",
        data: {
          id : id,
        //  id_p : id_possible
        },
        success: function (data) {
          if(data == 'pass'){
            $.ajax({
              type: "POST",
              url: "https://techvernity.com/thainy/php/update_set_permanentID.php",
              data: {id : id},
              success: function (data) {
              
                alert_success({text:"บันทึกสำเร็จ", button_func_1: function(){
      
                  window.location.reload();
                }});
      
              }
            });
          }else{
            alert_fail({
              text:"หมายเลข Infant’s ID Number ซ้ำ",
            });
            //sessionStorage.detail = "เลขทะเบียซ้ำ"
            //window.location="registry_criteria5_1.html";
          }
          
        },
      });
   
    }
    
  });
}
