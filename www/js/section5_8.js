var col = ["reach_120_ml","start_reach_120_ml","age","status","last_modified_username","last_modified_date"]

window.onload = function(){
    //alert("test");
    get_birth_date(function(){
        timeline_min_max("birth_date","now");
      });
    setting();
    load();
    
}

function setting(){
    $("input[name='start_reach_120_ml']").on("change",function(){
        if($(this).val()!=""&&!!birth_date_data){
            $("input[name='age']").val("Age " + diffDate(birth_date_data,$(this).val(),"d")+" day(s)");
        }else{
            $("input[name='age']").val("");
        }
    });
  $("input[name='reach_120_ml']").on("change",function(){
    if($("input[name='reach_120_ml']:checked").val() == "No"){
      $("input[name='start_reach_120_ml']").prop("disabled",true);
     // $("input[name='age']").prop("disabled",true);
      $("input[name='start_reach_120_ml']").val("").trigger("change");
      $("input[name='age']").val("");
    }
    else if($("input[name='reach_120_ml']:checked").val() == "Yes"){
      $("input[name='start_reach_120_ml']").prop("disabled",false);
    //  $("input[name='age']").prop("disabled",false);
    }

  });
}


function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"5_8",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result.length);
            var status = "done";
            if(result.length!=0){
                push_ob_data(result[0]);
                var temp = result[0];
                if(temp["reach_120_ml"]==""){
                    status = "not done";
                }

                if(temp["reach_120_ml"]=="Yes"&&(temp["start_reach_120_ml"]==""||temp["age"]=="")){
                    status = "not done";
                }
            }
            else{
                status = "not done";
            }
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/update_progress.php",
                data:{num:"5_8",status:status},
                success: function (data) {
                    //alert(status);
                },
              });
            if(localStorage.hospital != ""){
                //alert(localStorage.hospital);
                $("input").prop("disabled",true);
                $("textArea").prop("disabled",true);
                $("select").prop("disabled",true);
                $(".save-btn").prop("hidden",true);
                $(".plus").prop("hidden",true);
            }
        },
      });
}
function save(){

    var temp = get_ob_data(col);
    console.log(temp);

    var complete = "not done";
    //console.log(document.getElementsByName("ino1")[0].checked);
    if(document.getElementsByName("reach_120_ml")[0].checked){
      complete = "done";
    }else if(document.getElementsByName("reach_120_ml")[1].checked){
        if(document.getElementsByName("start_reach_120_ml")[0].value != "" && document.getElementsByName("age")[0].value != ""  ){
            complete = "done";
        }
    }
    console.log(complete);
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/update_process_section.php",
        data:{data:complete,num:"5_8"},
        success: function (data) {
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/save_ob_section.php",
                data:{data:temp,num:"5_8"},
                success: function (data) {
                    var result = JSON.parse(data);
                    console.log(result);
                    load();
                    alert_success({
                        text:"บันทึกสำเร็จ",
                        button_func_1 : function(){
                            change_page("section5.html","back");
                        }
                    });
                },
              });
        }
      });
/*
     $.ajax({
         type: "POST",
         url: "https://techvernity.com/thainy/php/save_ob_section.php",
         data:{data:temp,num:"5_8"},
         success: function (data) {
             var result = JSON.parse(data);
             console.log(result);
             alert("บันทึกสำเร็จ");
             window.location.reload();
         },
       });
       */
    
}