$(document).ready(function(){
    //dummy
    load("certified")

    
    $("#waiting").on("click",function(){
        load("certified");
        $(".footer-text").removeClass("text-green");
        $(".footer-text").addClass("text-gray");
        $(".box img").attr("src","img/waitgray.svg");
        $("#waiting .footer-text").removeClass("text-gray");
        $("#waiting .footer-text").addClass("text-green");
        $("#waiting img").attr("src","img/waitgreen.svg");
        $("#approve img").attr("src","img/approvegray.svg");
        $("#reject img").attr("src","img/rejectgray.svg");
    })

    $("#approve").on("click",function(){
        load("approved");
        $(".footer-text").removeClass("text-green");
        $(".footer-text").addClass("text-gray");
        $(".box img").attr("src","img/approvegray.svg");
        $("#approve .footer-text").removeClass("text-gray");
        $("#approve .footer-text").addClass("text-green");
        $("#approve img").attr("src","img/approvegreen.svg");
        $("#waiting img").attr("src","img/waitgray.svg");
        $("#reject img").attr("src","img/rejectgray.svg");
    })

    $("#reject").on("click",function(){
        load("reject");
        $(".footer-text").removeClass("text-green");
        $(".footer-text").addClass("text-gray");
        $(".box img").attr("src","img/rejectgray.svg");
        $("#reject .footer-text").removeClass("text-gray");
        $("#reject .footer-text").addClass("text-green");
        $("#reject img").attr("src","img/rejectgreen.svg");
        $("#waiting img").attr("src","img/waitgray.svg");
        $("#approve img").attr("src","img/approvegray.svg");
    })
})

function load(status){
  document.getElementById("list").innerHTML = "";

  $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/get_user_list.php",
      data:{status:status},
      success: function (data) {
          var result = JSON.parse(data);
          console.log(result);

          for(var i = 0 ; i < result.length; i++){
              add(result[i].username, result[i].firstname + " " + result[i].lastname ,status);
          }
          document.getElementById("list").innerHTML += '<div class="number-active">จำนวนบัญชีผู้ใช้รอการอนุมัติ ' + result.length + ' คน</div>';

          $(".each-list").on("click",function(){
            sessionStorage.authorized_username = $(this).attr("name");
            change_page("authorize_detail.html", "next");
           // window.location = "authorize_detail.html"
          })
      },
    });
}
function add(username,fullname,status){
  var status_text = "";

  if(status == "certified"){
    status_text = "รออนุมัติ"
  }
  else if(status == "approved"){
    status_text = "อนุมัติแล้ว"
  }
  else if(status == "reject"){
    status_text = "ถูกปฎิเสธ"
  }

  var text = '<div class="each-list" name="'+username+'">'
                         +'<div class="name-patient">'+fullname+'</div>'
                         //+'<div class="tn-patient">TN#: '+TNR+'</div>'
                         +'<div class="save-btn">'+status_text+'</div>'
                         +'<img src="img/next2.svg">'
                     +'</div>';

  document.getElementById("list").innerHTML += text;

  $(".patient_display").on("click",function(){
    change_page("patient_display.html", "next");
     // window.location = "patient_display.html"
  })
}