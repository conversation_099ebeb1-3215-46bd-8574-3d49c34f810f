var col = ["delivery_mode","vaginal","apgar_score","1_min_score","5_min_score","10_min_score","resuscitation","CPAP","PPV","intubation","chest_compression","epinephrine","cord_blood_pH","cord_blood_pCO2","cord_blood_HCO2","cord_blood_BE","delayed_cord_clamping"];

window.onload = function(){
    setting();
   load();
  
}
function setting(){
    $("input[name='delivery_mode']").on("change",function(){
        if($(this).val()=="Vaginal"){
            $("#pop1").prop("hidden",false);
        }else{
            $("#pop1").prop("hidden",true);
        }

    });
    $("input[name='resuscitation']").on("change",function(){
        if($(this).val()=="Yes"){
            $("#pop2").prop("hidden",false);
        }else{
            $("#pop2").prop("hidden",true);
        }

    });
}
function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_section1.php",
        data:{num:"3"},
        success: function (data) {
            var result2 = JSON.parse(data);
            //console.log(result2[0]);
            if(result2.length!=0){
                push_ob_data(result2[0]);
            }

            // var array = [];
            // var col = ["first","second","third","fourth"];
            // array.push([1,2,3,4]);
            // array.push([5,6,7,8]);
            // console.log(array);
            // console.log(array_to_ob(col,array));
        },
      });
}
function save(){
    var temp = get_ob_data(col);
    console.log(temp);

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/save_section_ob.php",
        data:{data:temp},
        success: function (data) {
            alert_success({
                text: "บันทึกสำเร็จ"
            });
        },
      });
}