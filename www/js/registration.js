$(document).ready(function(){
    timeline_min_max(null,"now")
    //info
    $("#info1").click(function(){ 
      show_info("เลือก Permanent เมื่อเลขที่กรอกเป็นเลขบัตรประชาชนของทารก โดยเมื่อ Submit แล้วจะไม่สามารถเปลี่ยนแปลงได้"
                  +"<br/><br/>ถ้ายังไม่มีเลขบัตรประจำตัวประชาชนของทารก ให้ใช้เลข Hospital Number แทน และไม่เลือก Permanent"
                  +"<br/><br/><span style='color:red'>*หากยังไม่เลือก Permanent จะไม่สามารถ Submit ข้อมูลของทารกคนนี้ได้</span>") 
     })

    $('#idcard').on("click",function(){
        if($('#idcard').prop("checked") == true){
            $('#check-idcard').slideDown(200);
        }
        else if($('#idcard').prop("checked") == false){
            $('#check-idcard').slideUp(200);
        }
    });

    $('#idpass').on("click",function(){
        if($('#idpass').prop("checked") == true){
            $('#check-passport').slideDown(200);
        }
        else if($('#idpass').prop("checked") == false){
            $('#check-passport').slideUp(200);
        }
    });
    
});
          var name = document.getElementById("name").value;
          var hn = document.getElementById("HN").value;
          var id = document.getElementById("ID").value;
          var DOB = document.getElementById("date").value;
          var  sex1 = '';
          var ethnic = document.getElementById("ethnic").value;
          var mothername = document.getElementById("mothername").value;
          var motherID = "";
          if(document.getElementById("idcard").ischecked){
            motherID = document.getElementById("check-idcard").value;
          }
          var motherpass = "";
          if(document.getElementById("idpass").ischecked){
            motherpass = document.getElementById("check-passport").value;
          }
          var address = document.getElementById("address").value;
          var mothertel = document.getElementById("tel").value;
          var cname = document.getElementById("contactName").value;
          var crelation = document.getElementById("contactRelate").value;
          var ctel = document.getElementById("contactTel").value;
          var cother = document.getElementById("contactOther").value;
          document.getElementById("M1").classList.remove('choose-fm');
          document.getElementById("M1").classList.add('unchoose-fm');
          document.getElementById("F1").classList.remove('choose-fm');
          document.getElementById("F1").classList.add('unchoose-fm');
          //var gender_status = "";
          //istemp
          var istemp = true;
          $('#Permanent').on("click",function(){
            if($('#Permanent').prop("checked") == true){
              console.log("permanent");
              istemp = false;
            }else{
              istemp = true;
            }
          });

            function choose(sex){
              console.log(sex+" "+sex1);
                if(sex == 'male' ){
        if(sex1 == 'male'){
          document.getElementById("M1").classList.remove('choose-fm');
          document.getElementById("M1").classList.add('unchoose-fm');
          sex1 = '';
          return;
        }
                document.getElementById("F1").classList.remove('choose-fm');
                document.getElementById("F1").classList.add('unchoose-fm');
                document.getElementById("M1").classList.remove('unchoose-fm');
                document.getElementById("M1").classList.add('choose-fm');
                sex1 = 'male';
                
                }
           
                if(sex == 'female' ){
                  if(sex1 == 'female'){
                    
                    document.getElementById("F1").classList.remove('choose-fm');
                    document.getElementById("F1").classList.add('unchoose-fm');
                    sex1 = '';
                    return;
                  }
                  document.getElementById("M1").classList.remove('choose-fm');
                  document.getElementById("M1").classList.add('unchoose-fm');
                  document.getElementById("F1").classList.remove('unchoose-fm');
                  document.getElementById("F1").classList.add('choose-fm');
                  sex1 = 'female';
                }
          
            }
            function checkandSend(){
              $(".save-btn").hide();

              var name = document.getElementById("name").value;
          var hn = document.getElementById("HN").value;
          var id = document.getElementById("ID").value;
          var DOB = document.getElementById("date").value;
         
          var ethnic = document.getElementById("ethnic").value;
          var mothername = document.getElementById("mothername").value;
          var motherID = "";
          if(document.getElementById("idcard").checked){
            motherID = document.getElementById("check-idcard").value;
          }
          var motherpass = "";
          if(document.getElementById("idpass").checked){
            motherpass = document.getElementById("check-passport").value;
          }
          var address = document.getElementById("address").value;
          var mothertel = document.getElementById("tel").value;
          var cname = document.getElementById("contactName").value;
          var crelation = document.getElementById("contactRelate").value;
          var ctel = document.getElementById("contactTel").value;
          var cother = document.getElementById("contactOther").value;
          var TNR = 00000000;
                    //session1
          var id_possible = "";
          if(id == "" ){
            console.log("session1:"+name+","+hn+","+id+","+DOB+","+ethnic+sex1);
            alert_fail({
              text:"โปรดกรอก Infant ID",
             
            });
            $(".save-btn").show();
            return;
        }
          if(istemp){
            id += "ε";
          }
          /*
              if(istemp){
                id_possible = id;
                id += "ε";
              }else{
                id_possible = id;
                id_possible += "ε";
              }
              */
   
             //session2
             /*
             if(mothername == "" || address == "" || mothertel == ""){
                console.log("session2:"+mothername+","+address+","+mothertel);
              alert("กรอกข้อมูลไม่ครบ session2");
              return;
          }
          */
        //session2.1
        if((document.getElementById("idcard").checked)){
            if(motherID == ""){
              alert_fail({
                text: "กรอกข้อมูลไม่ครบ",
              });
              $(".save-btn").show();
              return;
            }
          }else{
            motherID = "";
          }
             //session2.2
        if((document.getElementById("idpass").checked)){
            if(motherpass == ""){
              alert_fail({
                text: "กรอกข้อมูลไม่ครบ",
              });
              $(".save-btn").show();
              return;
            }
          }else{
            motherpass = "";
          }
            //session3
            /*
            if(cname == "" || crelation == "" || ctel == ""  || cother == ""){
                console.log("session3:"+cname+","+crelation+","+ctel+","+cother);
              alert("กรอกข้อมูลไม่ครบ session3");
              return;
          }
          */
          console.log("clear"+sex1+":"+document.getElementById("idcard").checked +motherID+":"+document.getElementById("idpass").checked +motherpass);
          var dateObj = new Date();
          var month = dateObj.getUTCMonth() + 1; //months from 1-12
          var day = dateObj.getUTCDate();
          var year = dateObj.getUTCFullYear();
         var dateCreate = year+"-"+month+"-"+day;
         console.log(dateCreate);
         
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/TNRgennerate.php",
            data: {
                regist_date: dateCreate,
                hospital: hn
            },
            success: function (data) {
                var result = JSON.parse(data);
              
                 TNR = result;
               
                 TNR = parseInt(TNR);
                if(TNR == 0){
                  $(".save-btn").show(); 
                  alert_fail({text:"กรุณาลองอีกครั้ง"});

                }else{
                //  sessionStorage.flow1 = a;
                //  sessionStorage.flow2 = b;
                //  sessionStorage.flow3 = c;
                var datasent = [];
                 datasent.push(TNR);
                 datasent.push(sessionStorage.flow1);
                 datasent.push(sessionStorage.flow2);
                datasent.push(sessionStorage.flow3);
                var datasent = JSON.stringify(datasent);
                $.ajax({
                  type: "POST",
                  url: "https://techvernity.com/thainy/php/set_register_criteria.php",
                  data:{
                      isdata:datasent
                  },
                  success: function (data) {
                   
                  },
                });


                 var dataarray = [];
                // var nameCut = name.toString().split(" ");
                // var mothernameCut = mothername.toString().split(" ");
                 var created_date = Date(Date.now()).toString();
                //sessionStorage.hospital = "vernity  ";
                sessionStorage.TNR = "TN#:"+TNR;
                sessionStorage.HN = "HN:"+hn;
                sessionStorage.mothername = "มารดา:"+mothername;
                sessionStorage.Cname = "\""+name+"\"";
                sessionStorage.TNR = TNR;

            

                dataarray.push(TNR);
                dataarray.push("");
                dataarray.push(name);
                dataarray.push(hn);
                dataarray.push(id);
                dataarray.push(DOB);
                 dataarray.push(sex1);
                dataarray.push(ethnic);
                dataarray.push(mothername);
                dataarray.push(motherID);
                dataarray.push(motherpass);
                dataarray.push(address);
                 dataarray.push(mothertel);
                dataarray.push(cname);
                dataarray.push(crelation);
                 dataarray.push(ctel);
                 dataarray.push(cother);
                 dataarray.push("active");
               //  dataarray.push(created_date);

                 console.log(dataarray);

         
                var data1 = JSON.stringify(dataarray);
               if(istemp){
                $.ajax({
                  type: "POST",
                  url: "https://techvernity.com/thainy/php/add_new_patient.php",
                  data: {
                    TNR : TNR,
                    mydata : data1
                  },
                  success: function (data) {
                    //console.log(data);    
                   

                    // set noti 30 day 
                   // set_noti_30d(TNR);
                  get_user_id(TNR,"1y");
                   // cancel_noti("20180030014","30d");
                   console.log("data id : "+id);
                   update_registerstatus(TNR,hn,id,"","registry_criteria4_1.html");
                   
                    console.log("OK");

                  },
                });
               }

               /////////////////////////////
               else{
                $.ajax({
                  type: "POST",
                  url: "https://techvernity.com/thainy/php/checker_id.php",
                  data: {
                    id : id,
                    TNR : TNR
                  //  id_p : id_possible
                  },
                  success: function (data) {
                    console.log(data);
                    if(data == "pass"){
                      $.ajax({
                        type: "POST",
                        url: "https://techvernity.com/thainy/php/add_new_patient.php",
                        data: {
                          TNR : TNR,
                          mydata : data1
                        },
                        success: function (data) {
                          //console.log(data);    
                         
    
                          // set noti 30 day 
                         // set_noti_30d(TNR);
                        get_user_id(TNR,"1y");
                         // cancel_noti("20180030014","30d");
                         console.log("data id : "+id);
                      
                    
                          update_registerstatus(TNR,hn,"",id,"registry_criteria4_1.html");
                
                         
                     
                          console.log("OK");
    
                        },
                      });
                    }else{
                      //ajax("del_concurrence.php",{TNR:TNR});
                      alert_fail({
                        text:"หมายเลข Infant’s ID Number ซ้ำ",

                        button_func_1: function(){
                          $(".save-btn").show();
                        }
                      });
                      //sessionStorage.detail = "เลขทะเบียซ้ำ"
                      //window.location="registry_criteria5_1.html";
                    }
             
  
                    },
                  });
               }
          ///////////////////////////////
              

              }
                  
            },

          });
        


        }
var flow1;
var flow2;
var flow3;
function select_criteria1(choice){
  flow1 = "";
 flow1 = choice.toString();
 switch(flow1){
   case "NICU" :{
    document.getElementById("NICU").className  = "ward-choose";
    document.getElementById("sick").className  = "ward-choice";
    document.getElementById("other").className  = "ward-choice"; 
    break;
  }
    case "sick" :{
      document.getElementById("NICU").className  = "ward-choice";
      document.getElementById("sick").className  = "ward-choose";
      document.getElementById("other").className  = "ward-choice"; 
      break;

    }
    case "other" :{
      document.getElementById("NICU").className  = "ward-choice";
      document.getElementById("sick").className  = "ward-choice";
      document.getElementById("other").className  = "ward-choose"; 
      break;

    }
 }
  console.log(flow1);
}
function to_next1(){
  if(flow1 == "" || flow1 == undefined){
    alert_fail({
      text:"กรอกไม่ครบ",
    });
    return;
  }
  if(flow1 == "other"){
    sessionStorage.flow1 = flow1;
    sessionStorage.flow2 = "";
    sessionStorage.flow3 = "";
    calculator_data(sessionStorage.flow1,sessionStorage.flow2,sessionStorage.flow3);
   
  }else{
    change_page("registry_criteria2.html", "next");
  }
  sessionStorage.flow1 = flow1;
 
  //window.location.href = "registry_criteria2.html";
  console.log(flow1);
}
function to_next2(){
  flow2 = "";
  
  //console.log("hello");
  flow2 = document.getElementsByName("PNA")[0].checked;
  flow2_1 = document.getElementsByName("PNA")[1].checked;
  console.log(flow2);
  console.log(flow2_1);
  
  if(!(flow2  || flow2_1)){
    alert_fail({
      text:"กรอกไม่ครบ"
    });
    return;
  }
  if(flow2){
    sessionStorage.flow2 = "<15";
    change_page("registry_criteria3.html", "next");
  }else{
    sessionStorage.flow2 = ">=15";
    sessionStorage.flow3 = "";
    calculator_data(sessionStorage.flow1,sessionStorage.flow2,sessionStorage.flow3);
  }
  console.log(sessionStorage.flow2);
 
 // window.location.href = "registry_criteria3.html";
  console.log(flow2);
  
}

function to_next3(){
  flow3 = "";
  
  checkbox = document.getElementsByName("checkbox1");
    if(checkbox[0].checked == true){
      flow3 = flow3+"GA < 32 weeks GA;";
    }
    if(checkbox[1].checked == true){
      flow3 = flow3+"BW < 1,500g;";
    }
    if(checkbox[2].checked == true){
      flow3 = flow3+"HIE;";
    }
    if(checkbox[3].checked == true){
      flow3 = flow3+"Major anomalies;";
    }
    /*
    if(checkbox[4].checked == true){
      flow3 = flow3+"other;";
    }
    */
    if(flow3 == ""){
      alert_fail({
        text:"กรอกไม่ครบ"
      });
      return;
    }
    flow3 = flow3.substring(0,flow3.length-1)
    sessionStorage.flow3 = flow3;
    console.log(flow3);
    calculator_data(sessionStorage.flow1,sessionStorage.flow2,sessionStorage.flow3);
}
function calculator_data(page1,page2,page3){
  sessionStorage.flow1 = undefined;
  sessionStorage.flow2 = undefined;
  sessionStorage.flow3 = undefined;
    console.log(page1);
    console.log(page2);
    console.log(page3);
    var canpass = false;
    var problem1 = false;
    var problem2 = false;
    var problem3 = false;
    if(page1 == "other"){
      page1 = "อื่นๆ";
    }
    if(page1 == "NICU" || page1 == "sick"){
      problem1 = true;
    }
    if(page2 == "<15"){
      problem2 = true;
    }
    if(page3 != ""){
      problem3 = true;
    }
    console.log(problem1);
    console.log(problem2);
    console.log(problem3);
   
    if(problem1 && problem2 && problem3){
   
      canpass = true;
    }

    if(canpass){
      redirect_pass(page1,page2,page3);
     
    }
    else{
      redirect_fail(page1,page2,page3);
    }
}

function redirect_pass(a,b,c){
  sessionStorage.flow1 = a;
  sessionStorage.flow2 = b;
  sessionStorage.flow3 = c;
  var dateObj = new Date();
  var month = dateObj.getUTCMonth() + 1; //months from 1-12
  var day = dateObj.getUTCDate();
  var year = dateObj.getUTCFullYear();
 var dateCreate = year+"-"+month+"-"+day;

  
  // var hn = "001";
  // $.ajax({
  //   type: "POST",
  //   url: "https://techvernity.com/thainy/php/TNRgennerate.php",
  //   data: {
  //       regist_date: dateCreate,
  //       hospital: hn
  //   },
  //   success: function (data) {
  //     console.log("data:"+data);
  //     var datasent = [];
  //     datasent.push(data);
  //     datasent.push(a);
  //     datasent.push(b);
  //    datasent.push(c);
  //    var datasent = JSON.stringify(datasent);

  //     $.ajax({
  //       type: "POST",
  //       url: "https://techvernity.com/thainy/php/set_register_criteria.php",
  //       data:{
  //           isdata:datasent
  //       },
  //       success: function (data) {
  //         console.log(data);
          change_page("registry_criteria4.html", "next");
          //window.location.href = "registry_criteria4.html";
    //     },
    //   });
    // },

  // });
  /*
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/set_register_criteria.php",
    data:{
        
    },
    success: function (data) {
      console.log(data);
      window.location.href = "registry_criteria4.html";
    },
  });
*/
  
 
}
function redirect_fail(a,b,c){
  sessionStorage.flow1 = a;
  sessionStorage.flow2 = b;
  sessionStorage.flow3 = c;
  change_page("registry_criteria5.html", "next");
  //window.location.href = "registry_criteria5.html";
}


function update_registerstatus(TNR,hospital2,id_temp,id_per,link2){
  console.log(TNR + " "+id_temp,+" "+id_per);
  
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/register_progress.php",
  data: {
    TNR : TNR,
    temporary : id_temp,
    permanent : id_per
  },
  success: function (data) {
console.log("php:"+data+"link:"+link2);
    change_page(link2, "next");
   // window.location = link;

  },
  });
}
function checkvalue(){
  var   limit = document.getElementById("ID").value;
  if(limit.match(/^[0-9]+$/) != null){
    console.log("ok");
  }else{
   
    alert_fail({
      text:"สำหรับตัวเลขเท่านั้น",button_func_1:function(){
        document.getElementById("ID").value = "";
      }
    });
   
  }
  if(limit.length >13){
    limit = limit.substring(0,13);
    document.getElementById("ID").value = limit;
    alert_fail({
      text:"หมายเลขบัตรประชาชน ต้องไม่เกิน13หลักเเละต้องเป็นตัวเลขเท่านั้น"
    });
  }
  document.getElementById("ID").value = limit;
  }