$(document).ready(function(){
    $("#registration").on("click",function(){
      window.location = "web_registration.html";
    })
  
    $("#section1").on("click",function(){
      window.location = "web_section1.html";
    })
    
    $("#section2").on("click",function(){
      window.location = "web_section2.html";
    })
  
    $("#section3").on("click",function(){
      window.location = "web_section3.html";
    })
  
    $("#section4").on("click",function(){
      window.location = "web_section4.html";
    })
  
    $("#section5").on("click",function(){
      window.location = "web_section5.html";
    })
  
    $("#section6").on("click",function(){
      window.location = "web_section6.html";
    })
  })
  
  window.onload = function(){
    load();
    //alert("test");
  }
  function load(){
      
      
      $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/get_patient_display.php",
          success: function (data) {
              var result = JSON.parse(data);
              console.log(result);
              document.getElementById("name").innerHTML = result["fullname"];
              document.getElementById("TNR").innerHTML = "TN# : "+result["TNR"];
              document.getElementById("HN").innerHTML = "HN : "+result["HN"];
              document.getElementById("mom").innerHTML = "มารดา : "+result["mother_fullname"];
              var num = 0;
              if(result["section1"]){
                document.getElementById("section1x").parentElement.style.cssText += "background-color: #7FC5C6;";
                num++;
              }
              if(result["section2"]){
                document.getElementById("section2x").parentElement.style.cssText += "background-color: #7FC5C6;";
                num++;
              }
              if(result["section3"]){
                document.getElementById("section3x").parentElement.style.cssText += "background-color: #7FC5C6;";
                num++;
              }
              if(result["section4"]){
                document.getElementById("section4x").parentElement.style.cssText += "background-color: #7FC5C6;";
                num++;
              }
              if(result["section5"]){
                document.getElementById("section5x").parentElement.style.cssText += "background-color: #7FC5C6;";
                num++;
              }
              if(result["section6"]){
                document.getElementById("section6x").parentElement.style.cssText += "background-color: #7FC5C6;";
                num++;
              }
              var  per = (100/6)*num;
              document.getElementById("percent").innerHTML = per.toFixed(2)+"%";
              document.getElementById("progress_bar").style.cssText += "width: " + per.toFixed(2)+"%";
          },  
  
        });
  
  
        if(localStorage.hospital == ""){
            $.ajax({
              type: "POST",
              url: "https://techvernity.com/thainy/php/check_refer.php",
              success: function (data) {
                  // var result = JSON.parse(data);
                  // console.log(result);
                 // alert(data);
                  if(data==0){
                    document.getElementById("cancel").hidden = true;
                  }else{
                    document.getElementById("refer").hidden = true;
                  }
              },
            });
        }else{
          //alert("referer");
            document.getElementById("cancel").hidden = true;
            document.getElementById("refer").hidden = true;
  
  
  
            ////////////// กรณี รอรับ refer
        }
  }
  function cancel(){
    if(confirm("Are you sure?")){
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/cancel_refer.php",
        success: function (data) {
            // var result = JSON.parse(data);
            // console.log(result);
            window.location.reload();
        },
      });
    }
  }