var section_name = ["TNR","hospital","admission_status","intrauterine_transter","transfer_member","hospital_name","non_hospital_name","admitted_to","admission_date","discharge_date","discharge_type","stay_day","discharge_hospital","discharge_hospital_code","discharge_level2_hospital","discharge_non_mem_hospital","discharge_at_age","discharge_hospital_day","status","last_modified_username","last_modified_date"];
window.onload = function(){
    load();
    setting();
   
}
function load(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_hospital.php",
        success: function (data) {
            var result = JSON.parse(data);
            //console.log(result);
            $("select[name='hospital_name']").html(' <option value="" selected disabled>เลือกโรงพยาบาล</option>');
            $("select[name='discharge_hospital']").html(' <option value="" selected disabled>เลือกโรงพยาบาล</option>');
 
            for(var i = 0;i<result.length;i++){
                $("select[name='hospital_name']").append(' <option value="'+result[i]["hospital_name"]+'">'+result[i]["hospital_name"]+' ('+result[i]["hospital_number"]+')</option>');
                $("select[name='discharge_hospital']").append(' <option value="'+result[i]["hospital_name"]+'">'+result[i]["hospital_name"]+' ('+result[i]["hospital_number"]+')'+'</option>');
           
            }

           
        },
      });

      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_ob_section.php",
        data:{num:"1",hospital:localStorage.hospital},
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
          push_ob_data(result[0]);
        },
      });
      if(localStorage.hospital != ""){
        //alert(localStorage.hospital);

        $("input").prop("disabled",true);
        $("textArea").prop("disabled",true);
        $("select").prop("disabled",true);
        $(".save-btn").prop("hidden",true);
        $(".plus").prop("hidden",true);
      }
}
function setting(){
    custom_date_picker("test",1);
    $("#test").on("change",function(){
        $("#admission_date").val($(this).val());
    });

    timeline_min_max(null,"now");
    
     //info
     $("#info1").click(function(){ 
         show_info("การส่งต่อมารดาเพื่อมาคลอดเท่านั้น (การส่งมาฝากครรภ์ต่อ ไม่ถือเป็นกรณีนี้)") 
    })
    $("#info2").click(function(){
        show_info("เป็นการส่งต่อมารดาเพื่อมาฝากครรภ์ต่อ ในกรณีที่ทารกอาจมีความผิดปกติในอนาคต")
    })
    $("#info3").click(function(){
        show_info("วันที่ผู้ป่วยเข้ารับการรักษาในโรงพยาบาลของท่าน (ไม่ใช่เข้าสู่ระบบ Registry)")
    })
    $("#info4").click(function(){
        show_info("วันที่ผู้ป่วยออกจากโรงพยาบาลของท่าน (ไม่ใช่ออกจากระบบ Registry)")
    })



    $("input[name='admission_status']").on("change",function(){
        if($(this).val()=="Inborn"){
            console.log("1");
        //    document.getElementById("pop1").style.display = "block";
        //    document.getElementById("pop2").style.display = "none";
            $("#pop1").slideDown(200);
            $("#pop2").slideUp(200);
            
           document.getElementsByName("transfer_member")[0].checked = false;
           document.getElementsByName("transfer_member")[1].checked = false;
        //    document.getElementById("hospital_name").style.display = "none";
        //    document.getElementById("non_hospital_name").style.display = "none";

           document.getElementById("hospital_name").value = "";
           document.getElementById("non_hospital_name").value = "";

        
        }
        if($(this).val()=="Outborn / Transfer from another hospital"){
            console.log("2");
        //    document.getElementById("pop1").style.display = "none";
        //    document.getElementById("pop2").style.display = "block";
            $("#pop2").slideDown(200);
            $("#pop1").slideUp(200);
           document.getElementsByName("intrauterine_transter")[0].checked = false;
           document.getElementsByName("intrauterine_transter")[1].checked = false;
           //document.getElementById("pop3").style.display = "none";
        }
        if($(this).val()=="BBA"){
            console.log("3");
        //    document.getElementById("pop1").style.display = "none";
        //    document.getElementById("pop2").style.display = "none";
        $("#pop2").slideUp(200);
        $("#pop1").slideUp(200);
           document.getElementsByName("intrauterine_transter")[0].checked = false;
           document.getElementsByName("intrauterine_transter")[1].checked = false;

           document.getElementsByName("transfer_member")[0].checked = false;
           document.getElementsByName("transfer_member")[1].checked = false;
           

           document.getElementById("hospital_name").value = "";
           document.getElementById("non_hospital_name").value = "";
        }

        $("input[name='transfer_member']").trigger("change")
    });
 

   
    $("input[name='transfer_member']").on("change",function(){
        if( $("input[name='transfer_member']:checked").val()=="TNR member hospital"){
            console.log("1");
        //    document.getElementById("hospital_name").style.display = "block";
        //    document.getElementById("non_hospital_name").style.display = "none";
           $("#hospital_name").slideDown(200);
           $("#non_hospital_name").slideUp(200);
           
           document.getElementById("non_hospital_name").value = "";
        }
        else if($("input[name='transfer_member']:checked").val()=="Non TNR member hospital"){
            console.log("2");
        //    document.getElementById("hospital_name").style.display = "none";
        //    document.getElementById("non_hospital_name").style.display = "block";
           $("#hospital_name").slideUp(200);
           $("#non_hospital_name").slideDown(200);
           document.getElementById("hospital_name").value = "";
          
        }
        else{
           $("#hospital_name").slideUp(200);
           $("#non_hospital_name").slideUp(200);
           document.getElementById("hospital_name").value = "";
           document.getElementById("non_hospital_name").value = "";
        }
      
    });

    $("input[name='discharge_type']").on("change",function(){
        if($(this).val()=="Refer to TNR member hospital"){
            console.log("1");
           //document.getElementById("discharge_hospital_box").style.display = "block";
            $("#discharge_hospital_box").slideDown(200);
           // document.getElementById("pop5").style.display = "none";
            $("#pop5").slideUp(200);
            document.getElementById("discharge_level2_hospital").value = "";
            
          
        }
        if($(this).val()=="Discharge home"){
            console.log("2");
            //document.getElementById("pop5").style.display = "block";

            // document.getElementById("discharge_hospital_box").style.display = "none";
            $("#discharge_hospital_box").slideUp(200);
            $("#pop5").slideUp(200);
            document.getElementById("discharge_hospital").value = "";
     //      document.getElementById("discharge_hospital_code").value = "";
         

           document.getElementById("discharge_level2_hospital").value = "";
        //   document.getElementById("discharge_non_mem_hospital").value = "";
         //  document.getElementById("discharge_at_age").value = "";
         //  document.getElementById("discharge_hospital_day").value = "";

        }
        if($(this).val()=="Refer to level 2 hospital / non TNR member hospital"){
            console.log("3");
           
            //document.getElementById("pop5").style.display = "block";
            $("#pop5").slideDown(200);
            document.getElementById("discharge_hospital_box").style.display = "none";
            document.getElementById("discharge_hospital").value = "";
         //  document.getElementById("discharge_hospital_code").value = "";

           
        }
    
    });

     $("input[name='admission_date']").on("change",function(){
      var admission_date =   document.getElementById("admission_date").value ;
      var discharge_date  = document.getElementById("discharge_date").value ;
        
      if(!!admission_date && !!discharge_date){
        document.getElementsByName("stay_day")[0].value  =  calculateAge (admission_date, discharge_date);
      }
      else{
        document.getElementsByName("stay_day")[0].value  = "";
      }
        
    });

    $("input[name='discharge_date']").on("change",function(){
        var admission_date =   document.getElementById("admission_date").value ;
        var discharge_date  = document.getElementById("discharge_date").value ;
        
        if(!!admission_date && !!discharge_date){
            document.getElementsByName("stay_day")[0].value  =  calculateAge (admission_date, discharge_date);
          }
          else{
            document.getElementsByName("stay_day")[0].value  = "";
          }
    });

}
function calculateAge (birthDate, otherDate) {
     birthDate = moment(birthDate, "YYYY-MM-DD");
     enbirthDated = moment(otherDate, "YYYY-MM-DD");
    
     var year = moment.duration(enbirthDated.diff(birthDate)).asDays();

   

    return (parseInt(year)+1)+" day(s)";
}
function save(){
    //console.log(get_ob_data(section_name));
    var temp = get_ob_data(section_name);
   //path1//
   var path1 = false;
    if(document.getElementsByName("admission_status")[0].checked){
        if(document.getElementsByName("intrauterine_transter")[0].checked || document.getElementsByName("intrauterine_transter")[1].checked){
            console.log("pass1s");
            path1 = true;
        }
    }
    else if(document.getElementsByName("admission_status")[1].checked){
        console.log(document.getElementsByName("hospital_name")[0].value);
        console.log(document.getElementsByName("non_hospital_name")[0].value);
        if(document.getElementsByName("hospital_name")[0].value != "" || document.getElementsByName("non_hospital_name")[0].value != ""){
            console.log("pass2");
            path1 = true;
            
        }
        
    }
    else if(document.getElementsByName("admission_status")[2].checked){
        console.log("pass3");
        path1 = true;
    }
  
   

   //path2//
   var path2 = false;
   console.log(document.getElementById("admission_date").value);
   console.log(document.getElementById("discharge_date").value);
   if(document.getElementById("admission_date").value != "" && document.getElementById("discharge_date").value != "" ){
    path2 = true;
   }

   //path3//
   path3 = false;

   if(document.getElementsByName("discharge_type")[0].checked){
       
    if(document.getElementById("discharge_hospital").value != ""){
        console.log("pass3 1");
        path3 = true;
    }
}
else if(document.getElementsByName("discharge_type")[1].checked){
    console.log("pass3 2");
    path3 = true;
    
}
else if(document.getElementsByName("discharge_type")[2].checked){
    
    if(document.getElementById("discharge_level2_hospital").value != "" 
    //&&
    //document.getElementById("discharge_non_mem_hospital").value != ""&&
    //document.getElementById("discharge_at_age").value != ""&&
    //document.getElementById("discharge_hospital_day").value != ""
    ){
        console.log("pass3 3");
    path3 = true;
    }
}
   var complete = "not done";
    if(path1 && path2 && path3){
        complete = "done";
    }
    console.log(temp);
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/update_process_section.php",
        data:{data:complete,num:"1"},
        success: function (data) {
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/save_ob_section.php",
                data:{data:temp,num:"1"},
                success: function (data) {
                    var result = JSON.parse(data);
                    console.log(result);
                    alert_success({ 
                        text: "บันทึกสำเร็จ", 
                        button_func_1: function(){
                            //localStorage.back = true;
                            change_page("patient_display.html","back");
                            // window.location = "patient_display.html";
                        }
                      })
                  
                },
              });
        },
      });
}