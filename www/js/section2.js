window.onload = function(){
  //  alert("test");
  setting();
  load();
 
}
function setting(){
  $("input[name='i1']").on("change",function(){
    if($(this).val()=="age"){
      $("input[name='i2']").prop("disabled",false);
    }else{
      $("input[name='i2']").prop("disabled",true);
      $("input[name='i2']").val("");
    }
  });

  $("input[name='ab5']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='other_etc']").prop("disabled",false);
    }
  });
  $("input[name='ab5']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_etc']").prop("disabled",false);
      $("input[name='ab6']").prop("checked",false);
    }
  });
  for(var i = 1 ;i<5;i++){
    $("input[name='ab"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
       
        $("input[name='ab6']").prop("checked",false);
      }
    });
  }
  $("input[name='ab6']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_etc']").prop("disabled",true);
      $("input[name='Other_etc']").val("");
      $("input[name='ab1']").prop("checked",false);
      $("input[name='ab2']").prop("checked",false);
      $("input[name='ab3']").prop("checked",false);
      $("input[name='ab4']").prop("checked",false);
      $("input[name='ab5']").prop("checked",false);
    }
  });


  $("input[name='comp10']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 0;i<10;i++){
        $("input[name='comp"+i+"']").prop("checked",false);
      }
      $("#pop1").slideUp(200);
      $("input[name='Other2_etc']").prop("disabled",true);
      $("input[name='Other2_etc']").val("");
      $("input[name='Maternal_drug_etc']").prop("disabled",true);
      $("input[name='Maternal_drug_etc']").val("");
      $("input[name='Higher_order_etc']").prop("disabled",true);
      $("input[name='Higher_order_etc']").val("");
    }
  });

  $("input[name='comp6']").on("change",function(){
    if($(this).val()=="Higher_order"){
      $("input[name='Higher_order_etc']").prop("disabled",false);
    }else{
      $("input[name='Higher_order_etc']").prop("disabled",true);
      $("input[name='Higher_order_etc']").val("");
    }
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp8']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Maternal_drug_etc']").prop("disabled",false);
    
    }else{
      $("input[name='Maternal_drug_etc']").prop("disabled",true);
      $("input[name='Maternal_drug_etc']").val("");
    

    }
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp9']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other2_etc']").prop("disabled",false);
    
    }else{
      $("input[name='Other2_etc']").prop("disabled",true);
      $("input[name='Other2_etc']").val("");
    

    }
    $("input[name='comp10']").prop("checked",false);
  });
  $("input[name='comp5']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop1").slideDown(200);
    }else{
      $("#pop1").slideUp(200);
      $("input[name='comp6']").prop("checked",false);
      $("input[name='Higher_order_etc']").prop("disabled",true);
      $("input[name='Higher_order_etc']").val("");
    }
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp1']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp2']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp3']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp4']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });

  $("input[name='comp7']").on("change",function(){
    $("input[name='comp10']").prop("checked",false);
  });



  $("input[name='intra15']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1;i<15;i++){
        $("input[name='intra"+i+"']").prop("checked",false);
      }

      $("input[name='Other_organism_etc']").prop("disabled",true);
      $("input[name='Other_organism_etc']").val("");
      $("input[name='Other3_etc']").prop("disabled",true);
      $("input[name='Other3_etc']").val("");
      $("#pop2").slideUp(200);
    }
  });


  $("input[name='intra14']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other3_etc']").prop("disabled",false);
      
    }else{
      $("input[name='Other3_etc']").prop("disabled",true);
      $("input[name='Other3_etc']").val("");
    }
    $("input[name='intra15']").prop("checked",false);

  });

  $("input[name='intra9']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other_organism_etc']").prop("disabled",false);
   
    }else{
      $("input[name='Other_organism_etc']").prop("disabled",true);
      $("input[name='Other_organism_etc']").val("");
    }
    $("input[name='intra15']").prop("checked",false);

  });
  $("input[name='intra6']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop2").slideDown(200);
    }else{
      $("#pop2").slideUp(200);
      $("input[name='intra7']").prop("checked",false);
      $("input[name='intra8']").prop("checked",false);
      $("input[name='intra9']").prop("checked",false);
      $("input[name='Other_organism_etc']").prop("disabled",true);
      $("input[name='Other_organism_etc']").val("");
    }
    $("input[name='intra15']").prop("checked",false);

  });
  for(var i = 1 ;i<6;i++){
    $("input[name='intra"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='intra15']").prop("checked",false);
      }
    });
  }
  for(var i = 10 ;i<14;i++){
    $("input[name='intra"+i+"']").on("change",function(){
      if($(this).prop("checked")==true){
        $("input[name='intra15']").prop("checked",false);
      }
    });
  }

  $("input[name='me7']").on("change",function(){
    if($(this).prop("checked")==true){
      for(var i = 1; i<7;i++){
        $("input[name='me"+i+"']").prop("checked",false);
      }
      $("input[name='Other4_etc']").val("");
      $("input[name='Other4_etc']").prop("disabled",true);
      $("#pop3").slideUp(200);
      $("#pop4").slideUp(200);
    }
  });

  $("input[name='me1']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop3").slideDown(200);
    }else{
      $("input[name='me2']").prop("checked",false);
      $("#pop3").slideUp(200);
    }
    $("input[name='me7']").prop("checked",false );
  });
  $("input[name='me3']").on("change",function(){
    if($(this).prop("checked")==true){
      $("#pop4").slideDown(200);
    }else{
      $("input[name='me4']").prop("checked",false);
      $("#pop4").slideUp(200);
    }
    $("input[name='me7']").prop("checked",false );
  });

  $("input[name='me5']").on("change",function(){

    $("input[name='me7']").prop("checked",false );
  });

  $("input[name='me6']").on("change",function(){
    if($(this).prop("checked")==true){
      $("input[name='Other4_etc']").prop("disabled",false);
    }else{
      $("input[name='Other4_etc']").prop("disabled",true);
      $("input[name='Other4_etc']").val("");
    }
    $("input[name='me7']").prop("checked",false );
  });
}

function load(){
  var status = "";
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section1.php",
    data:{num:"2"},
    success: function (data) {
        var result2 = JSON.parse(data);
        console.log(result2);
        if(result2.length!=0){
            push_all_section_data("i",result2[0]);
        }else{
          status = "not done";
        }

        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/get_section1.php",
          data:{num:"2_2"},
          success: function (data) {
              var result2 = JSON.parse(data);
              console.log(result2);
              if(result2.length!=0){
                  push_all_section_data("ab",result2[0]);
              }
              else{
                status = "not done";
              }
              $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/get_section1.php",
                data:{num:"2_3"},
                success: function (data) {
                    var result2 = JSON.parse(data);
                    console.log(result2);
                    if(result2.length!=0){
                        push_all_section_data("comp",result2[0]);
                    }
                    else{
                      status = "not done";
                    }
                    $.ajax({
                      type: "POST",
                      url: "https://techvernity.com/thainy/php/get_section1.php",
                      data:{num:"2_4"},
                      success: function (data) {
                          var result2 = JSON.parse(data);
                          console.log(result2);
                          if(result2.length!=0){
                              push_all_section_data("intra",result2[0]);
                          }
                          else{
                            status = "not done";
                          }
                          $.ajax({
                            type: "POST",
                            url: "https://techvernity.com/thainy/php/get_section1.php",
                            data:{num:"2_5"},
                            success: function (data) {
                                var result2 = JSON.parse(data);
                                console.log(result2);
                                if(result2.length!=0){
                                    push_all_section_data("me",result2[0]);
                                }
                                
                            },
                          });
                      },
                    });
                },
              });
          },
        });
    },
  });

 

  
}

function save(){
    var temp = get_all_data("i");
    console.log(temp);
    $.ajax({
      type: "POST",
      url: "https://techvernity.com/thainy/php/save_section1.php",
      data:{data:JSON.stringify(temp),num:"2"},
      success: function (data) {
          var result = JSON.parse(data);
          console.log(result);
          
          //.location.reload();

          var temp2 = get_all_data("ab");
          console.log(temp2);
      
          $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/save_section1.php",
            data:{data:JSON.stringify(temp2),num:"2_2"},
            success: function (data) {
                var result = JSON.parse(data);
                console.log(result);
               
               // window.location.reload();
               var temp3 = get_all_data("comp");
               console.log(temp3); 

               $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/save_section1.php",
                data:{data:JSON.stringify(temp3),num:"2_3"},
                success: function (data) {
                    var result = JSON.parse(data);
                    console.log(result);
                   
                   // window.location.reload();
                   var temp4 = get_all_data("intra");
                    console.log(temp4); 

                    $.ajax({
                      type: "POST",
                      url: "https://techvernity.com/thainy/php/save_section1.php",
                      data:{data:JSON.stringify(temp4),num:"2_4"},
                      success: function (data) {
                          var result = JSON.parse(data);
                          console.log(result);
                        
                        // window.location.reload();
                        var temp5 = get_all_data("me");
                        console.log(temp5); 

                        $.ajax({
                          type: "POST",
                          url: "https://techvernity.com/thainy/php/save_section1.php",
                          data:{data:JSON.stringify(temp5),num:"2_5"},
                          success: function (data) {
                              var result = JSON.parse(data);
                              console.log(result);
                              alert("บันทึกสำเร็จ");
                              change_page("patient_display.html","back");
                          },
                        });
                      },
                    });
                },
              });
            },
          });
      },
    });

   

  //  alert("บันทีกสำเร็จ");
  //  window.location.reload();
}