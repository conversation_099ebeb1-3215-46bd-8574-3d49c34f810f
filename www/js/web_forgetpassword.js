

$(document).ready(function(){
  $(".popup-fake").hide();
  $("#error-text1").hide();
  $("#error-text2").show();
  $("#email").removeClass("error-type"); 
  $(".color-pink").hide(); 
  $(".next-ntn").on("click",function(){
    // $(".error-text").hide();
    var email = $("#email").val();
    if(!email){
      popup1();
      $(".popup-fake").show(); 
      $(".color-pink").show();
      $(".color-green").hide();
      $("#error-text1").show();
      $("#error-text2").hide();
      $("#email").addClass("error-type"); 
    }else{
      $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/forget_password.php",
          data: {
              email: email
          },
          success: function (data) {
              var result = JSON.parse(data);
  
              if(result.status == 1){
                // popup2(); when i use this function txt-mail on popup not show
                $(".popup-fake").show();
                $(".color-pink").hide();
                $("#txt-email").text(email);
                $(".color-green").show();
                $("#email").removeClass("error-type");   
                $(".error-box").css("visibility","hidden");  

              }
              else{
                popup1();
                $(".popup-fake").show();
                $(".color-pink").show();
                $(".color-green").hide();
                $("#email").addClass("error-type");       
              }
          },
      });
    }
  })

  $(".popup-btn").on("click",function(){
    $(".popup-fake").hide();
  })

  $(".back-box").on("click",function(){
    window.location = "web_login.html";
  })
})
