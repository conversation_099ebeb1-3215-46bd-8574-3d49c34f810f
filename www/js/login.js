window.onload = function(){
    //floatx("username",1);
   // show_info("test");
 //   ajax_loading("get_ob_section.php",{data:{},num:"1"});
}
$(document).ready(function(){
  localStorage.removeItem("token");
  $(".login-btn").on("click",function(){
       var username = $("#username").val();
       var password = $("#password").val();

      /* error */
      if (!username && !password) {
          alert_fail({text: "กรุณากรอกบัญชีผู้ใช้และรหัสผ่านให้ครบถ้วน"});
          $("#error-text").text("กรุณากรอกบัญชีผู้ใช้และรหัสผ่านให้ครบถ้วน");
          $(".error-box").css("visibility", "visible");
          $("#username").addClass("error-type");
          $("#password").addClass("error-type");
      }
    //   else if (!username) {
    //       alert_fail({text: "กรุณากรอกบัญชีผู้ใช้ให้ครบถ้วน"});
    //       $("#error-text").text("กรุณากรอกบัญชีผู้ใช้ให้ครบถ้วน");
    //       $(".error-box").css("visibility", "visible");
    //       $("#username").addClass("error-type");
    //       $("#password").removeClass("error-type");
    //   }
    //   else if (!password) {
    //       alert_fail({text: "กรุณากรอกรหัสผ่านให้ครบถ้วน"});
    //       $("#error-text").text("กรุณากรอกรหัสผ่านให้ครบถ้วน");
    //       $(".error-box").css("visibility", "visible");
    //       $("#password").addClass("error-type");
    //       $("#username").removeClass("error-type");
    //   }
      else{
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/login.php",
            data: {
                username: username,
                password: password
            },
            success: function (data) {
                var result = JSON.parse(data);

                if(result.status == 1){
                    sessionStorage.is_auth = 1;
                    
                    if(result.user_status=="ready"){
                        localStorage.token = result.token;

                        localStorage.master_pin = result.pin;
                        change_page("home.html", "next");
                     //   window.location = "home.html";

                        /*
                        alert_success({
                            text: "เข้าสู่ระบบสำเร็จ", 
                            button_func_1: function(){      
                            }
                        });
                        */
                    }else{
                        change_page("termandcondition.html", "next");
                       // window.location = "termandcondition.html";

                        /*
                        alert_success({
                            text: "เข้าสู่ระบบสำเร็จ", 
                            button_func_1: function(){
                               
                            }
                        });
                        */
                    }
                }
                else if(result.status == -1){
                    alert_fail({text: "บัญชีของคุณยังไม่ได้การอนุมัติ"});
                    $(".error-box").css("visibility","visible");
                    $("#error-text").text("บัญชีของคุณยังไม่ได้การอนุมัติ");          
                }
                else{
                    alert_fail({text: "ดูเหมือนว่าบัญชีผู้ใช้หรือ<br /> รหัสผ่านของคุณไม่ถูกต้อง"});
                    $(".error-box").css("visibility","visible");
                    $("#username").removeClass("error-type");            
                    $("#password").removeClass("error-type");   
                    $("#error-text").text("บัญชีผู้ใช้หรือรหัสผ่านไม่ถูกต้อง");          
                }
            },
          });
      }
     
  })

  $(".forget-btn").on("click",function(){
    change_page("forget_password.html", "next");
   // window.location = "forget_password.html";
  })

  $(".back-box").on("click",function(){
    change_page("login.html", "back");
    //window.location = "login.html";
  })

  $(".forget-btn").on("click",function(){
    //dummy
    var email = "<EMAIL>";

    // $.ajax({
    //     type: "POST",
    //     url: "https://techvernity.com/thainy/php/forget_password.php",
    //     data: {
    //         email: email
    //     },
    //     success: function (data) {
    //         var result = JSON.parse(data);

    //         if(result.status == 1){
    //             alert("success");
    //         }
    //         else{
    //             alert("failed " + result.text);              
    //         }
    //     },
    //   });
      
  });
 
  //get version
  $("#version").text("0.0.8")


//   $.ajax({
//     type: "POST",
//     url: "https://techvernity.com/thainy/php/get_version.php",
//     success: function (data) {
//         var result = JSON.parse(data);

//         if(result.status == 1){
            
//         }
//         else{
                          
//         }
//     },
//   });
});
