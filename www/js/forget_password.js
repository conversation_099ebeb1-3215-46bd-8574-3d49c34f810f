

$(document).ready(function(){
    $(".login-btn").on("click",function(){
    
      // $(".error-text").hide();
      var email = $("#input-email").val();
      if(!email){
        alert_fail({text: "ดูเหมือนว่าอีเมลที่คุณกรอก<br/>ไม่ตรงกับที่อยู่ในระบบ", header: "ไม่ถูกต้อง"})
        $(".error-box").css("visibility","visible");
        $("#input-email").addClass("error-type"); 
        

      }else{
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/forget_password.php",
            data: {
                email: email
            },
            success: function (data) {
                var result = JSON.parse(data);
    
                if(result.status == 1){
                  alert_success({text: "เราได้ส่งลิงค์การตั้งค่ารหัสผ่านใหม่<br>ไปยัง “"+email+"” <br>เรียบร้อยแล้ว", header: "ไม่ถูกต้อง"})
                  $("#input-email").removeClass("error-type");   
                  $(".error-box").css("visibility","hidden");  

                }
                else{
                  alert_fail({text: "ดูเหมือนว่าอีเมลที่คุณกรอก<br/>ไม่ตรงกับที่อยู่ในระบบ", header: "ไม่ถูกต้อง"})
                  $("#input-email").addClass("error-type");   
                  $(".error-box").css("visibility","visible");     
                }
            },
        });
      }
    })
})
