document.addEventListener("deviceready", onDeviceReady, false);

function onDeviceReady(){
    if(!sessionStorage.is_auth && localStorage.master_pin != "null"){
        get_pin_page_check();
        BioAuthen();
        checkBioAuthen();
        //update_noti_key();
        sessionStorage.is_auth = 1;
    }
}

$(document).ready(function(){
    localStorage.search = "";
    localStorage.target = ""

    $("body").fadeIn(250, function(){
        
    })
/*
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/session.php",
    success: function (data) {
        
        var result = JSON.parse(data);
        console.log(result.username);
        
        setname("",result.firstname.toString(),result.lastname.toString(),result.hospital.toString(),result.roll.toString());
    }
  });
  */
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_userdata.php",
    success: function (data) {
        
        var result = JSON.parse(data);
        console.log(result[0].username);
        localStorage.p_id = result[0].username;
        localStorage.hos_id = result[0].hospital;
        setname("",result[0].firstname,result[0].lastname,result[0].hospital,result[0].role);

        if(result[0].role == "leader"){
            $("#approve").show();
            $("#track_tn").show();
        } 
    }
  });

 // setname("นาย","พสิษฐ์","พงศ์พจน์เกษม","รามา","พยาบาล");
  $("#patient_list").on("click",function(){
    change_page("patientlist.html", "next");
  })

  $("#track_tn").on("click",function(){
    change_page("search_patient.html", "next");
     // window.location = "search_patient.html";
  })

  $("#export").on("click",function(){
    change_page("export_data.html", "next");
     // window.location = "export_data.html";
  })

  $("#help").on("click",function(){
    change_page("help_and_support1.html", "next");
     // window.location = "help_and_support1.html";
  })

  $("#approve").on("click",function(){
    change_page("authorize.html", "next");
      //window.location = "authorize.html";
  })

  $("#setting").on("click",function(){
    change_page("setting.html", "next");
      //window.location = "setting.html";
  })

  $("#logout").on("click",function(){
      confirm_o({text:"ต้องการออกจากระบบหรือไม่", header:"ออกจากระบบ", button_func_1: function(){
        change_page("login.html", "next");
        //window.location = "login.html";
      }})
     
  })
})

function setname(title,name,surname,hospital,userstatus){
    console.log("Hello");
    var fullname = title+" "+name+" "+surname;
    var fullhospital = hospital;
    var status = userstatus;
    if(status == "collector"){
        status = "ผู้เก็บข้อมูล";
    }
    if(status == "doctor"){
        status = "แพทย์";
    }
    if(status == "leader"){
        status = "หัวหน้า";
    }
    if(status == "admin"){
        status = "ผู้ดูแลระบบ";
    }
    document.getElementById("fullname").innerHTML = fullname;
    document.getElementById("hospital").innerHTML = fullhospital;
    document.getElementById("status").innerHTML = status;

}