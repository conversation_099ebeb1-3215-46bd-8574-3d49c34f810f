document.getElementById('check-idcard').setAttribute("disabled","disabled");
document.getElementById('check-passport').setAttribute("disabled","disabled");
$.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_userdata.php",
    success: function (data) {
        
        var result = JSON.parse(data);
        console.log(result[0].username);
        
        setname("",result[0].firstname,result[0].lastname,result[0].hospital,result[0].role);
    }
  });
  function setname(title,name,surname,hospital,userstatus){
    console.log("Hello");
    var fullname = title+" "+name+" "+surname;
    var fullhospital = hospital;
    var status = userstatus;
    if(status == "collector"){
        status = "ผู้เก็บข้อมูล";
    }
    if(status == "doctor"){
        status = "แพทย์";
    }
    if(status == "leader"){
        status = "หัวหน้า";
    }
    if(status == "admin"){
        status = "ผู้ดูแลระบบ";
    }
    document.getElementById("fullname").innerHTML = fullname;
    document.getElementById("hospital").innerHTML = fullhospital;
    document.getElementById("status").innerHTML = status;

}
var sex1 = "Male";
function choose(sex){
    if(sex == 'male'){
   
    document.getElementById("F1").classList.remove('choose-fm');
    document.getElementById("F1").classList.add('unchoose-fm');
    document.getElementById("M1").classList.remove('unchoose-fm');
    document.getElementById("M1").classList.add('choose-fm');
    sex1 = 'Male';
 //   console.log("top:"+sex1);
    }
    if(sex == 'female'){
    
    document.getElementById("M1").classList.remove('choose-fm');
    document.getElementById("M1").classList.add('unchoose-fm');
    document.getElementById("F1").classList.remove('unchoose-fm');
    document.getElementById("F1").classList.add('choose-fm');
    sex1 = 'Female';
//    console.log("down:"+sex1);
    }

}
function selectbox(type){
    if(type == "id"){
        console.log("id: "+document.getElementById("idcard").checked);
        if(!document.getElementById("idcard").checked){  
            document.getElementById('check-idcard').removeAttribute("disabled");    
            
        }else{
            document.getElementById('check-idcard').setAttribute("disabled","disabled");
            document.getElementById("check-idcard").value = "";
        }
    }

    if(type == "pass"){
        console.log("pass: "+document.getElementById("idcard").checked);
        if(!document.getElementById("idpass").checked){      
            document.getElementById('check-passport').removeAttribute("disabled"); 
        }else{
            document.getElementById('check-passport').setAttribute("disabled","disabled");
            document.getElementById("check-passport").value = "";
        }
    }
}

function collectdata(){
    console.log("hello");
    var name = document.getElementById("patient_name").value; //must
    var id = document.getElementById("patient_id").value;
    var sex = sex1 //must
   // console.log(name+":"+id+":"+sex1);
    
    var hn = document.getElementById("patient_hn").value; //must
    var dob = document.getElementById("patient_date").value; //must
    var ethnic = document.getElementById("patient_ethnic").value;
    //console.log(hn+":"+dob+":"+ethnic);

    var mothername = document.getElementById("mothername").value;
    var mother_id = "none";
    var mother_pass = "none"
    if(document.getElementById("idcard").checked){
        mother_id = document.getElementById("check-idcard").value;
    } 
        if(document.getElementById("idpass").checked){
            mother_pass = document.getElementById("check-passport").value;
        }
  //  console.log(mothername+":"+mother_id+":"+mother_pass);

    var mother_address = document.getElementById("address").value;
    var mother_tel = document.getElementById("tel").value;
   // console.log(mother_address+":"+mother_tel);

    var contact_name = document.getElementById("contact_name").value;
    var contact_tel = document.getElementById("contact_tel").value;
//    console.log(contact_name+":"+contact_tel);

    var contact_relate = document.getElementById("contact_relate").value;
    var contact_other =  document.getElementById("contact_other").value;
  //  console.log(contact_relate+":"+contact_other);

    if(name == "" || hn == "" || dob == ""){
    alert("กรุณากรอกช่องที่มี * ให้สมบูรณ์");
    return;
    }
    if(mothername == "" || (mother_id == "none" && mother_pass == "none")){
    alert("กรุณากรอกช่องที่มี * ให้สมบูรณ์");
    return;
    }
    var TNR = 0;
    var dateObj = new Date();
    var month = dateObj.getUTCMonth() + 1; //months from 1-12
    var day = dateObj.getUTCDate();
    var year = dateObj.getUTCFullYear();
   var dateCreate = year+"-"+month+"-"+day;
   $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/TNRgennerate.php",
    data: {
        regist_date: dateCreate,
        hospital: hn
    }, success: function (data) {
        TNR = data;
        console.log(dateCreate);
        console.log(TNR);
        send_data = [];
        send_data.push(TNR);
        send_data.push(name);
        send_data.push(id);
        send_data.push(sex);
        send_data.push(hn);
        send_data.push(dob);
        send_data.push(ethnic);
        send_data.push(mothername);
        send_data.push(mother_id);
        send_data.push(mother_pass);
        send_data.push(mother_address);
        send_data.push(mother_tel);
        send_data.push(contact_name);
        send_data.push(contact_tel);
        send_data.push(contact_relate);
        send_data.push(contact_other);
        console.log(send_data);
    } 

    });
  
 
}
 
  



