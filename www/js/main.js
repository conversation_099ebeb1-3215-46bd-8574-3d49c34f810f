document.addEventListener("resume", onResume, false);

Date.prototype.yyyymmdd = function() {         

    var yyyy = this.getFullYear().toString();                                    
    var mm = (this.getMonth()+1).toString(); // getMonth() is zero-based         
    var dd  = this.getDate().toString();             

    return yyyy + '-' + (mm[1]?mm:"0"+mm[0]) + '-' + (dd[1]?dd:"0"+dd[0]);
};

$("input[type='number']").on("keydown", function () {
    if ($(this).val() != "") {
        var val = $(this).val();
        val = parseFloat(val);
        if(!!val){
            $(this).val(val);
        }
    }
});

$("input[data-input='date']").on("change",function(){
   if($(this).val()!=""){
       $(this).attr("type","date");
        $(this).attr("data-date",  get_thai_date(new Date($(this).val())));

        timeline_validate($(this))
   }
   else{
        $(this).attr("type","text");
        $(this).removeAttr("data-date");
   }
   
})
//.trigger("change")

function date_tricker(element){
    if($(element).val()!=""){
        $(element).attr("type","date");
        $(element).attr("data-date",  get_thai_date(new Date($(element).val())));
        
        timeline_validate($(element))
    }
    else{
        $(element).attr("type","text");
        $(element).removeAttr("data-date");
    }
}

function onResume() {
    if (localStorage.master_pin != "null" && ["login.html","forget_password.html"].indexOf(getPage()) == -1) {
        get_pin_page_check();
        BioAuthen();
        checkBioAuthen();
    }
}

document.addEventListener("backbutton", back, false);

function back() {

}
function change_page(page, type) {
    if(!!window.plugins && !!window.plugins.nativepagetransitions){
        if(type == "next"){
            transition_page_next(page);
        }
        else if(type == "back"){
            transition_page_back(page);
        }
        else if(type == "up"){
            transition_page_up(page);
        }
        else if(type == "down"){
            transition_page_up(page);
        }
    }
    
    window.location = page;
  }

function alert_success(options) {
    if (typeof options != "object") {
        options = Object();
    }

    if (!options.text) options.text = "";
    if (!options.header) options.header = "ถูกต้อง";
    if (!options.button_text_1) options.button_text_1 = "ยืนยัน";
    if (!options.button_func_1) options.button_func_1 = function () { };

    options.type = "success";
    popup_o(options);
}

function alert_fail(options) {
    if (typeof options != "object") {
        options = Object();
    }

    if (!options.text) options.text = "";
    if (!options.header) options.header = "ไม่ถูกต้อง";
    if (!options.button_text_1) options.button_text_1 = "ลองอีกครั้ง";
    if (!options.button_func_1) options.button_func_1 = function () { };

    options.type = "fail";
    popup_o(options);
}

function confirm_o(options) {
    if (typeof options != "object") {
        options = Object();
    }

    if (!options.text) options.text = "";
    if (!options.header) options.header = "ต้องการหรือไม่";
    if (!options.button_text_1) options.button_text_1 = "ใช่";
    if (!options.button_text_2) options.button_text_2 = "ไม่";
    if (!options.button_func_1) options.button_func_1 = function () { };
    if (!options.button_func_2) options.button_func_2 = function () { };

    options.type = "confirm";
    popup_o(options);
}

function popup_o(options) {
    if (!$("#popup_box").length) {
        $("body").prepend('<div id="popup_background" class="popup-login" hidden></div>' +
            '<div id="popup_container" class="popup-fake" hidden>' +
            '<div class="content_box">' +
            '<div class="ipd-popup">' +
            '<div id="popup_box">' +
            '<img id="popup_image" src="img/faillogin.svg">' +
            '<div id="popup_header" class="popup-text1"></div>' +
            '<div id="popup_text" class="popup-text2"></div>' +
            '<div id="popup_button_html"></div>' +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>'
        );
    }

    var text = options.text;
    var header = options.header;
    var button_text_1 = options.button_text_1;
    var button_text_2 = options.button_text_2;
    var image, background_color;

    if (options.type == "confirm") {
        image = "img/waitapprove.svg";
        background_color = "brown";
        $("#popup_text").removeAttr("class");
        $("#popup_text").addClass("popup-text2");
        $("#popup_text").addClass("color-text-" + background_color);
        $("#popup_image").addClass("waitapprove");

        $("#popup_button_html").html('<div class="popup-btn-box">' +
            '<div id="popup_button_2" class="cencel-btn"><span id="popup_button_text_2" class="set_center"></span></div>' +
            '<div class="line-btn"></div>' +
            '<div id="popup_button_1" class="approve-btn"><span id="popup_button_text_1"  class="set_center"></span></div>' +
            '</div>');
    }
    else {
        $("#popup_button_html").html('<div id="popup_button_1" class="popup-btn"><span id="popup_button_text_1"></div>');

        if (options.type == "success") {
            image = "img/complete.svg";
            background_color = "green";
            $("#popup_text").removeAttr("class");
            $("#popup_text").addClass("popup-text2");
            $("#popup_text").addClass("color-text-" + background_color);
        }
        else if (options.type == "fail") {
            image = "img/faillogin.svg";
            background_color = "pink";
            $("#popup_text").removeAttr("class");
            $("#popup_text").addClass("popup-text2");
            $("#popup_text").addClass("color-text-" + background_color);
        }
    }

    $("#popup_box").removeAttr("class");
    $("#popup_box").addClass("popup-box2");
    $("#popup_box").addClass("color-" + background_color);
    $("#popup_image").attr("src", image);
    $("#popup_text").html(text)
    $("#popup_header").html(header);
    $("#popup_button_text_1").html(button_text_1);
    $("#popup_button_text_2").html(button_text_2);

    if(!!options.on_top){
        $("#popup_background, #popup_container").css("z-index", "102");
    }

    $("#popup_button_1").on("click", function () {
        popup_box_hide(function () {
            options.button_func_1();
        });
    });

    $("#popup_button_2").on("click", function () {
        popup_box_hide(function () {
            options.button_func_2();
        });
    });

    popup_box_show();

    function popup_box_show() {
        $("#popup_container").fadeIn(250);
        $("#popup_background").fadeIn(250);
        $("#popup_box").transition(
            {
                opacity: 0,
                scale: 1.5
            }, 0);
        $("#popup_box").transition(
            {
                opacity: 1,
                scale: 1
            }, 250, 'easeOutQuart');
    }

    function popup_box_hide(success) {
        $("#popup_container").fadeOut(250);
        $("#popup_background").fadeOut(250);
        $("#popup_box").transition(
            {
                opacity: 0,
                scale: 1
            }, 250, 'easeOutQuart', function () {
                success();
            });
    }

}
function show_info(text){
   // if(document.getElementById("popup_container")==undefined){
        $("body").prepend('<div id="popup_background" class="popup-login" hidden style="display: block;"></div>'
        +'<div id="popup_container" class="popup-fake" hidden="" style="display: block;">'
            +'<div class="content_box">'
                +'<div class="ipd-popup">'
                    +'<div id="popup_box" class="popup-box-info color-white" style="opacity: 1; transform: scale(1, 1);">'
                        +'<div class="content_box_flow">'
                        +text
                        +'</div>'
                        +'<div id="popup_button_html">'
                            +'<div id="popup_button_1" class="popup-btn" onclick = "hide_info()">'
                                +'<span id="popup_button_text_1" >เรียบร้อย</span>'
                            +'</div>'
                        +'</div>'
                    +'</div>'
                +'</div>'
            +'</div>'
        +'</div>');


  $("#popup_box").css("top", $("#popup_container").height()/2 - $("#popup_box").outerHeight()/2);
  // } $("#popup_container").fadeIn(250);
  $("#popup_background").fadeIn(250);
  $("#popup_box").transition(
      {
          opacity: 0,
          scale: 1.5
      }, 0);
  $("#popup_box").transition(
      {
          opacity: 1,
          scale: 1
      }, 250, 'easeOutQuart');

}

function hide_info() {
    $("#popup_container").fadeOut(250);
    $("#popup_background").fadeOut(250);
    $("#popup_box").transition(
        {
            opacity: 0,
            scale: 1
        }, 250, 'easeOutQuart', function () {
            document.getElementsByTagName("body")[0].removeChild(document.getElementById("popup_background"));
            document.getElementsByTagName("body")[0].removeChild(document.getElementById("popup_container"));
        });
}
var user_pin = "";

function get_pin_page_change(success) {
    if (!success) { success = function () { }; }

    get_pin_page(success, function () {
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/check_pin.php",
            data: {
                pin: user_pin
            },
            success: function (data) {
                var result = JSON.parse(data);
                if (result.status == 1) {
                    for (var i = 0; i < 6; i++) {
                        $("#dot" + i).css("background-color", "#7FC5C6");
                        user_pin = ""
                    }
                    get_pin_page_add(success)


                }
                else {
                    $(".text-2").html("ลองใหม่อีกครั้ง <br>");

                    $(".pin-lock").transition({ x: 20 }, 50, 'ease').transition({ x: -20 }, 50, 'ease').transition({ x: 20 }, 50, 'ease ').transition({ x: -20 }, 50, 'ease').transition({ x: 20 }, 50, 'ease').transition({ x: -20 }, 50, 'ease').transition({ x: 0 }, 250, 'easeOutQuad');

                    if (window.TapticEngine) {
                        TapticEngine.notification({ type: "error" });
                    }

                    for (var i = 0; i < 6; i++) {
                        $("#dot" + i).css("background-color", "#7FC5C6");
                        user_pin = ""
                    }
                }
            },
        });
    })


    $(".text-2").html("กรุณากรอกรหัสผ่านเดิม<br/>ของ ThaiNy");
}


function get_pin_page_add(success) {
    if (!success) { success = function () { }; }

    get_pin_page(success, function () {
        //first click
        if (!sessionStorage.temp_pin) {
            sessionStorage.temp_pin = user_pin;
            for (var i = 0; i < 6; i++) {
                $("#dot" + i).css("background-color", "#7FC5C6");
                user_pin = ""
            }

            $(".text-2").html("กรุณากรอกรหัสผ่านอีกครั้ง");
        }
        //second click
        else {
            if (user_pin == sessionStorage.temp_pin) {

                $("#text-error1").text("กำลังตรวจสอบ");
                $("#text-error2").hide();
                
                $.ajax({
                    type: "POST",
                    url: "https://techvernity.com/thainy/php/set_pin.php",
                    data: {
                        pin: sessionStorage.temp_pin
                    },
                    success: function (data) {
                        var result = JSON.parse(data);
                        if (result.status == 1) {
                            localStorage.master_pin = sessionStorage.temp_pin;
                            alert_success({
                                text: "ตั้งรหัสผ่านสำเร็จ",
                                button_func_1: function () {
                                    pin_page_hide(function(){
                                        success();
                                    });
                                },
                                on_top: true
                            });
                        }

                        sessionStorage.removeItem("temp_pin");
                    },
                });

            } else {
                $(".text-2").html("ลองใหม่อีกครั้ง <br>");

                $(".pin-lock").transition({ x: 20 }, 50, 'ease').transition({ x: -20 }, 50, 'ease').transition({ x: 20 }, 50, 'ease ').transition({ x: -20 }, 50, 'ease').transition({ x: 20 }, 50, 'ease').transition({ x: -20 }, 50, 'ease').transition({ x: 0 }, 250, 'easeOutQuad');

                if (window.TapticEngine) {
                    TapticEngine.notification({ type: "error" });
                }

                for (var i = 0; i < 6; i++) {
                    $("#dot" + i).css("background-color", "#7FC5C6");
                    user_pin = ""
                }
            }
        }

    })

    $(".text-2").html("กรุณากรอกรหัสผ่านครั้งแรก<br/>สำหรับใช้งาน ThaiNy");
}

function get_pin_page_check(success) {
    if (!success) { success = function () { }; }

    get_pin_page(success, function () {
        if (user_pin == localStorage.master_pin) {
            pin_page_hide(function () {
                sessionStorage.pin_submit = 1;
                $("#text-error1").text("รหัสผ่านถูกต้อง");
                $("#text-error2").hide();
                success();
            });

        } else {
            $(".text-2").html("ลองใหม่อีกครั้ง <br>");

            $(".pin-lock").transition({ x: 20 }, 50, 'ease').transition({ x: -20 }, 50, 'ease').transition({ x: 20 }, 50, 'ease ').transition({ x: -20 }, 50, 'ease').transition({ x: 20 }, 50, 'ease').transition({ x: -20 }, 50, 'ease').transition({ x: 0 }, 250, 'easeOutQuad');

            if (window.TapticEngine) {
                TapticEngine.notification({ type: "error" });
            }

            for (var i = 0; i < 6; i++) {
                $("#dot" + i).css("background-color", "#7FC5C6");
                user_pin = ""
            }
        }
    })
}

function get_pin_page(success, submit_pin_page) {
    if (!success) { success = function () { }; }
    if (!submit_pin_page) { submit_pin_page = function () { }; }

    var pin_box = $("#pin_box");

    if (!pin_box.length) {
        $("body").prepend('<div id="pin_box" class="pin-box" hidden>' +
            '<div class="ipd-box">' +
            '<div id="pin_back" style="position: absolute; right: 20px; top: 20px;" hidden>ยกเลิก</div>' +
            '<img src="img/locked.svg">' +
            '<div class="text-1">ป้อนรหัสผ่าน</div>' +
            '<div class="text-2 h-text-2">ต้องการรหัสผ่านเพื่อ<br />เข้าใช้งาน ThaiNy ต่อ</div>' +
            '<div class="pin-lock">' +
            '<span id="dot0"></span>' +
            '<span id="dot1"></span>' +
            '<span id="dot2"></span>' +
            '<span id="dot3"></span>' +
            '<span id="dot4"></span>' +
            '<span id="dot5"></span>' +
            '</div>' +
            '<div class="pin-main">' +
            '<div class="first-box" id="one-btn"><span class="set_center">1</span></div>' +
            '<div class="not-first" id="two-btn"><span class="set_center">2</span></div>' +
            '<div class="not-first" id="three-btn"><span class="set_center">3</span></div><br />' +
            '<div class="first-box" id="four-btn"><span class="set_center">4</span></div>' +
            '<div class="not-first" id="five-btn"><span class="set_center">5</span></div>' +
            '<div class="not-first" id="six-btn"><span class="set_center">6</span></div><br />' +
            '<div class="first-box" id="seven-btn"><span class="set_center">7</span></div>' +
            '<div class="not-first" id="eight-btn"><span class="set_center">8</span></div>' +
            '<div class="not-first" id="nine-btn"><span class="set_center">9</span></div><br />' +
            '<div class="first-box" id="zero-btn"><span class="set_center">0</span></div>' +
            '</div>' +
            '<div class="foot-box">' +
            '<div id="fingerprint"><span id="settxt_finger_face" class="set_center" ></span></div>' +
            '<div id="delete-btn"><span class="set_center" >ลบ</span></div>' +
            '</div>' +
            '</div>' +
            '' +
            '</div>');

        pin_box = $("#pin_box");
    }

    $("#one-btn, #two-btn, #three-btn, #four-btn, #five-btn, #six-btn, #seven-btn, #eight-btn, #nine-btn, #zero-btn, #delete-btn").unbind("touchstart")

    $("#one-btn").on("touchstart", function () {
        pin_click(1)
    })
    $("#two-btn").on("touchstart", function () {
        pin_click(2)
    })
    $("#three-btn").on("touchstart", function () {
        pin_click(3)
    })
    $("#four-btn").on("touchstart", function () {
        pin_click(4)
    })
    $("#five-btn").on("touchstart", function () {
        pin_click(5)
    })
    $("#six-btn").on("touchstart", function () {
        pin_click(6)
    })
    $("#seven-btn").on("touchstart", function () {
        pin_click(7)
    })
    $("#eight-btn").on("touchstart", function () {
        pin_click(8)
    })
    $("#nine-btn").on("touchstart", function () {
        pin_click(9)
    })
    $("#zero-btn").on("touchstart", function () {
        pin_click(0)
    })
    $("#delete-btn").on("touchstart", function () {
        if (user_pin.length > 0) {
            user_pin = user_pin.substring(0, user_pin.length - 1);
            console.log(user_pin);
            $("#dot" + user_pin.length).css("background-color", "#7FC5C6");
        }
    })

    if (!!pin_box.prop("hidden")) {
        pin_page_show()
    }

    function pin_click(number) {
        if (user_pin.length < 6) {
            user_pin += number;
            $("#dot" + (user_pin.length - 1)).css("background-color", "white");


            if (user_pin.length == 6) {
                submit_pin_page();
            }
        }
    }
}

function checkBioAuthen() {
    Fingerprint.isAvailable(isAvailableSuccess, isAvailableError);
    function isAvailableSuccess(result) {
        /*
        result depends on device and os. 
        iPhone X will return 'face' other Android or iOS devices will return 'finger'  
        */
        // alert(result);
        $('#list_setting_finger').slideDown(250);
        if (result == "face") {
            $('#settxt_finger_face_list').html("ใช้ Face ID สำหรับปลดล็อค");
            $('#icon_auth_bio').attr("src", "img/setting/faceid.svg")
        } else {
            if (device.platform == "iOS") {
                $('#settxt_finger_face_list').html("ใช้ Touch ID สำหรับปลดล็อค");
            } else {
                $('#settxt_finger_face_list').html("สแกนลายนิ้วมือสำหรับปลดล็อค");
            }
        }

    }

    function isAvailableError(message) {
        // alert(message);
        $('#list_setting_finger').slideUp(250);
    }
}


function BioAuthen() {
    Fingerprint.isAvailable(isAvailableSuccess, isAvailableError);

    function isAvailableSuccess(result) {
        if (localStorage.BioAuthenStatus == "on") {
            /*
            result depends on device and os. 
            iPhone X will return 'face' other Android or iOS devices will return 'finger'  
            */
            // alert(result);
            if (result == "face") {
                $('#settxt_finger_face').html("Face ID");
            } else {
                $('#settxt_finger_face').html("สแกนนิ้ว");
            }

            Fingerprint.show({
                clientId: "เข้าใช้งานด้วย Touch ID หรือ ยกเลิกเพื่อกลับไปใช้รหัส",
                clientSecret: "password" //Only necessary for Android
            }, successCallback, errorCallback);

            function successCallback() {
                //alert("Authentication successfull");
                if (window.TapticEngine) {
                    TapticEngine.notification({ type: "success" });
                }
                pin_page_hide();
            }

            function errorCallback(err) {
                // alert("Authentication invalid " + err);
            }
        }

    }

    function isAvailableError(message) {
        // alert(message);
    }

}

function pin_page_show() {
    var pin_box = $("#pin_box");
    pin_box.css("opacity", 0);
    pin_box.removeAttr("hidden")
    pin_box.transition(
        {
            opacity: 1,
            scale: 1
        }, 250, 'easeOutQuart');

}

function pin_page_hide(success) {
    if (!success) { success = function () { }; }

    var pin_box = $("#pin_box");
    pin_box.transition(
        {
            y: '100vh'
        }, 500, 'easeInQuad', function () {
            pin_box.attr("hidden", true);
            pin_box.transition({ y: 0 }, 0);
            user_pin = "";
            success();
        });

    for (var i = 0; i < 6; i++) {
        $(".text-2").html("ต้องการรหัสผ่านเพื่อ<br />เข้าใช้งาน ThaiNy ต่อ");
        $("#dot" + i).css("background-color", "#7FC5C6");
        user_pin = ""
    }
}



function get_thai_date(date) {
    var month_list = ["มกราคม", "กุมภาพันธ์", "มีนาคม", "เมษายน", "พฤษภาคม", "มิถุนายน", "กรกฎาคม", "สิงหาคม", "กันยายน", "ตุลาคม", "พฤศจิกายน", "ธันวาคม"];
    return date.getDate() + " " + month_list[date.getMonth()] + " " + (parseInt(date.getFullYear()) + 543);
}
function get_time() {
    var date = new Date();
    return date.getHours() + ":" + date.getMinutes();
}
function ajaxError(jqXHR, exception) {
    var msg = '';
    if (jqXHR.status === 0) {
        msg = 'Not connect.\n Verify Network.';
    } else if (jqXHR.status == 404) {
        msg = 'Requested page not found. [404]';
    } else if (jqXHR.status == 500) {
        msg = 'Internal Server Error [500].';
    } else if (exception === 'parsererror') {
        msg = 'Requested JSON parse failed.';
    } else if (exception === 'timeout') {
        msg = 'Time out error.';
    } else if (exception === 'abort') {
        msg = 'Ajax request aborted.';
    } else {
        msg = 'Uncaught Error.\n' + jqXHR.responseText;
    }

    return msg;
}

function getDate(i,date) {
    var a = new Date();
    if(!!date){
        a = new Date(date);
    }
    if (!!i) {
        a.setDate(a.getDate() + i);
    }
    var month = "";
    var day = "";
    if (a.getDate() < 10) {
        day = "0";
    }
    if (a.getMonth() + 1 < 10) {
        month = "0";
    }
    day += a.getDate();
    month += a.getMonth() + 1;
    return a.getFullYear() + "-" + month + "-" + day;//+ " " + a.getHours() + ":" + a.getMinutes();
}

function check_all_data(name) { ///easy example
    var ob = get_all_data(name);
    for (var i = 0; i < ob.length; i++) {

        var dom = document.getElementsByName(name + (i + 1));
        if (ob[i] == "" && dom[0].type != "checkbox") {
            return false;
        } else if (ob[i][0] == "checkbox" && ob[i].length == 1 && dom[0].type == "checkbox") {
            return false;
        }
    }
    return true;
}

function dimension_disable(name) {
    for (var i = 1; document.getElementsByName(name + "_" + i)[0] != undefined; i++) {
        for (var j = 0; j < document.getElementsByName(name + "_" + i).length; j++) {
            document.getElementsByName(name + "_" + i)[j].disabled = true;
            if (document.getElementsByName(name + "_" + i)[j] != undefined) {
                if (document.getElementsByName(document.getElementsByName(name + "_" + i)[j].value + "_etc")[0] != undefined) {
                    document.getElementsByName(document.getElementsByName(name + "_" + i)[j].value + "_etc")[0].disabled = true;
                }
            }
        }


    }
}

function dimension_enable(name) {
    for (var i = 1; document.getElementsByName(name + "_" + i)[0] != undefined; i++) {
        for (var j = 0; j < document.getElementsByName(name + "_" + i).length; j++) {
            document.getElementsByName(name + "_" + i)[j].disabled = false;
            if (document.getElementsByName(name + "_" + i)[j] != undefined) {
                if (document.getElementsByName(document.getElementsByName(name + "_" + i)[j].value + "_etc")[0] != undefined) {
                    document.getElementsByName(document.getElementsByName(name + "_" + i)[j].value + "_etc")[0].disabled = false;
                }
            }
        }

    }

}

function dimensionx(name) {
    var ob = [];
    for (var i = 1; document.getElementsByName(name + i)[0] != undefined; i++) {

        var temp = [];
        //console.log(document.getElementsByName(name + i)[0].value);

        /// first of data , why always 'on'
        // if(document.getElementsByName(name + i)[0].value!="on"){
        //     console.log(document.getElementsByName(name + i)[0].value);
        // }
        for (var j = 1; document.getElementsByName(name + i + "_" + j)[0] != undefined; j++) {

            if (document.getElementsByName(name + i + "_" + j)[0].type == "radio") {
                var ischecked = false;
                for (var k = 0; document.getElementsByName(name + i + "_" + j)[k] != undefined; k++) {
                    if (document.getElementsByName(name + i + "_" + j)[k].checked == true) {

                        temp.push(Array(document.getElementsByName(name + i + "_" + j)[k].value));
                        ischecked = true;
                    }
                }
                if (ischecked == false) {
                    temp.push(Array(""));
                }
            } else if (document.getElementsByName(name + i + "_" + j)[0].type == "checkbox") {
                if (document.getElementsByName(name + i + "_" + j)[0].checked == true) {
                    temp.push(Array(document.getElementsByName(name + i + "_" + j)[0].value));
                } else {
                    temp.push(Array(""));
                }
            } else {
                temp.push(Array(document.getElementsByName(name + i + "_" + j)[0].value));
            }
        }
        // console.log(temp);

        ob.push(temp);
    }
    return ob;
}

function encodeURIComponentKeyValue(key, value, isFirstValue) {
    if (!isFirstValue) {
        return "&" + key + "=" + encodeURIComponent(value);
    }
    else {
        return key + "=" + encodeURIComponent(value);
    }
}

function get_all_data(name) {

    var num = 0;
    var obs = [];
    for (var i = 1; document.getElementsByName(name + i)[0] != undefined; i++) {

        var ob = [];
        var temp = document.getElementsByName(name + i);
        if (temp[0] != undefined && temp[0].type == "checkbox") {
            ob.push("checkbox");
        }
        for (var j = 0; temp[j] != undefined; j++) {
            if (temp[j].type == "radio" && temp[j].checked == true) {
                if (document.getElementsByName(temp[j].value + "_etc").length >= 2) {
                    ob.push("no_data_access");
                }
                else if (document.getElementsByName(temp[j].value + "_etc")[0] != undefined) {
                    ob.push(document.getElementsByName(temp[j].value + "_etc")[0].value);
                } else {
                    ob.push(temp[j].value);

                }
            } else if (temp[j].type == "checkbox" && temp[j].checked == true) {
                if (document.getElementsByName(temp[j].value + "_etc").length >= 2) {
                    ob.push("no_data_access");
                }
                else if (document.getElementsByName(temp[j].value + "_etc")[0] != undefined) {
                    ob.push(document.getElementsByName(temp[j].value + "_etc")[0].value);
                } else {
                    ob.push(temp[j].value);
                }
            } else if (temp[j].type != "radio" && temp[j].type != "checkbox") {
                ob.push(temp[j].value);
            }
            num++;
        }
        obs.push(ob);
    }
    //alert(obs);
    return obs;
}
function push_all_data(name, ob) {
    // alert(ob.length);
    if (!Array.isArray(ob)) {
        ob = ob_to_array(ob);
        // alert(ob.length);
    }
    var number = 0;
    for (var i = 1; document.getElementsByName(name + i)[0] != undefined; i++) {

    }
    number = i - 1;
    /// alert(number);
    for (; ob.length > number;) {
        ob.shift();
    }
    // alert(ob.length);
    for (var i = 1; document.getElementsByName(name + i)[0] != undefined; i++) {
        var temp = document.getElementsByName(name + i);
        for (var j = 0; j < temp.length; j++) {
            // console.log(temp[j].value);
            if (document.getElementsByName(temp[j].value + "_etc")[0] != undefined && ob[i - 1] != "") {

                temp[j].checked = true;
                document.getElementsByName(temp[j].value + "_etc")[0].value = ob[i - 1];

            }
            else if (temp[j].type == "radio" && ob[i - 1] == temp[j].value) {
                temp[j].checked = true;
            }
            else if (temp[j].type == "checkbox" && ob[i - 1] == temp[j].value) {
                temp[j].checked = true;
            }
            else if (temp[j].type != "checkbox" && temp[j].type != "radio") {
                temp[j].value = ob[i - 1];
            }


            $(temp[j]).trigger('change');
            
            if(!!ob[i - 1]){
                $(temp[j]).trigger('touchstart');
            }
            
            // $(temp[j]).trigger('click');
        }
    }

}
function push_all_section_data(name, ob) {
    // alert(ob.length);
    if (!Array.isArray(ob)) {
        ob = ob_to_array(ob);
        // alert(ob.length);
    }
    ob.pop(); ob.pop(); ob.pop();
    var number = 0;
    for (var i = 1; document.getElementsByName(name + i)[0] != undefined; i++) {

    }
    number = i - 1;
    /// alert(number);
    for (; ob.length > number;) {
        ob.shift();
    }
    // alert(ob.length);
    for (var i = 1; document.getElementsByName(name + i)[0] != undefined; i++) {
        var temp = document.getElementsByName(name + i);
        for (var j = 0; j < temp.length; j++) {
            // console.log(temp[j].value);
            if (document.getElementsByName(temp[j].value + "_etc")[0] != undefined && ob[i - 1] != "") {
                
                temp[j].checked = true;
                document.getElementsByName(temp[j].value + "_etc")[0].value = ob[i - 1];

            }
            else if (temp[j].type == "radio" && ob[i - 1] == temp[j].value) {
                temp[j].checked = true;
            }
            else if (temp[j].type == "checkbox" && ob[i - 1] == temp[j].value) {
                temp[j].checked = true;
            }
            else if (temp[j].type != "checkbox" && temp[j].type != "radio") {
                temp[j].value = ob[i - 1];
            }


           // if ($(temp[j]).prop("checked") == true || $(temp[j]).prop("tagName") == "SELECT") {
                $(temp[j]).trigger('change');
          

            // $(temp[j]).trigger('click');
        }
    }

}
function ob_to_array(temp) {
    var array = $.map(temp, function (value, index) {
        return [value];
    });
    return array;
}
function multiJS(id) {
    var string = document.getElementById(id).innerHTML;
    var temp = "";
    var close = false;
    var quote = false;
    var first_time = true;

    for (var i = 0; i < string.length - 1; i++) {

        if (string[i] == "\n" && close) {
            temp += "'\n";
            close = false;
        } else if (string[i].match("^\\s+$") && !string[i + 1].match("^\\s+$") && !close) {
            if (!first_time) {
                temp += "+";

            }
            temp += "'";
            first_time = false;
            close = true;
        } else if (string[i] == "'") {
            //if(!quote){
            temp += "'" + '+"' + "'" + '"' + "+'";
            // }else{
            //temp += "'"+'+"'+"'"+'"'+"+'";
            //}
        } else {
            temp += string[i];
        }

    }
    console.log(temp);
}
function set_min_max(name, min, max) {
    if (document.getElementsByName(name)[0] != undefined) {
        document.getElementsByName(name)[0].type = "number";
        document.getElementsByName(name)[0].onkeyup = function (e) {
            if (parseFloat(document.getElementsByName(name)[0].value) > max) {
                document.getElementsByName(name)[0].value = document.getElementsByName(name)[0].value.toString().slice(0, parseInt(document.getElementsByName(name)[0].value.length) - 1);
            }

            if (parseFloat(document.getElementsByName(name)[0].value) < min) {
                document.getElementsByName(name)[0].value = min;
            }

        }
    } else {
        document.getElementById(name).onkeyup = function (e) {
            document.getElementById(name).type = "number";
            if (parseFloat(document.getElementById(name).value) > max) {
                document.getElementById(name).value = document.getElementById(name).value.toString().slice(0, parseInt(document.getElementById(name).value.length) - 1);
            }

            if (parseFloat(document.getElementById(name).value) < min) {
                document.getElementById(name).value = min;
            }
        }
    }
}
function floatx(name, x) {
    $("input[name='" + name + "']").on("change", function () {
        if ($(this).val() != "") {
            var val = $(this).val();
            val = parseFloat(val);
            val = val.toFixed(x);
            $(this).val(val);
        }
    });

}
function passwordx(mode,name){
    if(mode=="setting"){
        $("input[name='" + name + "']").on("keyup", function (e) {
            if(!check_eng(e.key)&&e.key!="_"&&!e.key.toString().match("[0-9]")){
                $(this).val($(this).val().toString().substring(0,$(this).val().toString().length-1));
            }
        });
    }else if(mode=="check"){
        if(!check_eng($("input[name='" + name + "']").val())&&$("input[name='" + name + "']").val()!="_"&&!$("input[name='" + name + "']").val().toString().match("[0-9]")){
           return false;
        }
        else {
            return true;
        }
    }
    
}
function check_eng(value){
    if(value.toString().match("[a-zA-Z]")){
        return true;
    }else{
        return false;
    }
}
function diffDate(start, stop, time_flag) {
    if (start != "" && stop != "") {
        var date1 = new Date(start);
        var date2 = new Date(stop);
        var timeDiff = Math.abs(date2.getTime() - date1.getTime());
        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));


        if (time_flag == "y") {
            return parseInt(diffDays / 360);
        }
        else if (time_flag == "m") {
            return parseInt(diffDays / 30);
        } else if (time_flag == "d") {
            return parseInt(diffDays);
        }
    }
}

function diffTime(start, stop, time_flag) {
    if (start != "" && stop != "") {
        var date1 = new Date(start);
        var date2 = new Date(stop);
        var timeDiff = Math.abs(date2.getTime() - date1.getTime());

        if (time_flag == "h") {
            return parseInt(timeDiff / (1000 * 3600) );
        }
        else if (time_flag == "m") {
            return parseInt(timeDiff / (1000 * 60));
        }
    }
}
function get_criteria(){
    var list = sessionStorage.criteria.toString().split(";");
    var criteria = {};
    for(var i = 0;i<list.length;i++){
        criteria[list[i]] = "1";
    }
    return criteria;
}
function dis_ob(col, start, stop) {
    for (var i = start; i <= stop; i++) {
        for (var j = 0; document.getElementsByName(col[i])[j] != undefined; j++) {
            var temp = document.getElementsByName(col[i])[j];
            if (temp.type == "checkbox" || temp.type == "radio") {
                temp.checked = false;
            } else {
                temp.value = "";
            }
            // if(document.getElementsByName(temp.name+"_main")[0]!=undefined){
            //    // alert(temp.name+"_main");
            //     $("input[name='"+temp.name+"_main']").trigger("change");
            // }
            $(temp).trigger("change");
        }
    }
}
function dis(name, start, stop) {
    for (var i = start; i <= stop; i++) {
        for (var j = 0; document.getElementsByName(name + i)[j] != undefined; j++) {
            var temp = document.getElementsByName(name + i)[j];
            if (temp.type == "checkbox" || temp.type == "radio") {
                temp.checked = false;
                if (document.getElementsByName(temp.value + "_etc")[0] != undefined) {
                    document.getElementsByName(temp.value + "_etc")[0].value = "";
                }
            } else {
                temp.value = "";
            }
            $(temp).trigger("change");
        }
    }
}
function array_cut(array, index) {
    if (!Array.isArray(array)) {
        return false;
    }
    array.splice(index, 1);
    return array;
}
function array_insert(array, index, item) {
    if (!Array.isArray(array)) {
        return false;
    }
    array.splice(index, 0, item);
    return array;
}
function get_column(db_name, table) {
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/get_column.php",
        data: {
            db_name: db_name,
            table: table
        },
        success: function (data) {
            console.log(data);
        },
    });
}
function get_ob_data(ob) {
    var temp = {};
    for (var i = 0; i < ob.length; i++) {
        if (document.getElementsByName(ob[i])[0] != undefined) {
            var ex = document.getElementsByName(ob[i])[0];
            if (ex.type == "radio" || ex.type == "checkbox") {
                ex = document.getElementsByName(ob[i]);
                temp[ob[i]] = "";
                for (var j = 0; j < ex.length; j++) {
                    if (ex[j].checked == true) {
                        temp[ob[i]] += ex[j].value;
                    }
                }
            } else {
                temp[ob[i]] = ex.value;
            }
        } else {
            temp[ob[i]] = "";
        }
    }
    return temp;
}

function array_to_ob(col, array) {
    if (Array.isArray(array[0])) {
        var temp = [];
        for (var i = 0; i < array.length; i++) {
            var ob = {};
            for (var j = 0; j < col.length; j++) {
                ob[col[j]] = array[i][j];
                //console.log(col[j]);
            }
            temp.push(ob);
        }
        return temp;
    } else {

    }

}
function push_ob_data(ob) {
    var keys = Object.keys(ob);
    for (var i = 0; i < keys.length; i++) {
        if (document.getElementsByName(keys[i])[0] != undefined) {
            var ex = document.getElementsByName(keys[i])[0];
            if (ex.type == "radio" || ex.type == "checkbox") {
                // console.log("test");
                ex = document.getElementsByName(keys[i]);
                for (var j = 0; j < ex.length; j++) {
                    if (ex[j].value == ob[keys[i]]) {
                        ex[j].checked = true;
                        $(ex[j]).trigger('change');
                    } else {
                        ex[j].checked = false;
                    }

                }
            } else {
                // console.log(ob[keys[i]]);
                document.getElementsByName(keys[i])[0].value = ob[keys[i]];
                $(document.getElementsByName(keys[i])[0]).trigger('change');

                if(!!ob[keys[i]]){
                    $(document.getElementsByName(keys[i])[0]).trigger("mouseenter");
                }
            }

           

        }
    }
}
function check_status(temp,must){
    if(temp.length!=0){
      if(!must){
          status = "not done";
          var temp = ob_to_array(temp[0]);
         // console.log(temp);
          temp.shift();
          temp.shift();
          temp.pop();
          temp.pop();
          temp.pop();
          for(var i = 0;i<temp.length;i++){
            if(temp[i]!=""){
              status = "done";
            }
          }
          return status;
      }else{
          status = "done";
          var temp = ob_to_array(temp[0]);
          console.log(temp);
          temp.shift();
          temp.shift();
          temp.pop();
          temp.pop();
          temp.pop();
          console.log(temp.length);
          for(var i = 0;i<temp.length;i++){
            if(temp[i]==""){
              status = "not done";
            }
            console.log(temp[i]);
          }
          return status;  
      }
    }else{
      return "not done";
    }
    
  }
  var first_date_data = "";
  function get_first_date(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_first_date.php",
        success: function (data) {
            first_date_data =  data;
        },
      });
  }
  var first_hospital_date_data = "";
  function get_first_hospital_date(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_first_hospital_date.php",
        success: function (data) {
           // alert(data);
            first_hospital_date_data =  data;
        },
      });
  }
  var birth_date_data = "";
  function get_birth_date(success){
    ajax("get_birth_date.php",function () {
            birth_date_data =  result;

            if(!!success){
                success();
            }
        },
    );
  }
  var birth_datetime_data = "";
  function get_birth_datetime(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_birth_datetime.php",
        success: function (data) {
            birth_datetime_data =  data;
        },
      });
  }
  var gastation = "";
  function get_gastation(success){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_gastation.php",
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            gastation = result;
            if(!!success){
                success();
            }
        },
      });
  }

  function getPage() {
    pathArray = window.location.pathname.split("/");
    return pathArray[pathArray.length - 1];
}
function setting_input(input,element){
    //alert(element);
    if(element.disabled == false){
        element.type = input;
    }
}
function search_client(ob_list,searching,call_back){
    var value = searching;
    result = ob_list.filter(function(element) {
    var keys = Object.keys(ob_list[0]);
       // console.log(keys);
            for(var i = 0;i<keys.length;i++){
                if(!!element[keys[i]]){
                    if(element[keys[i]].indexOf(value) != -1 ){
                        return element[keys[i]].indexOf(value) != -1 ;
                    }
                }
            }
          });

    if(!!call_back){
        call_back();
    }
    return result;
}

function padLeft(nr, n, str){
    return Array(n-String(nr).length+1).join(str||'0')+nr;
}

var project_url = "https://techvernity.com/thainy/php/";
function ajax(phpfile,data,success){
    var real_data;
    if (typeof data != "object") {
       success = data;
    }
    else{
        if(!data){
            real_data = {};
        }else{
            real_data = data;
        }
    }
    $.ajax({
            type: "POST",
            url: project_url+phpfile,
            data:real_data,
            success: function (data) {
                if(!!data){
                    try{
                        result = JSON.parse(data);
                    }
                    catch(e){
                        result = data;
                    }
                }
                if(!!success){
                    success();
                }
            },
        });
}

function ajax_loading(phpfile,data,success){
    var real_data;
    if (typeof data != "object") {
       success = data;
    }
    else{
        if(!data){
            real_data = {};
        }else{
            real_data = data;
        }
    }
    if(document.getElementById("ajax_loading_popup_background")==undefined){
        $("body").prepend('<div id="ajax_loading_popup_background" style = " width: 100%;height: 100%;position: fixed;box-sizing: border-box;background-color: rgba(0 , 0, 0, 0.25);z-index: 1000;padding: 13%;" >'
        +'<div style = "position:relative;height:120px;width:270px;background-color:white; border-radius: 14px;left: calc(50% - (270px / 2));top:calc(50% - 60px);">'

        +'<img src = "https://techvernity.com/Ellipsis-1.5s-200px.svg" style = "position:relative;height:40%;width:100%;margin-top:24px;">'
        +'<div  style = "width:100%;text-align:center;font-size:17px;margin-bottom:24px;">กำลังโหลด</div>'
        +'</div>'
        +'</div>');
    }
 

    $.ajax({
            type: "POST",
            url: project_url+phpfile,
            data:real_data,  
            success: function (data) {
                setTimeout(function(){
                    if(!!success){
                        if(!success.toString().match(".*ajax_loading.*")){
                            $("#ajax_loading_popup_background").fadeOut(300, function(){
                                document.getElementsByTagName("body")[0].removeChild(document.getElementById("ajax_loading_popup_background"));
                            })
                            
                        }
                    } else{
                            $("#ajax_loading_popup_background").fadeOut(300, function(){
                                document.getElementsByTagName("body")[0].removeChild(document.getElementById("ajax_loading_popup_background"));
                            })
                    } 
                    if(!!data){
                        try{
                            result = JSON.parse(data);
                        }
                        catch(e){
                            result = data;
                        }
                    }
                    if(!!success){
                        success();
                    }
                },1000);
            },
        });

}

function timeline_min_max(min_type, max_type){
    var min, max;
    if(min_type == "birth_date"){
        min = birth_date_data;    
    }

    if(max_type == "now"){
        max = new Date().yyyymmdd();
    }
    
    if(!!min){
        $("input[data-input='date']").attr("min", min)
    }
   
    if(!!max){
        $("input[data-input='date']").attr("max", max)
    }
}

function timeline_validate(check_element){
    var check = true;
    var date = [];

    var check_date = $(check_element).val();
    var check_date_array;
    var elements = $("input[data-validate='l'], input[data-validate='r']");
    var i = 0;

    var now = new Date().yyyymmdd();

    //get date array
    while(i < elements.length){
        var date_1 = "";
        var date_2 = "";
        var validate_1, validate_2;
        
        date_1 = $(elements[i]).val();
        validate_1 = $(elements[i]).attr("data-validate");

        if(validate_1 == "l"){
            if(!!elements[i+1]){
                date_2 = $(elements[i+1]).val();
                validate_2 = $(elements[i+1]).attr("data-validate");
            }

            if(validate_2 == "r"){
                if(check_element.is($(elements[i]))) {
                    check_date_array = [date_1, date_2];
                    date_1 = "";
                }

                if(check_element.is($(elements[i+1]))){
                    check_date_array = [date_1, date_2];
                    date_2 = "";
                }

                date.push([date_1, date_2]);
                i++;
            }
            else if(validate_2 != "l"){
                if(check_element.is($(elements[i]))){
                    check_date_array = [date_1, ""];
                    date_1 = "";
                }

                date.push([date_1, ""]);
                i++;
            }
        }
        else if(validate_1 == "r"){
            if(check_element.is($(elements[i]))){
                check_date_array = ["", date_1];
                date_1 = "";
            }

            date.push(["", date_1]);
        }

        i++;
    }

    // default lock
    // if(!!birth_date_data){
    //     date.push([birth_date_data, ""]);
    // }
    // date.push(["", now]);

    if(!!check_date_array){
        for(var i = 0 ; i < date.length ; i++){
            var each_date = date[i];  
            
            // check_date_array overlap  ex: [10,7]
            if(!!check_date_array[1] && check_date_array[0] > check_date_array[1]){
                check = false;
                break;
            }
    
            //min range
            if(!!each_date[0] && !each_date[1] && check_date < each_date[0]){
                check = false;
                break;
            }
    
            //max range
            if(!each_date[0] && !!each_date[1] && check_date > each_date[1]){
                check = false;
                break;
            }
    
            // check_date in range
            if(!!each_date[0] && !!each_date[1]){
                //solo in ranage ex: 7 in [1,8]
                if(check_date_array[0] >= each_date[0] && check_date_array[0] < each_date[1]){
                    check = false;
                    break;
                }
                
                //solo in ranage ex: 7 in [1,8]
                if(check_date_array[1] > each_date[0] && check_date_array[1] <= each_date[1]){
                    check = false;
                    break;
                }
    
                //check_date overlap range
                if(!!check_date_array[0] && !!check_date_array[1] && !(
                      (check_date_array[0] < each_date[0] && check_date_array[1] <= each_date[0]) ||
                      (check_date_array[0] >= each_date[1] && check_date_array[1] > each_date[1])
                    )
                  ){
                    check = false;
                    break;
                }
            }
        }
    }
   


    if(!check){
        alert_fail({
            text: "กรุณาตรวจสอบวันที่ให้ถูกต้อง",
            header: "ผิดพลาด"
        })

        $(check_element).val("").trigger("change")
    }

    return check;
}

//////////////// date zone

var pick_select_year = getDate(0).split("-")[0];
var pick_select_month = getDate(0).split("-")[1];
function custom_date_picker(id,thai){
    ////please include 
    // <link rel="stylesheet" href="https://techvernity.com/asset/backend/date/ui.css">
    // <script src="https://techvernity.com/asset/backend/date/jq.js"></script>
    // <script src="https://techvernity.com/asset/backend/date/jq3.js"></script>
    $("#"+id).datepicker({ dateFormat: 'yy-mm-dd', changeYear : true, changeMonth : true });
    if(!!thai){
        $("#"+id).on("click",function(){

            var date = getDate(0);
            var year  = date.split("-")[0];
            console.log(year);
            document.getElementsByClassName("ui-datepicker-year")[0].innerHTML = "";
            
            //year

            for(var i = (parseInt(year)-100);i<(parseInt(year)+100);i++){
                var select = "";
                if(i==pick_select_year){
                    //alert("test");
                select = "selected";
                }
                document.getElementsByClassName("ui-datepicker-year")[0].innerHTML += "<option value = '"+(i)+"' "+select+">"+(i+543)+"</option>";
            }

            //month
            var month = date.split("-")[1];
            var month_list = ["มกราคม", "กุมภาพันธ์", "มีนาคม", "เมษายน", "พฤษภาคม", "มิถุนายน", "กรกฎาคม", "สิงหาคม", "กันยายน", "ตุลาคม", "พฤศจิกายน", "ธันวาคม"];
            var month_value_list = ["01","02","03","04","05","06","07","08","09","10","11","12"];
            document.getElementsByClassName("ui-datepicker-month")[0].innerHTML  = "";
            for(var i = 0;i<12;i++){
                var select = "";
                if(i==parseInt(pick_select_month)-1){
                    //alert("test");
                select = "selected";
                document.getElementsByClassName("ui-datepicker-month")[0].value = month_value_list[i];
                }
                document.getElementsByClassName("ui-datepicker-month")[0].innerHTML += "<option value = '"+month_value_list[i]+"' "+select+">"+month_list[i]+"</option>";
            }
            
            $("span[title='Sunday']").html("อา");
            $("span[title='Monday']").html("จ");
            $("span[title='Tuesday']").html("อ");
            $("span[title='Wednesday']").html("พ");
            $("span[title='Thursday']").html("พฤ");
            $("span[title='Friday']").html("ศ");
            $("span[title='Saturday']").html("ส");
            $(".ui-datepicker-next").prop("hidden",true);
            $(".ui-datepicker-prev").prop("hidden",true);
            $(".ui-datepicker-month").css("margin-right","2px");
            $(".ui-datepicker-year").css("margin-left","2px");
        
            $(".ui-datepicker-year").on("change",function(){
                pick_select_year = $(this).val();
                change_picker();
            });
            $(".ui-datepicker-month").on("change",function(){
                pick_select_month = $(this).val();
                change_picker();
            });
        });

        function change_picker(){

            var date = getDate(0);
            var year  = date.split("-")[0];
            console.log(year);
            document.getElementsByClassName("ui-datepicker-year")[0].innerHTML = "";
            
            //year

            for(var i = (parseInt(year)-100);i<(parseInt(year)+100);i++){
                var select = "";
                if(i==pick_select_year){
                    //alert("test");
                select = "selected";
                }
                document.getElementsByClassName("ui-datepicker-year")[0].innerHTML += "<option value = '"+(i)+"' "+select+">"+(i+543)+"</option>";
            }

            //month
            var month = date.split("-")[1];
            var month_list = ["มกราคม", "กุมภาพันธ์", "มีนาคม", "เมษายน", "พฤษภาคม", "มิถุนายน", "กรกฎาคม", "สิงหาคม", "กันยายน", "ตุลาคม", "พฤศจิกายน", "ธันวาคม"];
            var month_value_list = ["01","02","03","04","05","06","07","08","09","10","11","12"];
            document.getElementsByClassName("ui-datepicker-month")[0].innerHTML  = "";
            for(var i = 0;i<12;i++){
                var select = "";
                if(i==parseInt(pick_select_month)-1){
                    //alert("test");
                select = "selected";
                document.getElementsByClassName("ui-datepicker-month")[0].value = month_value_list[i];
                }
                document.getElementsByClassName("ui-datepicker-month")[0].innerHTML += "<option value = '"+month_value_list[i]+"' "+select+">"+month_list[i]+"</option>";
            }
            
            $("span[title='Sunday']").html("อา");
            $("span[title='Monday']").html("จ");
            $("span[title='Tuesday']").html("อ");
            $("span[title='Wednesday']").html("พ");
            $("span[title='Thursday']").html("พฤ");
            $("span[title='Friday']").html("ศ");
            $("span[title='Saturday']").html("ส");
            $(".ui-datepicker-next").prop("hidden",true);
            $(".ui-datepicker-prev").prop("hidden",true);
            $(".ui-datepicker-month").css("margin-right","2px");
            $(".ui-datepicker-year").css("margin-left","2px");
        
            $(".ui-datepicker-year").on("change",function(){
                pick_select_year = $(this).val();
                change_picker();
            });
            $(".ui-datepicker-month").on("change",function(){
                pick_select_month = $(this).val();
                change_picker();
            });
            
        }
        document.getElementById(id).onchange = function(){
            $(this).val((get_thai_date(new Date($(this).val()))));
        }
    }
}
