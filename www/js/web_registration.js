window.onload = function(){
     $("input[name='i8']").on("change",function(){
         if($(this).prop("checked")==true){
            $("input[name='ID_etc']").prop("disabled",false);
         }else{
            $("input[name='ID_etc']").prop("disabled",true);
         }
     });
     $("input[name='i9']").on("change",function(){
        if($(this).prop("checked")==true){
           $("input[name='passport_etc']").prop("disabled",false);
        }else{
           $("input[name='passport_etc']").prop("disabled",true);
        }
    });
}

function setting(){
    $("input[name='i8']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='ID_etc']").prop("disabled",false);
        }else{
            $("input[name='ID_etc']").prop("disabled",true);
            $("input[name='ID_etc']").val("");
        }

    });
    $("input[name='i9']").on("change",function(){
        if($(this).prop("checked")==true){
            $("input[name='passport_etc']").prop("disabled",false);
        }else{
            $("input[name='passport_etc']").prop("disabled",true);
            $("input[name='passport_etc']").val("");
        }

    });
}
function choose(sex){
    if(sex == 'male'){
   
    document.getElementById("F1").classList.remove('choose-fm');
    document.getElementById("F1").classList.add('unchoose-fm');
    document.getElementById("M1").classList.remove('unchoose-fm');
    document.getElementById("M1").classList.add('choose-fm');
    document.getElementsByName("i5")[0].value = "Male";

 //   console.log("top:"+sex1);
    }
    if(sex == 'female'){
    
    document.getElementById("M1").classList.remove('choose-fm');
    document.getElementById("M1").classList.add('unchoose-fm');
    document.getElementById("F1").classList.remove('unchoose-fm');
    document.getElementById("F1").classList.add('choose-fm');
    document.getElementsByName("i5")[0].value = "Female";
   
//    console.log("down:"+sex1);
    }
}
function save(){
    var temp = get_all_data("i");
    console.log(temp);
    var pass = true;
    for(var i = 0 ;i<temp.length;i++){
        if(temp[i].length==2&&temp[i][1]==""){
            pass = false;   
        }else if(i==0||i==1||i==3||i==4||i==6){
            if(temp[i]==""){
                pass = false;
                
            }
        }
    }
    if(document.getElementsByName("i8")[0].checked == false&&document.getElementsByName("i9")[0].checked == false){
        pass = false;
    }
    if(!pass){
        alert("please enter data");
    }else{
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/web_add_new_patient.php",
            data:{data:JSON.stringify(temp)},
            success: function (data) {
                var result = JSON.parse(data);
                console.log(result);
                if(data == 0){
                    alert("ข้อมูลผู้ป่วยซ้ำ กรุณาตรวจสอบ");
                }else{
                    alert("บันทึกสำเร็จ");
                }
            },
          });
    }
}