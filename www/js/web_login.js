$(document).ready(function(){

    $(".error-box").css("visibility","hidden");
    $("#username").removeClass("error-type");  
    $("#password").removeClass("error-type");
    $(".popup-fake").hide();
  $(".login-btn").on("click",function(){
       var username = $("#username").val();
       var password = $("#password").val();
      /* error */
      if(!username || !password){
        if(!username){
            $("#error-text").text("กรุณากรอกบัญชีผู้ใช้ให้ครบถ้วน");
            $(".error-text").css("visibility","visible");
            $("#username").addClass("error-type");
            $("#password").removeClass("error-type");  
            $("#username").addClass("current-type"); 
            $(".popup-fake").show();
            $(".popup-text2").text("กรุณากรอกบัญชีผู้ใช้ให้ครบถ้วน");
        }
        if(!password){
            $("#error-text").text("กรุณากรอกรหัสผ่านให้ครบถ้วน");
            $(".error-box").css("visibility","visible");
            $("#password").addClass("error-type");  
            $("#username").removeClass("error-type"); 
            $("#password").addClass("current-type");
            $(".popup-fake").show();
            $(".popup-text2").text("กรุณากรอกรหัสผ่านให้ครบถ้วน"); 
        }
         if(!username && !password){
            $("#error-text").text("กรุณากรอกบัญชีผู้ใช้และรหัสผ่านให้ครบถ้วน");
            $(".error-box").css("visibility","visible");
            $("#username").addClass("error-type"); 
            $("#password").addClass("error-type");
            $(".popup-fake").show();
            $(".popup-text2").text("กรุณากรอกบัญชีผู้ใช้และรหัสผ่านให้ครบถ้วน");
            
        }
      }
      else{
        $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/login.php",
            data: {
                username: username,
                password: password
            },
            success: function (data) {
                var result = JSON.parse(data);

                if(result.status == 1){
                    window.location = "home.html";
                }
                else{
                    $(".error-box").css("visibility","visible");
                    $("#username").addClass("error-type");            
                    $("#password").addClass("error-type");  
                    $("#error-text").text("บัญชีผู้ใช้หรือรหัสผ่านไม่ถูกต้อง");  
                    $(".popup-fake").show();  
                    $(".popup-text2").html("ดูเหมือนว่าบัญชีผู้ใช้หรือ<br /> รหัสผ่านของคุณไม่ถูกต้อง");              
                }
            },
          });
      }
     
  })

  $(".popup-btn").on("click",function(){
    $(".popup-fake").hide();
  })

  $(".forget-btn").on("click",function(){
    window.location = "web_forgetpassword.html";
  })

  $(".forget-btn").on("click",function(){
    //dummy
    var email = "<EMAIL>";

    // $.ajax({
    //     type: "POST",
    //     url: "https://techvernity.com/thainy/php/forget_password.php",
    //     data: {
    //         email: email
    //     },
    //     success: function (data) {
    //         var result = JSON.parse(data);

    //         if(result.status == 1){
    //             alert("success");
    //         }
    //         else{
    //             alert("failed " + result.text);              
    //         }
    //     },
    //   });
      
  });
 
  
});
