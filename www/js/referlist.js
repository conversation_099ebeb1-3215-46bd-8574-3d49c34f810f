var patient_list;

window.onload = function(){
    localStorage.hospital = "";
    // alert("test");
    var status = "active";
    load(status);
}



function load(status){
    document.getElementById("list").innerHTML = "";

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_refer_list.php",
        data:{status:status},
        success: function (data) {
            var result = JSON.parse(data);
            patient_list = result;
            console.log(result);

            for(var i = 0 ; i < result.length; i++){
                console.log(result[i].refer_date);
                add(result[i].fullname, result[i].TNR,result[i].hospital,result[i].refer_date);
            }
            
            document.getElementById("list").innerHTML += '<div class="number-active">จำนวนผู้ป่วย Refer ' + result.length + ' คน</div>';
        },
      });
}
function add(fullname,TNR,hospital,time){
    var text = '<div class="each-list" onclick = "select('+"'"+TNR+"','"+hospital+"','"+time+"'"+')">' +
                  '<div class="name-patient">'+fullname+'</div>' +
                  '<div class="tn-patient">TN#: '+TNR+'</div>' +
                  '<div class="save-btn">รอการรับ</div>' +
                  '<img src="img/next2.svg">' + 
              '</div>';

    document.getElementById("list").innerHTML += text;

    $(".each-list").on("click",function(){
      //  window.location = "patient_display.html"
    })
}
function select(TNR,hospital,time){
    console.log(TNR,hospital);
    ////set TNR
    localStorage.time = time;
    localStorage.hospital = hospital;

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/set_session_TNR.php",
        data:{TNR:TNR},
        success: function (data) {
            var result = JSON.parse(data);

            if(result.status == 1){
                sessionStorage.criteria = result.criteria;
                change_page("patient_display_refer.html", "next");
              //  window.location = "patient_display_refer.html";
            }else{
                alert_fail({text:"กำลังมีผู้อื่นบันทึกข้อมูลอยู่"});
            }
        },
      });
}
