$(document).ready(function(){
  
 
  $(".next-ntn").on("click",function(){
    if($('input[type="checkbox"]').prop("checked") == true){
      ///gen token
      $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/generate_token.php",
        success: function (data) {
            var result = JSON.parse(data);
            console.log(result);
            localStorage.token = result;
            get_pin_page_add(function(){
              window.location = "home.html";
            })
        },
      });
     
    }else if($('input[type="checkbox"]').prop("checked") == false){
      alert_fail({
        text: "กรุณายืนยันข้อตกลงการใช้งาน",
        // button_func_1: function(){
        //   window.location = "home.html";
        // }
      })
    }
  })
})