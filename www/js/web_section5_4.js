
window.onload = function(){
  setting();
  load();
}
function setting(){
  $("input[name='ino1']").on("change",function(){});
  // ---------------------web_5_4---------------------
  $("#back-section5-btn").on("click",function(){
    window.location="web_section5.html";
  });
  //list_bar
  $("#patient_list").on("click",function(){
    window.location="web_patientlist.html";
  });
  $("#track_tn").on("click",function(){
    window.location="web_search_patient.html";
  });
  $("#export").on("click",function(){
    window.location="web_export_data.html";
  });
  $("#help").on("click",function(){
    window.location="web_help_and_support1.html";
  });
  $("#approve").on("click",function(){
    window.location="web_authorize.html";
  });
  $("#setting").on("click",function(){
    window.location="setting.html";
  });
  $("#logout").on("click",function(){
    window.location = "login.html";
  });
  
}
function load(){
  //--------------5_4-----------------
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/get_section5_4.php",
    data:{num:"5_4"},
    success: function (data) {
        var result1 = JSON.parse(data);
        console.log(result1);
        if(result1.length!=0){
            push_all_section_data("ino",result1[0]);
        }
      }
  });
  //
}

function save(){
  //--------------5_4-----------------
  var temp = get_all_data("ino");
  console.log(temp);
  $.ajax({
    type: "POST",
    url: "https://techvernity.com/thainy/php/save_section5_4.php",
    data:{data:JSON.stringify(temp),num:"5_4"},
    success: function (data) {
        var result = JSON.parse(data);
        console.log(result);
        alert_success({text: "บันทีกสำเร็จ", button_func_1: function(){ window.location.reload(); } });
    }
  })
}