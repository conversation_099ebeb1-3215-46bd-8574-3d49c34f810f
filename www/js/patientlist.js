var patient_list;
var status = "active";  
var sort_by = "TNR";
var sort_order = "ASC";
window.onload = function(){
    // $.ajax({
    //     type: "POST",
    //     url: "https://techvernity.com/thainy/php/test_date.php",
    //         success: function (data) {
    //         alert(data);
    //     },
    //   });
    
    localStorage.hospital = "";
    // alert("test");
    setting();

   
     load(status);
    check_refer();

    $("#add").on("click",function(){
        change_page("registry_criteria1.html", "up");
        //window.location = "registry_criteria1.html";
    })
     
    $("#refer").on("click",function(){
        change_page("referlist.html", "next");
      //  window.location = "referlist.html";
    })
    $("input[name='1']").on("click",function(){
        sort_by = $(this).val();
        //alert("test");
    });
    $("input[name='2']").on("click",function(){
        sort_order = $(this).val();
       // alert("test2");
    });
    $("#active").on("click",function(){
        status = "active";
        $("#filter-accept").trigger("click");

        $("#active img").attr("src", "img/activegreen.svg");
        $("#inactive img").attr("src", "img/inactivegray.svg");

        $("#active .footer-text").addClass("text-green")
        $("#active .footer-text").removeClass("text-gray")

        $("#inactive .footer-text").addClass("text-gray")
        $("#inactive .footer-text").removeClass("text-green") 
    })

    $("#inactive").on("click",function(){
        status = "inactive";
        //load(status);
        $("#filter-accept").trigger("click");
        $("#active img").attr("src", "img/activegray.svg");
        $("#inactive img").attr("src", "img/inactivegreen.svg");

        $("#active .footer-text").removeClass("text-green")
        $("#active .footer-text").addClass("text-gray")

        $("#inactive .footer-text").removeClass("text-gray")
        $("#inactive .footer-text").addClass("text-green") 
    })

    $("#search").on("keyup", function(){
        // var value = $(this).val();
        // var result = patient_list.filter(function(element) {
        //     return element.fullname.indexOf(value) != -1 ||
        //             element.TNR.indexOf(value) != -1;
        //   });

        //   $("#list").empty();
        //   for(var i = 0 ; i < result.length; i++){
        //     add(result[i].fullname, result[i].TNR);
        // }
        search_client(patient_list,$(this).val(),function(){
           $("#list").empty();
            for(var i = 0 ; i < result.length; i++){
                add(result[i].fullname, result[i].TNR, status, result[i].refer_status, result[i].status);
            }
        });
    });
   
    $("#sort").on("click",function(){
        $('#popup-sort-box').css(
            {
                scale: 0.25
            }
        );
        $('#popup-sort-box').show(1, function(){
            $('#popup-sort-box').transition(
                {
                    scale: 1
                }
            , 300, 'easeOutBack');
            $('#popup-sort').fadeIn(300);
        });
    })

    $("#filter").on("click",function(){
        $('#popup-filter-box').css(
            {
                scale: 0.25
            }
        );
        $('#popup-filter-box').show(1, function(){
            $('#popup-filter-box').transition(
                {
                    scale: 1
                }
            , 300, 'easeOutBack');
            $('#popup-filter').fadeIn(300);
        });
    })

    $("#filter-cancel").on("click",function(){
        $('#popup-filter-box').transition(
            {
                scale: 0.25
            }
        , 300, 'easeInBack', function(){
            $('#popup-filter-box').hide();
        });
        $('#popup-filter').fadeOut(300);
    })

    $("#sort-cancel").on("click",function(){
        $('#popup-sort-box').transition(
            {
                scale: 0.25
            }
        , 300, 'easeInBack', function(){
            $('#popup-sort-box').hide();
        });
        $('#popup-sort').fadeOut(300);
    })

    $("#sort-accept").on("click",function(){
        $('#popup-sort-box').transition(
            {
                scale: 0.25
            }
        , 300, 'easeInBack', function(){
            $('#popup-sort-box').hide();
        });
        $('#popup-sort').fadeOut(300);


        // if($("#sort-name").is(":checked")) type = "fullname"
        // if($("#sort-TN").is(":checked")) type = "TNR"
        // if($("#sort-dateAdmit").is(":checked")) type = "created_date"
        
        // if($("#sort-min").is(":checked")) sort_by = "ASC"
        // if($("#sort-max").is(":checked")) sort_by = "DESC"

        //sort(type, sort_by, status);
        $("#filter-accept").trigger("click");
    })

    $("#filter-accept").on("click",function(){
        $('#popup-filter-box').transition(
            {
                scale: 0.25
            }
        , 300, 'easeInBack', function(){
            $('#popup-filter-box').hide();
        });
        $('#popup-filter').fadeOut(300);
        
        var all = "";
        var date_start = "";
        var date_stop = "";
        var GA = "";
        var BW = "";
        var HIE = "";
        var major = "";
        var complete = false;
        var incomplete = false;
   
        if($("input[value='all']").prop("checked")==true){
            all = "all";
        }else if($("input[value='not all']").prop("checked")==true){
            all = "not all";
            date_start = $("#date_start").val();
            date_stop = $("#date_stop").val();
        }

        if($("#GA").prop("checked")==true){
            GA = true;
        }
        if($("#BW").prop("checked")==true){
            BW = true;
        }
        if($("#HIE").prop("checked")==true){
            HIE = true;
        }
        if($("#major").prop("checked")==true){
            major = true;
        }

        if(document.getElementsByName("completed")[0].checked==true){
            incomplete = true;
        }
         if(document.getElementsByName("completed")[1].checked==true){
            complete = true;
        }
        
        console.log(all);
        console.log(date_start);
        console.log(date_stop);
        console.log(GA);
        console.log(BW);
        console.log(HIE);
        console.log(major);
        console.log(complete);
        console.log(incomplete);
        console.log(sort_by);
        console.log(sort_order);

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_patient_filter.php",
        data:{all:all, date_start:date_start, date_stop:date_stop,GA:GA,BW:BW,HIE:HIE,major:major,complete:complete,incomplete:incomplete,status:status,sort_by:sort_by,sort_order:sort_order},
        success: function (data) {
            var result = JSON.parse(data);
            patient_list = result;
            console.log(result);
            document.getElementById("list").innerHTML = "";
            for(var i = 0 ; i < result.length; i++){
                add(result[i].fullname, result[i].TNR, status, result[i].refer_status, result[i].status);
            }
            $("#list").append('<div class="number-active">จำนวนผู้ป่วยสถานะ ' + status + ' ' + result.length + ' คน</div>');
        },
      });
        /*
        if($("#sort-name").is(":checked")) type = "fullname"
        if($("#sort-TN").is(":checked")) type = "TNR"
        if($("#sort-dateAdmit").is(":checked")) type = "created_date"
        
        if($("#sort-min").is(":checked")) sort_by = "ASC"
        if($("#sort-max").is(":checked")) sort_by = "DESC"

        sort(type, sort_by, status);
        */
    })

    //$("#filter-name").attr('checked')
    
}

function setting(){
    
    // $("input[name='completed']").on("change",function(){
    //     alert("test");
    // });
    $("input[name='date']").on("change",function(){
        if($(this).val()=="all"){
            $("#date_start").val("");
            $("#date_stop").val("");
            $("#date_start").prop("disabled",true);
            $("#date_stop").prop("disabled",true);
        }else{
            $("#date_start").prop("disabled",false);
            $("#date_stop").prop("disabled",false);
        }
        //alert("test");
    });
}

function load(status){
    sessionStorage.flow1 = undefined;
    sessionStorage.flow2 = undefined;
    sessionStorage.flow3 = undefined;
    sessionStorage.Cname  = undefined;
    sessionStorage.TNR = undefined;
    sessionStorage.HN = undefined;
    sessionStorage.mothername = undefined;

    document.getElementById("list").innerHTML = "";

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_patient_list.php",
        data:{status:status},
        success: function (data) {
            var result = JSON.parse(data);
            patient_list = result;
            console.log(result);

            for(var i = 0 ; i < result.length; i++){
                add(result[i].fullname, result[i].TNR, status, result[i].refer_status);
            }

           $("#list").append('<div class="number-active">จำนวนผู้ป่วยสถานะ ' + status + ' ' + result.length + ' คน</div>');

            $('#list').transition({ y: '150px', opacity: 0 }, 0);
            $('#list').show();
            $('#list').transition({ y: '0', opacity: 1 }, 300, 'easeOutQuad');

        },
      });
}
function add(fullname,TNR,status,patient_status,patient_status2){
    var text;
    
    var patient_status_text;
    var patient_status_color;
    var patient_status_text2;
    var patient_status_color2;

    if(status == "active"){
        patient_status_text = "กำลังบันทึก"
        patient_status_color = "#75ACE6"

        if(!!patient_status){
            if(patient_status == "waiting"){
                patient_status_text = "รอการ refer";
                patient_status_color = "#E5CC62"
            }
            else if(patient_status == "reject"){
                patient_status_text = "ถูกปฎิเสธ";
                patient_status_color = "#E5706A"
            }
        }
        

        text = '<div class="patient_display each-list">'
                            +'<div class="name-patient">'+fullname+'</div>'
                            +'<div class="tn-patient">TN#: '+TNR+'</div>'
                            +'<div class="save-btn" style="background-color: '+patient_status_color+'">'+ patient_status_text +'</div>'
                            +'<img src="img/next2.svg">'
                        +'</div>';
    }
    else{
        if(!!patient_status2){
            if(patient_status2 == "inactive"){
                patient_status_text2 = "discharge home";
                //patient_status_color2 = ""
            }
            else if(patient_status2 == "inactive_refer"){
                patient_status_text2 = "ถูก refer";
                //patient_status_color2 = ""
            }
        }

        text = '<div class="patient_display each-list">'
                //+'<div class="name-patient">'+fullname+'</div>'
                +'<div class="tn-patient" style="color:black">TN#: '+TNR+'</div>'
                +'<div class="status-no-next">'+patient_status_text2+'</div>'
            +'</div>';
    }

    $("#list").append(text);
    
    $(".patient_display").unbind('click');

    if(status == "active"){
        $(".patient_display").on("click",function(){
            var TNR = patient_list[$(this).index()].TNR;
           
            $.ajax({
                type: "POST",
                url: "https://techvernity.com/thainy/php/set_session_TNR.php",
                data:{TNR:TNR},
                success: function (data) {
                    var result = JSON.parse(data);
                   //alert(result.TNR);
                    if(result.status == 1){
                     //   alert( result.criteria);
                        sessionStorage.criteria = result.criteria;
                       //alert(sessionStorage.criteria);
                      // change_page("patient_display.html", "next");
                        change_page("patient_display.html", "next");
                    }else{
                        alert_fail({text: "กำลังมีผู้อื่นบันทึกข้อมูลอยู่", header: "ข้อผิดพลาด"});
                    }
                },
              });
        })
    }
}

function sort(type, sort_by, status){
    document.getElementById("list").innerHTML = "";

    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_patient_sort.php",
        data:{status:status, type:type, sort_by:sort_by},
        success: function (data) {
            var result = JSON.parse(data);
            patient_list = result;
            console.log(result);

            for(var i = 0 ; i < result.length; i++){
                add(result[i].fullname, result[i].TNR, status, result[i].refer_status);
            }
        },
      });
}

function check_refer(){
    $.ajax({
        type: "POST",
        url: "https://techvernity.com/thainy/php/get_refer_list.php",
        data:{},
        success: function (data) {
            var result = JSON.parse(data);
            var refer_count = result.length;
           
            if(refer_count > 0){
                $("#refer_count").text(refer_count);
                $("#refer").slideDown(300);
            }
        },
      });
}


   

