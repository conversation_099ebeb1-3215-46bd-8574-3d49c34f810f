<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box" >
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>TPN / PN use </div>
                        </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="pn1" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-trans2" id = "pop1" hidden>
                            <div><span class="gray-txt">TPN / PN use </span><span class="black-txt" id = "days">4</span>days</div>
                            <div class="add-dynamic">    
                                <div name="TPN1" class="inside-box-with-pad2">
                                    <div class="">วันเริ่มต้น</div>
                                    <input class="input-section input-sec5" name="TPN1_1" type="date" data-input="date"  data-validate="l"  placeholder="เลือกวัน"  ontouchend="setting_input('date',this);">
                                    <div class="margin-top8  ">วันสิ้นสุด</div>
                                    <input class="input-section input-sec5" name="TPN1_2" type="date" data-input="date"  data-validate="r" placeholder="เลือกวัน"  ontouchend="setting_input('date',this);">
                                    <div class="circle-red red-sec5" onclick="del(this)"><img src="img/delete.svg"></div>                                
                                    </div>
                                
                            </div>   
                                    <div class="plus-box" >
                                            <div class="plus" id="plus-btn" >
                                                <div class="content_box">
                                                        <img src="img/add.svg">
                                                </div>
                                            </div>
                                        </div>
                        </div>

                        <label class="container_radio">No
                            <input type="radio" name="pn1" value="No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <!-- btn -->
                        <div class="save-btn section5_6-btn" onclick="save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5_6.js"></script>
</body>

</html>