<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>O<sub>2</sub> supplement </div><img id="info1" src="img/info.svg">
                        </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="" id = "O2_yes" value="Yes" onclick ="response2('check')">
                            <span class="checkmark_radio"></span>
                        </label>

                        <div id= "O2_box">
                        <div class="inside-trans2">
                            <div><span class="gray-txt">O<sub>2</sub></span><span class="black-txt"></span><div id = "o2_day">0 days</div></div>
                
                         
                            <div id = "O2_contain">
                                        <div class="inside-box-with-pad2" id = "o2_pop">
                                            <div>วันเริ่มต้น</div>
                                            <input class="input-section input-sec5" type="date" data-input="date" data-validate="l"  placeholder="เลือกวัน" name = "day1_start"  onchange="diff_date('day1_start','day1_final','o2_day')">
                                            <div class="margin-top8">วันสิ้นสุด</div>
                                            <input class="input-section input-sec5" type="date" data-input="date" data-validate="r"  placeholder="เลือกวัน" name = "day1_final"  onchange="diff_date('day1_start','day1_final','o2_day')">
                                            <div class="circle-red red-sec5" onclick="del_o2(this)"><img src="img/delete.svg"></div>
                                        </div>
                            </div>
                            <div class="plus-box" >
                                <div class="plus" onclick = "O2_add()">
                                    <div class="content_box">
                                        <img src="img/add.svg">
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>

                        <label class="container_radio">No
                            <input type="radio" name="" id = "O2_no"  value="No" onclick ="response2('uncheck')">
                            <span class="checkmark_radio"></span>
                        </label>
                        <!-- btn -->
                        <div class="save-btn" onclick="save_section5_3()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5.js"></script>
    <script>

    window.onload = function(){
        get_birth_date(function(){
            timeline_min_max("birth_date","now");
        });

            $("#info1").click(function(){ 
                show_info("- การใช้ FiO<sub>2</sub> > 0.21 เท่านั้น กรณีที่ใช้เครื่องช่วยหายใจแต่ใช้ <span class='font-weight:600'>FiO<sub>2</sub> 0.21 ให้ถือว่า “No O<sub>2</sub> supplement</span>"
                            +"<br/>- <span style='font-weight:600'>ไม่นับรวม O<sub>2</sub> ที่ให้ในช่วง resuscitation แรกเกิด</span>"
                            +"<br/>- กรณีที่กลับบ้านด้วย Home oxygen ให้ลงว่า O<sub>2</sub> supplement ถึงวัน discharge") 
            })

                
            document.getElementById("O2_box").hidden = true;
          
            set_contain_disable("O2"); 
            $.ajax({
            type: "POST",
            url: "https://techvernity.com/thainy/php/get_section5_3.php",
            data:{hospital:localStorage.hospital},
            success: function (data) {
              // console.log("data"+data);
               var value1 = JSON.parse(data);
               // console.log("value1"+data);
              // var num = parseInt(value1);
               //console.log(length_json(value1));
                init_table_ox(value1,length_json(value1));
                if(value1 == undefined || value1 == ""){
                   isdone('3',function(bool1){
                    if(bool1){
                        response2('uncheck');
                    }else{
                        response2('none');
                    }
                   });
                   
                   
                }else{
                    response2('check');
                }
                diff_date('day1_start','day1_final','o2_day');
 //               console.log(num);
               // response2('check');
               
               setTimeout(function(){
                if(localStorage.hospital != ""){
                         //   alert("a");
                            $("input").prop("disabled",true);
                            $("textArea").prop("disabled",true);
                            $("select").prop("disabled",true);
                            $(".save-btn").prop("hidden",true);
                            $(".plus").prop("hidden",true);
                            $(".circle-red").prop("hidden",true);
                     }
                 },250);
                }//scccess
                
            });
            
        }
     
        </script>
</body>

</html>