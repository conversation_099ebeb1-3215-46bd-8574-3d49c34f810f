<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">

        <link rel="stylesheet" type="text/css" href="css/web.css">
        <link rel="stylesheet" type="text/css" href="css/web_termandcondition.css">
        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>

</head>

<body>
        <!-- header -->
        <section class="header_web">
                <img class="img_logo" src="">
        </section>

        <!-- content -->

        <section class="content_login">
                <div class="term_data">
                        <div class="head-content">ข้อตกลงการใช้งาน</div>
                        <div class="term-content">Lorem Ipsum คือ เนื้อหาจำลองแบบเรียบๆ
                                ที่ใช้กันในธุรกิจงานพิมพ์หรืองานเรียงพิมพ์
                                มันได้กลายมาเป็นเนื้อหาจำลองมาตรฐานของธุรกิจดังกล่าวมาตั้งแต่ศตวรรษที่ 16
                                เมื่อเครื่องพิมพ์โนเนมเครื่องหนึ่งนำรางตัวพิมพ์มาสลับสับตำแหน่งตัวอักษรเพื่อทำหนังสือตัวอย่าง
                                Lorem Ipsum อยู่ยงคงกระพันมาไม่ใช่แค่เพียงห้าศตวรรษ
                                แต่อยู่มาจนถึงยุคที่พลิกโฉมเข้าสู่งานเรียงพิมพ์ด้วยวิธีทางอิเล็กทรอนิกส์
                                และยังคงสภาพเดิมไว้อย่างไม่มีการเปลี่ยนแปลง มันได้รับความนิยมมากขึ้นในยุค ค.ศ. 1960
                                เมื่อแผ่น Letraset วางจำหน่ายโดยมีข้อความบนนั้นเป็น Lorem Ipsum และล่าสุดกว่านั้น
                                คือเมื่อซอฟท์แวร์การทำสื่อสิ่งพิมพ์ (Desktop Publishing) อย่าง Aldus PageMaker
                                ได้รวมเอา Lorem Ipsum เวอร์ชั่นต่างๆ เข้าไว้ในซอฟท์แวร์ด้วย

                                ตรงกันข้ามกับความเชื่อที่นิยมกัน Lorem Ipsum
                                ไม่ได้เป็นเพียงแค่ชุดตัวอักษรที่สุ่มขึ้นมามั่วๆ
                                แต่หากมีที่มาจากวรรณกรรมละตินคลาสสิกชิ้นหนึ่งในยุค 45 ปีก่อนคริสตศักราช
                                ทำให้มันมีอายุถึงกว่า 2000 ปีเลยทีเดียว ริชาร์ด แมคคลินท็อค ศาสตราจารย์ชาวละติน
                                จากวิทยาลัยแฮมพ์เด็น-ซิดนีย์ ในรัฐเวอร์จิเนียร์ นำคำภาษาละตินคำว่า consectetur
                                ซึ่งหาคำแปลไม่ได้จาก Lorem Ipsum ตอนหนึ่งมาค้นเพิ่มเติม
                                โดยตรวจเทียบกับแหล่งอ้างอิงต่างๆ ในวรรณกรรมคลาสสิก และค้นพบแหล่งข้อมูลที่ไร้ข้อกังขาว่า
                                Lorem Ipsum นั้นมาจากตอนที่ 1.10.32 และ 1.10.33 ของเรื่อง “de Finibus Bonorum et
                                Malorum” (The Extremes of Good and Evil) ของ ซิเซโร ที่แต่งไว้เมื่อ 45
                                ปีก่อนคริสตศักราช หนังสือเล่มนี้เป็นเรื่องราวที่ว่าด้วยทฤษฎีแห่งจริยศาสตร์
                                ซึ่งเป็นที่นิยมมากในยุคเรเนสซองส์ บรรทัดแรกของ Lorem Ipsum “Lorem ipsum dolor sit
                                amet..” ก็มาจากบรรทัดหนึ่งในตอนที่ 1.10.32 นั่นเอง
                        </div>
                        <div class="bottom-box2">
                                <label class="container">ข้าพเจ้ายอมรับ “ข้อตกลงการใช้งาน” ทุกประการ
                                        และพร้อมเข้าใช้งาน ThaiNy
                                        <input type="checkbox" checked="checked">
                                        <span class="checkmark"></span>
                                </label>
                                <div class="btn-content next-ntn"><span class="set_center">ถัดไป <img src="img/nextwhite.svg"></span></div>
                        </div>
                </div>
        </section>



        <script src="js/main.js"></script>
</body>

</html>