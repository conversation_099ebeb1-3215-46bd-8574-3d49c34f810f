<?php
  include('header.php');
  session_start();
  $hospital = $_SESSION["hospital"];
  $query = mysql_query("select * from patient where (TNR = '".$_POST["data"]."' or fullname = '".$_POST["data"]."') and hospital = '".$hospital."'");
  if($_POST["mode"]=="check"){
      echo mysql_num_rows($query);
  }else{
      $ob = Array();
      for($i = 0;$i<mysql_num_rows($query);$i++){
          $temp = mysql_fetch_assoc($query);
          $ex = Array();
          array_push($ex,$temp["TNR"]);
          array_push($ex,$temp["fullname"]);
          array_push($ex,$temp["HN"]);
          array_push($ex,$temp["mother_fullname"]);
          array_push($ob,$ex);
      }
      echo json_encode($ob);
  }
  
?>  