<?php 
  include('header.php');

  $token = $_POST['token'];
  $password = md5($_POST['password']);
  
  if(!isset($token) || !isset($_POST['password'])){
    echo json_encode(array("status" => 0, "text" => "empty input"));
    exit();
  }

  $stored_token = hash('SHA256', $token);

  $query = mysql_query("SELECT email, expired FROM forget_password_token WHERE token = '$stored_token' ");
  
  if($query == 0 || mysql_num_rows($query) == 0){
    echo json_encode(array("status" => 0, "text" => "no token"));
    exit();
  }

  $array = mysql_fetch_assoc($query);
  $email = $array["email"];
  $expired = $array["expired"];

  if($expired - time() < 0){
    echo json_encode(array("status" => 0, "text" => "token expired"));
    exit();
  }

  mysql_query("START TRANSACTION");

  $query1 = mysql_query("UPDATE user SET password = '$password' WHERE email = '$email' ");
  $query2 = mysql_query("DELETE FROM forget_password_token WHERE email = '$email' ");

  if (!$query1 || !$query2) {
      mysql_query("ROLLBACK");
      echo json_encode(array("status" => 0, "text" => "token update failed". $email));
      exit();
  } 

  mysql_query("COMMIT");
  echo json_encode(array("status" => 1));
?>