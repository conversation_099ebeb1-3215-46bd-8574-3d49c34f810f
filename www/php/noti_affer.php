<?php
//f342b998-55f0-4995-8b7d-e43012ff0c31  thainykey
//26d93cee-bb6a-44e2-a3ab-93b921de11bd Testing key
$massage = $_POST["message"];
$user_ids = $_POST["ids"];
$S_date = $_POST["date"];

function sendMessage($massage,$user_ids,$S_date) {
   $ml_ids = explode(",",$user_ids);
   $content      = array(
        "en" => $massage
    );
    $hashes_array = array();
    array_push($hashes_array, array(
        "id" => "like-button",
        "text" => "Like",
        "icon" => "http://i.imgur.com/N8SN8ZS.png",
        "url" => "https://yoursite.com"
    ));
    array_push($hashes_array, array(
        "id" => "like-button-2",
        "text" => "Like2",
        "icon" => "http://i.imgur.com/N8SN8ZS.png",
        "url" => "https://yoursite.com"
    ));
    $fields = array(
        'app_id' => "f342b998-55f0-4995-8b7d-e43012ff0c31",
        'include_player_ids' => $ml_ids , //array("566afe32-16d0-4b2c-a511-62ce458f29f1","76ece62b-bcfe-468c-8a78-839aeaa8c5fa","8e0f21fa-9a5a-4ae7-a9a6-ca1f24294b86"),
        'data' => array(
         "foo" => "bar"
        ),
        'contents' => $content,
        'web_buttons' => $hashes_array
        ,'send_after' => $S_date
    );
    
    $fields = json_encode($fields);
   // print("\nJSON sent:\n");
  // print($fields);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://onesignal.com/api/v1/notifications");
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json; charset=utf-8',
        'Authorization: Basic YmQ2NzUzM2ItNTIzNy00ZjY1LWIwNmQtYzRjYWYxNGEyNDRl'
    ));
   
//NTVhNmM2NWYtN2RmMC00ZmMwLWE5ODYtMDdiYTEwNTliZTYy

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_POST, TRUE);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    
    $response = curl_exec($ch);
    print($response);
    curl_close($ch);
    
    return ;
   
}

$response = sendMessage($massage,$user_ids,$S_date);
$return["allresponses"] = $response;
$return = json_encode($return);

$data = json_decode($response, true);
//print_r($data);
$id = $data['id'];
//print_r($id);

//print("\n\nJSON received:\n");
//print($return);
//print("\n");
?>