<?php
   include("header.php");
   
   $username = $_POST["username"];
   $password = md5($_POST["password"]);

   $query = mysql_query("SELECT u.username, pin, hospital,status FROM user u LEFT JOIN pin p ON u.username = p.username WHERE u.username = '$username' AND password = '$password'");

   if($query == 0 || mysql_num_rows($query) == 0){
     echo json_encode(array("status" => 0));
     exit();
   }
   
   $temp = mysql_fetch_assoc($query);
   
   if($temp["status"] != 'approved' && $temp["status"] != 'ready'){
      echo json_encode(array("status" => -1));
      exit();
   }
   else{
     session_start();
     
     $_SESSION["username"] = $temp["username"];
     $_SESSION["hospital"] = $temp["hospital"];
     $query2 = mysql_query("select * from user_token where username = '".$username."'");
     $token = 0;
     if(mysql_num_rows($query2)==0){
          $token = 0;
     }else{
          $tempx = mysql_fetch_assoc($query2);
          $token = $tempx["token"];
     }
     echo json_encode(array("status" => 1,"user_status" => $temp["status"],"token" => $token, "pin" => $temp["pin"]));
   }
?>