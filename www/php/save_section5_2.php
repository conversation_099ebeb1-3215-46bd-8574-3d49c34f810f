<?php
  include('header.php');
  session_start();
  $TNR = $_SESSION["TNR"];
  $hospital = $_SESSION["hospital"];
  $id = "000";
  $status = "save";
  $user = $_SESSION["username"];
  $date = date("Y-m-d H:i:s");

  $data1 = $_POST["data1"];
  $data2 = $_POST["data2"];
  $data3 = $_POST["data3"];
  $num_v= $_POST["num_v"];
  $num_io= $_POST["num_io"];
  $num_l= $_POST["num_l"];

$cut_data1 = explode(";", $data1);
$cut_data2 = explode(";", $data2);
$cut_data3 = explode(";", $data3);

 mysql_query("delete from section5_2_LFNC where TNR = '".$TNR."' and hospital = '".$hospital."'");
 mysql_query("delete from section5_2_non_invasive where TNR = '".$TNR."' and hospital = '".$hospital."'");
 mysql_query("delete from section5_2_ventilator where TNR = '".$TNR."' and hospital = '".$hospital."'");

if($num_v  == "Yes"){
foreach($cut_data1 as $data){

$array = [];
$cut = explode(":", $data);
array_push($array,$id);
 array_push($array,$TNR);
array_push($array,$hospital);
//if($cut[0] == ""  || $cut[1] == "")continue;
array_push($array,$cut[0]);
array_push($array,$cut[1]);
array_push($array,$status);
array_push($array,$user);
array_push($array,$date);
 add_to_database("section5_2_ventilator",$array);
}
}
if($num_io == "Yes"){
foreach($cut_data2 as $data){

$array = [];
$cut = explode(":", $data);
array_push($array,$id);
 array_push($array,$TNR);
array_push($array,$hospital);
//if($cut[0] == ""  || $cut[1] == "")continue;
array_push($array,$cut[0]);
array_push($array,$cut[1]);
array_push($array,$status);
array_push($array,$user);
array_push($array,$date);
 add_to_database("section5_2_non_invasive",$array);
}
}
if($num_l == "Yes"){
foreach($cut_data3 as $data){
$array = [];
$cut = explode(":", $data);
array_push($array,$id);
 array_push($array,$TNR);
array_push($array,$hospital);
//if($cut[0] == ""  || $cut[1] == "")continue;
array_push($array,$cut[0]);
array_push($array,$cut[1]);
array_push($array,$status);
array_push($array,$user);
array_push($array,$date);
 add_to_database("section5_2_LFNC",$array);
}
} 

mysql_query("delete from section5_2 where TNR = '".$TNR."' and hospital = '".$hospital."'");
$array = [];
 array_push($array,$TNR);
array_push($array,$hospital);
array_push($array,"Yes");
array_push($array,$status);
array_push($array,$user);
array_push($array,$date);
 add_to_database("section5_2",$array);
?>