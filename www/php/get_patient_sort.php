<?php
 include('header.php');
 session_start();
 $hospital = $_SESSION["hospital"];
 $status = $_POST["status"];
 $type = $_POST["type"];
 $sort_by = $_POST["sort_by"];

 //$query = mysql_query("select * from patient where hospital = '".$hospital."' ");
 //$query = mysql_query("select * from patient where hospital = '".$hospital."' and status = '".$status."'");
 
 if(isset($type) && isset($sort_by)){
   $query = mysql_query("select fullname, TNR from patient where hospital = '$hospital' and status like '$status%' ORDER BY $type $sort_by");
   
   if($query == 0){
      echo 0;
      exit();
   }
   
   while($row = mysql_fetch_assoc($query)){
      $array[$row["TNR"]] = $row;
      //$array[$row["TNR"]]["status"] = "";
   }

   $query2 = mysql_query("SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = '$hospital' ORDER BY refer_id DESC) AS referx GROUP BY TNR");

   if($query2 == 0){
      echo 0;
      exit();
   }

   while($row = mysql_fetch_assoc($query2)){
      if(isset($array[$row["TNR"]])){
         $array[$row["TNR"]]["refer_status"] = $row["status"];
      }
   }

   echo json_encode(array_values($array));
 }
 

 /*
 $ob = Array();
 for($i = 0;$i<mysql_num_rows($query);$i++){
    $temp = mysql_fetch_assoc($query);
    $ob2 = {fullname:$temp["fullname"],TNR:$temp["TNR"]};
    array_push($ob,$ob2);
 }
 */
?>