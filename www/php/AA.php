<?php
<?php
 include('header.php');
 session_start();
 $TNR = $_SESSION["TNR"];
 $hospital = $_SESSION["hospital"];
 if(isset($_POST["hospital"])&&$_POST["hospital"]!=""){
   $hospital = $_POST["hospital"];
 }
 $table = $_POST["num"];
 if($table == "2_2"){
    $table = "2_abnormal_serology";
 }else if($table == "2_3"){
    $table = "2_complication_during_pregnancy";
 }else if($table == "2_4"){
    $table = "2_intrapartum_complication";
 }else if($table == "2_5"){
    $table = "2_meternal_medication";
 }
 echo json_encode(get_em("select * from section".$table." where TNR = '".$TNR."' and hospital = '".$hospital."'"));
 //$query = mysql_query("select * from section".$table." where TNR = '".$TNR."' and hospital = '".$hospital."'");
 //echo json_encode(mysql_fetch_assoc($query));
?>
/*curl --include \
     --request DELETE \
 --header "Authorization: Basic YmQ2NzUzM2ItNTIzNy00ZjY1LWIwNmQtYzRjYWYxNGEyNDRl" \
https://onesignal.com/api/v1/notifications/{notificationId}?app_id={f342b998-55f0-4995-8b7d-e43012ff0c31}

$ch = curl_init();
$httpHeader = array(
      'Authorization: Basic MY_REST_API_KEY'
    );
$url = "https://onesignal.com/api/v1/notifications/"************************************"?app_id="f342b998-55f0-4995-8b7d-e43012ff0c31;

$options = array (
  CURLOPT_URL => $url,
  CURLOPT_HTTPHEADER => $httpHeader,
  CURLOPT_RETURNTRANSFER => TRUE,
  CURLOPT_CUSTOMREQUEST => "DELETE",
  CURLOPT_SSL_VERIFYPEER => FALSE
);
curl_setopt_array($ch, $options);
$response = curl_exec($ch);
curl_close($ch);*/


?>