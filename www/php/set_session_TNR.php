<?php
    include('header.php');
    session_start();
   
    //$_SESSION["TNR"] = $_POST["TNR"];
    $query = mysql_query("select * from user_concurrence where TNR = '".$_POST["TNR"]."' and time > '".date("Y-m-d H:i:s")."'");
    if(mysql_num_rows($query)==0){
       mysql_query("delete from user_concurrence where TNR = '".$_POST["TNR"]."'");
       $newTime = date("Y-m-d H:i:s",strtotime(date("Y-m-d H:i:s")." +15 minutes"));
       mysql_query("insert into user_concurrence (username,TNR,time) values ('".$_SESSION["username"]."','".$_POST["TNR"]."','".$newTime."')");
       $_SESSION["TNR"] = $_POST["TNR"];
       $query = mysql_query("select * from register_criteria where TNR = '".$_POST["TNR"]."'");
       $temp = mysql_fetch_assoc($query);
       echo json_encode(array("status" => 1,"criteria"=>$temp["patient_symptoms"],"TNR"=>$_SESSION["TNR"]));
    }else{
       echo json_encode(array("status" => 0));
    }

?>