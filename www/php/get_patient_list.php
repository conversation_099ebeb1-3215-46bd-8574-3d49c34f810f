<?php
 include('header.php');
 session_start();
 $hospital = $_SESSION["hospital"];
 $status = $_POST["status"];
 //$query = mysql_query("select * from patient where hospital = '".$hospital."' ");
 //$query = mysql_query("select * from patient where hospital = '".$hospital."' and status = '".$status."'");
 mysql_query("delete from user_concurrence where username = '".$_SESSION["username"]."'");

 $query = mysql_query("select patient.fullname, patient.TNR from patient,register_criteria,section_progress where patient.hospital = '$hospital' and patient.status like '$status%' and patient.TNR = register_criteria.TNR and patient.TNR = section_progress.TNR and patient.hospital = section_progress.hospital");
 
 if($query == 0){
   echo 0;
   exit();
 }
 
while($row = mysql_fetch_assoc($query)){
   $array[$row["TNR"]] = $row;
   //$array[$row["TNR"]]["status"] = "";
}

$query2 = mysql_query("SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = '$hospital' ORDER BY refer_id DESC) AS referx GROUP BY TNR");

if($query2 == 0){
   echo 0;
   exit();
}

while($row = mysql_fetch_assoc($query2)){
   if(isset($array[$row["TNR"]])){
      $array[$row["TNR"]]["refer_status"] = $row["status"];
   }
}

 $result = array_values($array);
 if(!isset($result)){
    $result = [];
 }

 echo json_encode($result);
 
 /*
 $ob = Array();
 for($i = 0;$i<mysql_num_rows($query);$i++){
    $temp = mysql_fetch_assoc($query);
    $ob2 = {fullname:$temp["fullname"],TNR:$temp["TNR"]};
    array_push($ob,$ob2);
 }
 */
?>