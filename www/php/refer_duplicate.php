<?php
 include('header.php');
 session_start();
 $TNR = $_SESSION["TNR"];
 $hospital = $_POST["hospital"];

 $user_hospital = $_SESSION["hospital"];
 //$user_hospital = "playground";

 function duplicate($table,$TNR,$hospital,$user_hospital){
   $query = mysql_query("select * from ".$table." where TNR = '".$TNR."' and hospital = '".$hospital."'");
   mysql_query("delete from ".$table." where TNR = '".$TNR."' and hospital = '".$user_hospital."'");
   for($i=0;$i<mysql_num_rows($query);$i++){
       $temp = mysql_fetch_assoc($query);
       $temp["hospital"] = $user_hospital;
       ob_to_database($table,$temp); 
     } 
 }

  function duplicate_more($table,$TNR,$hospital,$user_hospital){
     $query = mysql_query("select * from ".$table." where TNR = '".$TNR."' and hospital = '".$hospital."'");
     mysql_query("delete from ".$table." where TNR = '".$TNR."' and hospital = '".$user_hospital."'");
     //echo json_encode(mysql_num_rows($query));
     for($i=0;$i<mysql_num_rows($query);$i++){
       $temp = mysql_fetch_assoc($query);
       $temp["hospital"] = $user_hospital;
       echo json_encode(ob_to_database($table,$temp,1)); 
     }
 }

 ////////////////////////////
 duplicate("patient",$TNR,$hospital,$user_hospital);

 duplicate("section1",$TNR,$hospital,$user_hospital);

 duplicate("section2",$TNR,$hospital,$user_hospital);
 duplicate("section2_abnormal_serology",$TNR,$hospital,$user_hospital);
 duplicate("section2_complication_during_pregnancy",$TNR,$hospital,$user_hospital);
 duplicate("section2_GBS",$TNR,$hospital,$user_hospital);
 duplicate("section2_intrapartum_complication",$TNR,$hospital,$user_hospital);
 duplicate("section2_meternal_medication",$TNR,$hospital,$user_hospital);
 duplicate_more("section2_other",$TNR,$hospital,$user_hospital);

 duplicate("section3",$TNR,$hospital,$user_hospital);

 duplicate("section4",$TNR,$hospital,$user_hospital);

 duplicate("section5_1",$TNR,$hospital,$user_hospital);

 duplicate("section5_2",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_2_LFNC",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_2_non_invasive",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_2_ventilator",$TNR,$hospital,$user_hospital);

 duplicate_more("section5_3",$TNR,$hospital,$user_hospital); 
 
 duplicate("section5_4",$TNR,$hospital,$user_hospital);

 duplicate("section5_5",$TNR,$hospital,$user_hospital);

 duplicate_more("section5_6",$TNR,$hospital,$user_hospital);

 duplicate("section5_7",$TNR,$hospital,$user_hospital);

 duplicate("section5_8",$TNR,$hospital,$user_hospital);

 duplicate("section5_9",$TNR,$hospital,$user_hospital);

 duplicate("section5_10",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_10_other_central_line",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_10_PICC_line",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_10_UAC",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_10_UVC",$TNR,$hospital,$user_hospital);

 duplicate("section5_11",$TNR,$hospital,$user_hospital);
 duplicate("section5_11_detail",$TNR,$hospital,$user_hospital);

 duplicate("section5_12",$TNR,$hospital,$user_hospital);
 duplicate_more("section5_12_seizure_other",$TNR,$hospital,$user_hospital);

 duplicate("section6",$TNR,$hospital,$user_hospital);

 duplicate("section_progress",$TNR,$hospital,$user_hospital);
 duplicate("register_progress",$TNR,$hospital,$user_hospital);
 ////////////////////////////

 mysql_query("update refer set status = 'accept' , response_date = '".date('Y-m-d')."' , response_username = '".$_SESSION["username"]."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
 mysql_query("update patient set status = 'inactive_refer' where TNR = '".$TNR."' and hospital = '".$hospital."'");
 //mysql_query("update patient set status = 'inactive_refer' where TNR = '".$TNR."' and hospital = '".$hospital."'");
 //echo json_encode($hospital);
?> 

