<?php
 include('header.php');
 session_start();
 $username = $_SESSION["username"];
 $TNR = $_SESSION["TNR"];


 $query = mysql_query("select * from user_concurrence where username = '".$username."' and TNR = '".$TNR."' and time > '".date("Y-m-d H:i:s")."'");
 if(mysql_num_rows($query)==0){
    echo 0;
 }else{
    if($_POST["update"]=="update"){
     mysql_query("delete from user_concurrence where TNR = '".$TNR."'");
     $newTime = date("Y-m-d H:i:s",strtotime(date("Y-m-d H:i:s")." +15 minutes"));
     mysql_query("insert into user_concurrence (username,TNR,time) values ('".$_SESSION["username"]."','".$TNR."','".$newTime."')");
    }
    echo 1;
 }
?>