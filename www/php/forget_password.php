<?php 
  include('header.php');
  include("../../asset/backend/email/mail_sender.php");

  $email = $_POST["email"];
  
  if(!isset($email) || empty($email)) {
    echo json_encode(array("status" => 0, "text" => "empty email"));
    exit();
  }
      
  $query = mysql_query("SELECT password FROM user WHERE email = '$email'");
  if($query == 0 || mysql_num_rows($query) == 0){
    echo json_encode(array("status" => 0, "text" => "no email"));
    exit();
  }

  $array = mysql_fetch_assoc($query);
  $token = hash_hmac('SHA256', rand(), $array["password"]);
  $stored_token = hash('SHA256', ($token));
  $expired = time() + (24*60*60*1000);

  $query = mysql_query("INSERT INTO forget_password_token (email, token, expired) 
                        VALUES ('$email', '$stored_token', '$expired') 
                        ON DUPLICATE KEY UPDATE token = '$stored_token', expired = '$expired' ");      
  
  if($query == 0){
    echo json_encode(array("status" => 0, "text" => "insert error"));
    exit();
  }


  $url = "https://techvernity.com/thainy/php/forget_password_page.php?token=". $token;
  send_email("Forget Password", 'Your link is <br/>'.$url, $email);
  echo json_encode(array("status" => 1));
?>