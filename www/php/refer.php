<?php
  include('header.php');
  session_start();
  $TNR = $_SESSION["TNR"];
  $hospital = $_SESSION["hospital"];
  $referer_username = $_SESSION["username"];;
  
  $destination_hospital = $_POST["hospital"];
  $refer_date = $_POST["time"];
  $status = $_POST["status"];
  $ob = Array(
     "TNR" => $TNR,
     "hospital" => $hospital,
     "destination_hospital" => $destination_hospital,
     "refer_date" => $refer_date,
     "status" => $status,
     "referer_usernmae" => $referer_username,
  );
  if($status == "non-mem refer"){
     mysql_query("update patient set status = 'inactive_refer_non_mem' where TNR = '".$TNR."' and hospital = '".$hospital."'");
  }
  echo json_encode(ob_to_database("refer",$ob));
?>