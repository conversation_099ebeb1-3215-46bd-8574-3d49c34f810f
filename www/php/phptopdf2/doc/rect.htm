<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>Rect</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>Rect</h1>

<code>Rect(<b>float</b> x, <b>float</b> y, <b>float</b> w, <b>float</b> h [, <b>string</b> style])</code>

<h2>Description</h2>

Outputs a rectangle. It can be drawn (border only), filled (with no border) or both.

<h2>Parameters</h2>

<dl class="param">

<dt><code>x</code></dt>

<dd>

Abscissa of upper-left corner.

</dd>

<dt><code>y</code></dt>

<dd>

Ordinate of upper-left corner.

</dd>

<dt><code>w</code></dt>

<dd>

Width.

</dd>

<dt><code>h</code></dt>

<dd>

Height.

</dd>

<dt><code>style</code></dt>

<dd>

Style of rendering. Possible values are:

<ul>

<li><code>D</code> or empty string: draw. This is the default value.</li>

<li><code>F</code>: fill</li>

<li><code>DF</code> or <code>FD</code>: draw and fill</li>

</ul>

</dd>

</dl>

<h2>See also</h2>

<a href="setlinewidth.htm">SetLineWidth()</a>,

<a href="setdrawcolor.htm">SetDrawColor()</a>,

<a href="setfillcolor.htm">SetFillColor()</a>.

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

