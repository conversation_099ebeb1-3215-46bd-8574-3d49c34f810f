<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>SetLineWidth</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>SetLineWidth</h1>

<code>SetLineWidth(<b>float</b> width)</code>

<h2>Description</h2>

Defines the line width. By default, the value equals 0.2 mm. The method can be called before

the first page is created and the value is retained from page to page.

<h2>Parameters</h2>

<dl class="param">

<dt><code>width</code></dt>

<dd>

The width.

</dd>

</dl>

<h2>See also</h2>

<a href="line.htm">Line()</a>,

<a href="rect.htm">Rect()</a>,

<a href="cell.htm">Cell()</a>,

<a href="multicell.htm">MultiCell()</a>.

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

