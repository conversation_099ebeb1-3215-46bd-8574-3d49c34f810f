<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>SetDrawColor</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>SetDrawColor</h1>

<code>SetDrawColor(<b>int</b> r [, <b>int</b> g, <b>int</b> b])</code>

<h2>Description</h2>

Defines the color used for all drawing operations (lines, rectangles and cell borders). It

can be expressed in RGB components or gray scale. The method can be called before the first

page is created and the value is retained from page to page.

<h2>Parameters</h2>

<dl class="param">

<dt><code>r</code></dt>

<dd>

If <code>g</code> et <code>b</code> are given, red component; if not, indicates the gray level.

Value between 0 and 255.

</dd>

<dt><code>g</code></dt>

<dd>

Green component (between 0 and 255).

</dd>

<dt><code>b</code></dt>

<dd>

Blue component (between 0 and 255).

</dd>

</dl>

<h2>See also</h2>

<a href="setfillcolor.htm">SetFillColor()</a>,

<a href="settextcolor.htm">SetTextColor()</a>,

<a href="line.htm">Line()</a>,

<a href="rect.htm">Rect()</a>,

<a href="cell.htm">Cell()</a>,

<a href="multicell.htm">MultiCell()</a>.

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

