<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>SetCompression</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>SetCompression</h1>

<code>SetCompression(<b>boolean</b> compress)</code>

<h2>Description</h2>

Activates or deactivates page compression. When activated, the internal representation of

each page is compressed, which leads to a compression ratio of about 2 for the resulting

document.

<br>

Compression is on by default.

<br>

<br>

<strong>Note:</strong> the Zlib extension is required for this feature. If not present, compression

will be turned off.

<h2>Parameters</h2>

<dl class="param">

<dt><code>compress</code></dt>

<dd>

Boolean indicating if compression must be enabled.

</dd>

</dl>

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

