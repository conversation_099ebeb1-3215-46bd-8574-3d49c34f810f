<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>GetStringWidth</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>GetStringWidth</h1>

<code><b>float</b> GetStringWidth(<b>string</b> s)</code>

<h2>Description</h2>

Returns the length of a string in user unit. A font must be selected.

<h2>Parameters</h2>

<dl class="param">

<dt><code>s</code></dt>

<dd>

The string whose length is to be computed.

</dd>

</dl>

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

