<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>Line</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>Line</h1>

<code>Line(<b>float</b> x1, <b>float</b> y1, <b>float</b> x2, <b>float</b> y2)</code>

<h2>Description</h2>

Draws a line between two points.

<h2>Parameters</h2>

<dl class="param">

<dt><code>x1</code></dt>

<dd>

Abscissa of first point.

</dd>

<dt><code>y1</code></dt>

<dd>

Ordinate of first point.

</dd>

<dt><code>x2</code></dt>

<dd>

Abscissa of second point.

</dd>

<dt><code>y2</code></dt>

<dd>

Ordinate of second point.

</dd>

</dl>

<h2>See also</h2>

<a href="setlinewidth.htm">SetLineWidth()</a>,

<a href="setdrawcolor.htm">SetDrawColor()</a>.

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

