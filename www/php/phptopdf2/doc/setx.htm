<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

<title>SetX</title>

<link type="text/css" rel="stylesheet" href="../fpdf.css">

</head>

<body>

<h1>SetX</h1>

<code>SetX(<b>float</b> x)</code>

<h2>Description</h2>

Defines the abscissa of the current position. If the passed value is negative, it is relative

to the right of the page.

<h2>Parameters</h2>

<dl class="param">

<dt><code>x</code></dt>

<dd>

The value of the abscissa.

</dd>

</dl>

<h2>See also</h2>

<a href="getx.htm">GetX()</a>,

<a href="gety.htm">GetY()</a>,

<a href="sety.htm">SetY()</a>,

<a href="setxy.htm">SetXY()</a>.

<hr style="margin-top:1.5em">

<div style="text-align:center"><a href="index.htm">Index</a></div>

</body>

</html>

