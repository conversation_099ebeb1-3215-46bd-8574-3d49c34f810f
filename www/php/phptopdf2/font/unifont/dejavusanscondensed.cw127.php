<?php
$rangeid=122;
$prevcid=126;
$prevwidth=754;
$interval=false;
$range=array (
  32 => 
  array (
    0 => 286,
    1 => 360,
    2 => 414,
    3 => 754,
    4 => 572,
    5 => 855,
    6 => 702,
    7 => 247,
  ),
  40 => 
  array (
    0 => 351,
    1 => 351,
    'interval' => true,
  ),
  42 => 
  array (
    0 => 450,
    1 => 754,
    2 => 286,
    3 => 325,
    4 => 286,
    5 => 303,
  ),
  48 => 
  array (
    0 => 572,
    1 => 572,
    'interval' => true,
    2 => 572,
    3 => 572,
    4 => 572,
    5 => 572,
    6 => 572,
    7 => 572,
    8 => 572,
    9 => 572,
  ),
  58 => 
  array (
    0 => 303,
    1 => 303,
    'interval' => true,
  ),
  60 => 
  array (
    0 => 754,
    1 => 754,
    'interval' => true,
    2 => 754,
  ),
  63 => 
  array (
    0 => 478,
    1 => 900,
    2 => 615,
    3 => 617,
    4 => 628,
    5 => 693,
    6 => 568,
    7 => 518,
    8 => 697,
    9 => 677,
  ),
  73 => 
  array (
    0 => 265,
    1 => 265,
    'interval' => true,
  ),
  75 => 
  array (
    0 => 590,
    1 => 501,
    2 => 776,
    3 => 673,
    4 => 708,
    5 => 542,
    6 => 708,
    7 => 625,
    8 => 571,
    9 => 549,
    10 => 659,
    11 => 615,
    12 => 890,
    13 => 616,
    14 => 549,
    15 => 616,
    16 => 351,
    17 => 303,
    18 => 351,
    19 => 754,
  ),
  95 => 
  array (
    0 => 450,
    1 => 450,
    'interval' => true,
  ),
  97 => 
  array (
    0 => 551,
    1 => 571,
    2 => 495,
    3 => 571,
    4 => 554,
    5 => 316,
    6 => 571,
    7 => 570,
  ),
  105 => 
  array (
    0 => 250,
    1 => 250,
    'interval' => true,
  ),
  107 => 
  array (
    0 => 521,
    1 => 250,
    2 => 876,
    3 => 570,
    4 => 550,
  ),
  112 => 
  array (
    0 => 571,
    1 => 571,
    'interval' => true,
  ),
  114 => 
  array (
    0 => 370,
    1 => 469,
    2 => 353,
    3 => 570,
    4 => 532,
    5 => 736,
  ),
  120 => 
  array (
    0 => 532,
    1 => 532,
    'interval' => true,
  ),
  122 => 
  array (
    0 => 472,
    1 => 572,
    2 => 303,
    3 => 572,
    4 => 754,
  ),
);
?>