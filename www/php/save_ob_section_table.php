<?php
  include('header.php');
  session_start();
  $TNR = $_SESSION["TNR"];
  $hospital = $_SESSION["hospital"];
  
  $status = "save";
  $user = $_SESSION["username"];
  $date = date("Y-m-d H:i:s");

  $temp = $_POST["main"];

  $temp["TNR"] = $TNR;
  $temp["hospital"] = $hospital;
  $temp["status"] = $status;
  $temp["last_modified_username"] = $user;
  $temp["last_modified_date"] = $date;

  $section = $_POST["num"];

  mysql_query("delete from section".$section." where TNR = '".$TNR."' and hospital = '".$hospital."'");
  ob_to_database("section".$section,$temp);

  $table = $_POST["table"];

  for($i = 0 ; $i < count($table) ; $i++ ){
    $query = mysql_query("delete from section".$section."_".$table[$i]." where TNR = '".$TNR."' and hospital = '".$hospital."'");

    $table_data = $_POST["table".($i+1)];

    for($j = 0 ; $j < count($table_data) ; $j++){
        $table2 = only_one_ex($table_data[$j]);
        
        array_unshift($table2, $hospital);
        array_unshift($table2, $TNR);
        array_push($table2, $status);
        array_push($table2, $user);
        array_push($table2, $date);

        add_to_database("section".$section."_".$table[$i], $table2, 1);
        //echo json_encode(add_to_database("section".$section."_".$table[$i], $table2, 1));
    }
  }


  echo json_encode(array("status" => 1));
?>