<?php
 include('header.php');
 session_start();
 $hospital = $_SESSION["hospital"];
 
 $filter = "";
 $condition = "";
 if($_POST["all"]=="all"){
  
 }else{
     if($_POST["date_start"]!=""){
         $filter .= " and patient.created_date >= '".$_POST["date_start"]."' "; 
     }
  
     if($_POST["date_stop"]!=""){
         $filter .= " and patient.created_date <= '".$_POST["date_stop"]."' "; 
     }
 }
 if($_POST["GA"]||$_POST["BW"]||$_POST["HIE"]||$_POST["major"]){
   $filter .= " and ( ";
 }
 $or = false;
 if($_POST["GA"]){
     $filter .= " register_criteria.patient_symptoms like '%GA%' ";
     $or = true;
 }
  if($_POST["BW"]){
     if($or){
       $filter .= " or ";
     }
     $filter .= " register_criteria.patient_symptoms like '%BW%' ";
      $or = true;
 }
  if($_POST["HIE"]){
     if($or){
       $filter .= " or ";
     }
     $filter .= " register_criteria.patient_symptoms like '%HIE%' ";
     $or = true;
 }
  if($_POST["major"]){
     if($or){
       $filter .= " or ";
     }
     $filter .= " register_criteria.patient_symptoms like '%Major%' ";
     $or = true;
 }

 if($_POST["GA"]||$_POST["BW"]||$_POST["HIE"]||$_POST["major"]){
    $filter .= " ) ";
 }

 if($_POST["complete"]=="true"||$_POST["incomplete"]=="true"){
     $filter .= " and ";
      if($_POST["complete"]=="true"&&$_POST["incomplete"]=="true"){
         $filter .= " ( ";
     }

     if($_POST["complete"]=="true"){
        $filter .= " (section_progress.section1 != 'not done' and section_progress.section2 != 'not done' and section_progress.section3 != 'not done' and section_progress.section4 != 'not done' and section_progress.section5_1 != 'not done' and section_progress.section5_2 != 'not done' and section_progress.section5_3 != 'not done' and section_progress.section5_4 != 'not done' and section_progress.section5_5 != 'not done' and section_progress.section5_6 != 'not done' and section_progress.section5_7 != 'not done' and section_progress.section5_8 != 'not done' and section_progress.section5_9 != 'not done' and section_progress.section5_10 != 'not done' and section_progress.section5_11 != 'not done' and section_progress.section5_12 != 'not done' )";
     
     }
     if($_POST["complete"]=="true"&&$_POST["incomplete"]=="true"){
         $filter .= " or ";
     }
     if($_POST["incomplete"]=="true"){
        $filter .= " (section_progress.section1 != 'done' or section_progress.section2 != 'done' or section_progress.section3 != 'done' or section_progress.section4 != 'done' or section_progress.section5_1 != 'done' or section_progress.section5_2 != 'done' or section_progress.section5_3 != 'done' or section_progress.section5_4 != 'done' or section_progress.section5_5 != 'done' or section_progress.section5_6 != 'done' or section_progress.section5_7 != 'done' or section_progress.section5_8 != 'done' or section_progress.section5_9 != 'done' or section_progress.section5_10 != 'done' or section_progress.section5_11 != 'done' or section_progress.section5_12 != 'done')";
     }

     if($_POST["complete"]=="true"&&$_POST["incomplete"]=="true"){
         $filter .= " ) ";
     }
 }

 if($_POST["status"]=="active"){
   $filter .= " and patient.status = 'active'";
 }else{
   $filter .= " and patient.status like '%inactive%'";
 }

 $filter .= " order by patient.".$_POST["sort_by"]." ".$_POST["sort_order"];

$query = mysql_query("select * from patient,register_criteria,section_progress where patient.hospital = '".$hospital."' and patient.TNR = register_criteria.TNR and section_progress.TNR = patient.TNR and section_progress.hospital = patient.hospital ".$filter);
 
 if($query == 0){
   echo 0;
   exit();
 }
 
while($row = mysql_fetch_assoc($query)){
   $array[$row["TNR"]] = $row;
   //$array[$row["TNR"]]["status"] = "";
}

$query2 = mysql_query("SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = '$hospital' ORDER BY refer_id DESC) AS referx GROUP BY TNR");

if($query2 == 0){
   echo 0;
   exit();
}

while($row = mysql_fetch_assoc($query2)){
   if(isset($array[$row["TNR"]])){
      $array[$row["TNR"]]["refer_status"] = $row["status"];
   }
}

   $result = array_values($array);
   if(!isset($result)){
      $result = [];
   }

   echo json_encode($result);
?>