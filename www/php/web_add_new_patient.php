<?php
   include('header.php');
   session_start();
   $hospital = $_SESSION["hospital"];
   $temp = json_decode($_POST["data"]);
   $temp = only_one_ex($temp);
   $TNR = generateTNR($temp[3],$hospital);
   array_unshift($temp,$hospital);
   array_unshift($temp,$TNR);
   array_push($temp,"active");
   array_push($temp,date("Y-m-d H:i:s"));
   //echo json_encode($temp);
   $query = mysql_query("select * from patient where fullname = '".$temp[2]."' and mother_fullname = '".$temp[8]."' and DOB = '".$temp[5]."'");
   if(mysql_num_rows($query)==0){
       add_to_database("patient",$temp);
       echo 1;
   }else{
       echo 0;
    }
?>