<?php
  include('header.php');
  session_start();
  $TNR = $_SESSION["TNR"];
  $hospital = $_SESSION["hospital"];
  //$TNR = "20180000";
  //$hospital = "vernity";

  $status = "save";
  $user = $_SESSION["username"];
  //$user = "1";
  $date = date("Y-m-d H:i:s");

  $temp = $_POST["data"];
  $temp["TNR"] = $TNR;
  $temp["hospital"] = $hospital;
  $temp["status"] = $status;
  $temp["last_modified_username"] = $user;
  $temp["last_modified_date"] = $date;

  $section = $_POST["num"];


  mysql_query("delete from section".$section." where TNR = '".$TNR."' and hospital = '".$hospital."'");
  echo json_encode(ob_to_database("section".$section,$temp));

  if($_POST["num"]=="1"&&$temp["admission_date"]!=""){
      $addmission_date = $temp["admission_date"];
      //echo "1";
      $query = mysql_query("select * from section6 where TNR = '".$TNR."' and hospital = '".$hospital."'");
      if(mysql_num_rows($query)!=0){
         $temp = mysql_fetch_assoc($query);
         if($temp["discharge_date"]!=""){
            mysql_query("update section6 set home_hospital_day = '".diffDate($addmission_date,$temp["discharge_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
         }

         if($temp["transfer_date"]!=""){
            mysql_query("update section6 set transfer_hospital_day = '".diffDate($addmission_date,$temp["transfer_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
         }
 
          if($temp["dead_date"]!=""){
            mysql_query("update section6 set dead_hospital_day = '".diffDate($addmission_date,$temp["dead_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
         }
      }
  }
?>