<?php
   include("header.php");
   require('phptopdf2/fpdf.php');
   session_start();
   $created_date = date("Y-m-d H:i:s");
   $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
        $section1 = $_GET["s1"];
        $section2 = $_GET["s2"];
        $section3 = $_GET["s3"];
        $section4 = $_GET["s4"];
        $section5 = $_GET["s5"];
        $section6 = $_GET["s6"];
class myPDF extends FPDF{
     function header(){
         
     }
     function footer(){
  
     }
     function headerTable(){
       $this->SetFont('Times','B','12');
       $this->Cell(250,10,'ThaiNy Registry (TN) - Data Record Form',0,0,'C');
       $this->Ln();       
       $this->Cell(250,10,'TN Number:'.$_GET["TNR"],0,0,'C');
       $this->Ln();
     }
     function section1(){
        $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
 $this->SetFont('Times','B','12');
       $query = mysql_query("SELECT * FROM `section1` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec1 = mysql_fetch_assoc($query);
        $this->Cell(250,10,'Section1',1,0,'C');
        $this->Ln();
       $this->Cell(100,10,'Birth status:'."\t".$sec1["admission_status"],0,0,'L');
       $this->Ln();
$this->Cell(10,10,"",0,0,'L');
       $this->Cell(100,10,"-Intrauterine_transter : ".$sec1["intrauterine_transter"],0,0,'L');
        $this->Ln();
$this->Cell(10,10,"",0,0,'L');
       $this->Cell(100,10,"-Transfer_member: ".$sec1["transfer_member"],0,0,'L');
       $this->Ln();
$this->Cell(10,10,"",0,0,'L');
       $this->Cell(100,10,"-Hospital_name: ".$sec1["hospital_name"],0,0,'L');
     $this->Ln();
$this->Cell(100,10,"Date of Admission in our center: ".$sec1["admission_date"],0,0,'L');
     $this->Ln();
$this->Cell(100,10,"Date of Discharge in our center:".$sec1["discharge_date"],0,0,'L');
  $this->Ln();
$this->Cell(100,10,"Hospital stay in our center:".$sec1["stay_day"],0,0,'L');
  $this->Ln();
$this->Cell(100,10,"Status at discharge from our center: ".$sec1["discharge_type"],0,0,'L');
      $this->Ln();
     }

   function section2(){
        $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
 $this->SetFont('Times','B','12');
       $query = mysql_query("SELECT * FROM `section2` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec2 = mysql_fetch_assoc($query);
       $query = mysql_query("SELECT * FROM `section2_abnormal_serology` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec2_ad = mysql_fetch_assoc($query);
       $query = mysql_query("SELECT * FROM `section2_complication_during_pregnancy` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec2_com = mysql_fetch_assoc($query);
       $query = mysql_query("SELECT * FROM `section2_intrapartum_complication` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec2_intra = mysql_fetch_assoc($query);
         $query = mysql_query("SELECT * FROM `section2_GBS` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec2_gbs = mysql_fetch_assoc($query);
       $query = mysql_query("SELECT * FROM `section2_meternal_medication` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec2_met = mysql_fetch_assoc($query);
 
        $this->Cell(250,10,'Section2',1,0,'C');
          $this->Ln();
         if($sec2["age_NA"] == "NA"){
           $this->Cell(100,10,"Maternal age: N/A",0,0,'L'); 
         }else{
           $this->Cell(100,10,"Maternal age: ".$sec2["age"],0,0,'L'); 
         }
$this->Ln();
        if($sec2["G_NA"] == "NA"){
         $this->Cell(30,10,"G: N/A",0,0,'L');
        }else{
          $this->Cell(30,10,"G:".$sec2["G"],0,0,'L');
        }
        if($sec2["P_NA"] == "NA"){
         $this->Cell(30,10,"P: N/A",0,0,'L');
        }else{
          $this->Cell(30,10,"P:".$sec2["P"],0,0,'L');
        }
   if($sec2["A_NA"] == "NA"){
         $this->Cell(30,10,"A: N/A",0,0,'L');
        }else{
          $this->Cell(30,10,"A:".$sec2["A"],0,0,'L');
        }
 $this->Ln();

 if($sec2_ad["NA"] == "N/A"){
 $this->Cell(30,10,"Abnormal serology: N/A",0,0,'L');
}else{
$text = "";
if($sec2_ad["positive_anti_HIV"] != ""){
 $text .= $sec2_ad["positive_anti_HIV"].",";
}
if($sec2_ad["positive_HBsAg"] != ""){
 $text .= $sec2_ad["positive_HBsAg"].",";
}
if($sec2_ad["positive_VDRL"] != ""){
 $text .= $sec2_ad["positive_VDRL"].",";
}
if($sec2_ad["normal_all"] != ""){
 $text .= $sec2_ad["normal_all"].",";
}
if($sec2_ad["other"] != ""){
 $text .= $sec2_ad["other"].",";
 
}
$this->Cell(30,10,"Abnormal serology:".substr($text, 0, -1),0,0,'L');

}
$this->Ln();
  
 if($sec2_com ["NA"] == "N/A"){
 $this->Cell(30,10,"Complication during pregnancy: N/A",0,0,'L');
}else{
$text = "";
if($sec2_com ["no_ANC"] != ""){
 $text .= $sec2_com ["no_ANC"].",";
}
if($sec2_com ["over_DM_or_GDM"] != ""){
 $text .= $sec2_com ["over_DM_or_GDM"].",";
}
if($sec2_com ["chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia"] != ""){
 $text .= $sec2_com ["chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia"].",";
}
if($sec2_com ["meternal_thyroid_disease"] != ""){
 $text .= $sec2_com ["	meternal_thyroid_disease"].",";
}
if($sec2_com ["multiple_gestation"] != ""){
 $text .= $sec2_com ["multiple_gestation"].",";
}
if($sec2_com ["number"] != ""){
 $text .= $sec2_com ["number"].",";
}
if($sec2_com ["meternal_UTI"] != ""){
 $text .= $sec2_com ["meternal_UTI"].",";
}
if($sec2_com ["meternal_drug_abuse"] != ""){
 $text .= $sec2_com ["meternal_drug_abuse"].",";
}
if($sec2_com ["other"] != ""){
 $text .= $sec2_com ["other"].",";
}
$this->Cell(30,10,"Complication during pregnancy:".substr($text, 0, -1),0,0,'L');
}//
$this->Ln();
if($sec2_intra["NA"] == "Unknown"){
 $this->Cell(30,10,"Intrapartum complication: N/A",0,0,'L');
}else{
$text = "";
if($sec2_intra["abnormal_vaginal_bleeding"] != ""){
 $text .= $sec2_intra["abnormal_vaginal_bleeding"].",";
}
if($sec2_intra["MSAF"] != ""){
 $text .= $sec2_intra["MSAF"].",";
}
if($sec2_intra["Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement"] != ""){
 $text .= $sec2_intra["Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement"].",";
}
if($sec2_intra["meternal_fever_or_Chrioaminonitis"] != ""){
 $text .= $sec2_intra["meternal_fever_or_Chrioaminonitis"].",";
}
if($sec2_intra["prolonged_rupture_of_membrane_more_than_18_hr"] != ""){
 $text .= $sec2_intra["prolonged_rupture_of_membrane_more_than_18_hr"].",";
}
if($sec2_intra["prolapsed_cord"] != ""){
 $text .= $sec2_intra["prolapsed_cord"].",";
}
if($sec2_intra["IUGR"] != ""){
 $text .= $sec2_intra["IUGR"].",";
}
if($sec2_intra["oilgohydramios"] != ""){
 $text .= $sec2_intra["oilgohydramios"].",";
}
if($sec2_intra["polyhydramios"] != ""){
 $text .= $sec2_intra["polyhydramios"].",";
}
if($sec2_intra["other"] != ""){
 $text .= $sec2_intra["other"].",";
}
$this->Cell(30,10,"Intrapartum complication:".substr($text, 0, -1),0,0,'L');
}//else
$this->Ln();
$text = "";
if($sec2_gbs["not_done"] != ""){
 $text .= $sec2_gbs["not_done"].",";
}
if($sec2_gbs["positive"] != ""){
 $text .= $sec2_gbs["positive"].",";
}
if($sec2_gbs["negative"] != ""){
 $text .= $sec2_gbs["negative"].",";
}
if($sec2_gbs["other"] != ""){
 $text .= $sec2_gbs["other"].",";
}
if($text == ""){
 $text = "N/A";
}
$this->Cell(30,10,"GBS screening:".$text,0,0,'L');
$this->Ln();
if($sec2_met["NA"] == "Unknown"){
 $this->Cell(30,10,"Maternal medication (prior to delivery): : N/A",0,0,'L');
}else{
$text = "";
if($sec2_met["antibiotics"] != ""){
 $text .= $sec2_met["antibiotics"].",";
}
if($sec2_met["time"] != ""){
 $text .= $sec2_met["time"].",";
}
if($sec2_met["dexamamthasone_or_prenatal_steroid"] != ""){
 $text .= $sec2_met["dexamamthasone_or_prenatal_steroid"].",";
}
if($sec2_met["course"] != ""){
 $text .= $sec2_met["course"].",";
}
if($sec2_met["MgSo4"] != ""){
 $text .= $sec2_met["MgSo4"].",";
}
if($sec2_met["other"] != ""){
 $text .= $sec2_met["other"].",";
}
$this->Cell(30,10,"Maternal medication (prior to delivery):".substr($text, 0, -1),0,0,'L');
}//else
 $this->Ln();
     }//section2
 function section3(){
   $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
 $this->SetFont('Times','B','12');
       $query = mysql_query("SELECT * FROM `section3` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
       $sec3 = mysql_fetch_assoc($query);
      $this->Cell(250,10,'Section3',1,0,'C');
 $this->Ln();
      $this->Cell(30,10,"Mode of delivery :".$sec3["delivery_mode"],0,0,'L'); 
$this->Ln();
$this->Cell(30,10,"Apgar score :",0,0,'L'); 
$this->Cell(30,10,"1 Min :".$sec3["1_min_score"],0,0,'L'); 
$this->Cell(30,10,"5 Min :".$sec3["5_min_score"],0,0,'L'); 
$this->Cell(30,10,"10 Min :".$sec3["10_min_score"],0,0,'L'); 
$this->Ln();
if($sec3["resuscitation "] == "No"){
$this->Cell(30,10,"Resuscitation : No",0,0,'L'); 
}else{
 $text = "";
if($sec3["CPAP"] != ""){
 $text .= $sec3["CPAP"].",";
}
if($sec3["PPV"] != ""){
 $text .= $sec3["PPV"].",";
}
if($sec3["intubation"] != ""){
 $text .= $sec3["intubation"].",";
}
if($sec3["chest_compression"] != ""){
 $text .= $sec3["chest_compression"].",";
}
if($sec3["epinephrine"] != ""){
 $text .= $sec3["epinephrine"].",";
}
$this->Cell(30,10,"Resuscitation :".substr($text, 0, -1),0,0,'L');
}//else
   $this->Ln();
if($sec3["cord_blood"] == "Not done"){
 $this->Cell(30,10,"Cord blood / 1st hour blood gas: Not done",0,0,'L');
}else{
$text = "";
if($sec3["cord_blood_pH"] != ""){
 $text .= $sec3["cord_blood_pH"].",";
}
if($sec3["cord_blood_pCO2"] != ""){
 $text .= $sec3["cord_blood_pCO2"].",";
}
if($sec3["cord_blood_HCO2"] != ""){
 $text .= $sec3["cord_blood_HCO2"].",";
}
if($sec3["cord_blood_BE"] != ""){
 $text .= $sec3["cord_blood_BE"].",";
}
$this->Cell(30,10,"Cord blood / 1st hour blood gas:".substr($text, 0, -1),0,0,'L');
}//else
$this->Ln();
$this->Cell(30,10,"Delayed cord clamping / Milking: ".$sec3["delayed_cord_clamping"],0,0,'L');
$this->Ln();
}//section3
 function section4(){
        $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
        $this->SetFont('Times','B','12');
        $query = mysql_query("SELECT * FROM `section4` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
        $sec4 = mysql_fetch_assoc($query);
        $this->Cell(250,10,'Section4',1,0,'C');
        $this->Ln();
        $this->Cell(80,10,"Date of birth:".$sec4["DOB"],0,0,'L');
        $this->Cell(80,10,"Time of birth:".$sec4["TOB"],0,0,'L');
        $this->Ln();
        $this->Cell(30,10,"Gender:".$sec4["gender"],0,0,'L');
        $this->Ln();
        $this->Cell(30,10,"Gestational age:".$sec4["gestational_age_week"]." week(s) ".$sec4["gestational_age_day"]." day(s)",0,0,'L');
        $this->Ln();
        $this->Cell(30,10,"Birth weight: ".$sec4["birth_weight"],0,0,'L');
$this->Ln();
        $this->Cell(30,10,"Length: ".$sec4["length"],0,0,'L');
$this->Ln();
        $this->Cell(30,10,"Head circumference: ".$sec4["head_circumference"],0,0,'L');
$this->Ln();
        $this->Cell(30,10,"Growth status (Fenton 2013): ".$sec4["growth_status"],0,0,'L');
$this->Ln();

 }//section4
function section5(){
     $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
        $this->SetFont('Times','B','12');
        $query = mysql_query("SELECT * FROM `section5` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
        $sec5 = mysql_fetch_assoc($query);
        $this->Cell(250,10,'Section5',1,0,'C');
        $this->Ln();
$this->Cell(250,10,'',0,0,'C');
$this->Ln();
}//section5
function section6(){
     $hospital = $_SESSION["hospital"];
        $TNR = $_GET["TNR"];
        $this->SetFont('Times','B','12');
        $query = mysql_query("SELECT * FROM `section6` WHERE `TNR` = '".$TNR."' and `hospital` = '".$hospital."'");
        $sec6 = mysql_fetch_assoc($query);
        $this->Cell(250,10,'Section6',1,0,'C');
        $this->Ln();
$this->Cell(250,10,'',0,0,'C');
$this->Ln();
}//section6

}//class
 
$pdf = new myPDF();
$pdf->AliasNbPages();
$firstpage = true;


if($section1 == 'true'){
if($firstpage){
$pdf->AddPage('L','A4',0);
$pdf->headerTable();
$firstpage = false;
}else{
$pdf->AddPage('L','A4',0);
}
$pdf->section1();

}
if($section2 == 'true'){
if($firstpage){
$pdf->AddPage('L','A4',0);
$pdf->headerTable();
$firstpage = false;
}else{
$pdf->AddPage('L','A4',0);
}
$pdf->section2();
}
if($section3 == 'true'){
if($firstpage){
$pdf->AddPage('L','A4',0);
$pdf->headerTable();
$firstpage = false;
}else{
$pdf->AddPage('L','A4',0);
}
$pdf->section3();
}
if($section4 == 'true'){
if($firstpage){
$pdf->AddPage('L','A4',0);
$pdf->headerTable();
$firstpage = false;
}else{
$pdf->AddPage('L','A4',0);
}
$pdf->section4();
}
if($section5 == 'true'){
if($firstpage){
$pdf->AddPage('L','A4',0);
$pdf->headerTable();
$firstpage = false;
}else{
$pdf->AddPage('L','A4',0);
}
$pdf->section5();
}
if($section6 == 'true'){
if($firstpage){
$pdf->AddPage('L','A4',0);
$pdf->headerTable();
$firstpage = false;
}else{
$pdf->AddPage('L','A4',0);
}
$pdf->section6();
}

$pdf->Output("S");


?>