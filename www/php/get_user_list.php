<?php
 include('header.php');
 session_start();
 $hospital = $_SESSION["hospital"];
 $status = $_POST["status"];
 //$query = mysql_query("select * from patient where hospital = '".$hospital."' ");
 //$query = mysql_query("select * from patient where hospital = '".$hospital."' and status = '".$status."'");
 
 echo json_encode(get_em("select username, firstname, lastname from user where (role = 'collector' or role = 'doctor') and hospital = '$hospital' and status = '$status'"));

 /*
 $ob = Array();
 for($i = 0;$i<mysql_num_rows($query);$i++){
    $temp = mysql_fetch_assoc($query);
    $ob2 = {fullname:$temp["fullname"],TNR:$temp["TNR"]};
    array_push($ob,$ob2);
 }
 */
?>