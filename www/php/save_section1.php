<?php
  include('header.php');
  session_start();
  $TNR = $_SESSION["TNR"];
  $hospital = $_SESSION["hospital"];
  if(isset($_POST["hospital"])&&$_POST["hospital"]!=""){
   $hospital = $_POST["hospital"];
 }
  $status = "save";
  $user = $_SESSION["username"];;
  $date = date("Y-m-d H:i:s");

  $data = json_decode($_POST["data"]);
  $temp = only_one_ex($data);
  array_unshift($temp,$hospital);
  array_unshift($temp,$TNR);
  array_push($temp,$status);
  array_push($temp,$user);
  array_push($temp,$date);
  $table = $_POST["num"];
  if($table == "2_2"){
    $table = "2_abnormal_serology";
  }else if($table == "2_3"){
    $table = "2_complication_during_pregnancy";
  }else if($table == "2_4"){
    $table = "2_intrapartum_complication";
  }else if($table == "2_5"){
    $table = "2_meternal_medication";
  }
  if($_POST["num"]=="4"){   
     //echo json_encode("4");
     $query = mysql_query("select * from section4 where TNR = '".$TNR."' and hospital = '".$hospital."'");
     if(mysql_num_rows($query)!=0){
        $tempx = mysql_fetch_assoc($query);
        if($tempx["DOB"]!=$temp[2]){
          // echo json_encode("change DOB");
          //section6 
          $query6 = mysql_query("select * from section6 where TNR = '".$TNR."' and hospital = '".$hospital."'");
          if(mysql_num_rows($query6)!=0){
           $temp6 = mysql_fetch_assoc($query6);
           if($temp6["discharge_date"]!=""){
             mysql_query("update section6 set home_at_age = '".diffDate($temp[2],$temp6["discharge_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
           }

           if($temp6["transfer_date"]!=""){
             mysql_query("update section6 set transfer_at_age = '".diffDate($temp[2],$temp6["transfer_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
           }
 
           if($temp6["dead_date"]!=""){
             mysql_query("update section6 set dead_age = '".diffDate($temp[2],$temp6["dead_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
           }
          }
           
          //section5_7
          $query5_7 = mysql_query("select * from section5_7 where TNR = '".$TNR."' and hospital = '".$hospital."'");
          if(mysql_num_rows($query5_7)!=0){
             $temp5_7  = mysql_fetch_assoc($query5_7);
             if($temp5_7["start_date"]!=""){
                 mysql_query("update section5_7 set age = 'Age ".diffDate($temp[2],$temp5_7["start_date"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
             }
          }

          //section5_8
          $query5_8 = mysql_query("select * from section5_8 where TNR = '".$TNR."' and hospital = '".$hospital."'");
          if(mysql_num_rows($query5_8)!=0){
             $temp5_8  = mysql_fetch_assoc($query5_8);
             if($temp5_8["start_reach_120_ml"]!=""){
                 mysql_query("update section5_8 set age = 'Age ".diffDate($temp[2],$temp5_8["start_reach_120_ml"])."' where TNR = '".$TNR."' and hospital = '".$hospital."'");
             }
          }

          //section5_12
          $query5_12 = mysql_query("select * from section5_12 where TNR = '".$TNR."' and hospital = '".$hospital."'");
          if(mysql_num_rows($query5_12)!=0){
             $temp5_12  = mysql_fetch_assoc($query5_12);
             if($temp5_12["cooling_start_at_date"]!=""&&$temp5_12["cooling_start_at_time"]!=""){
                 ////// do something
                   $goodtime = $temp[2]." ".$temp[3];
                   $temp5_12_q = mysql_query("SELECT TIMEDIFF(CONCAT(cooling_start_at_date, ' ', cooling_start_at_time), '$goodtime') AS time_diff 
                                               FROM section4, section5_12 WHERE section4.TNR = section5_12.TNR AND section4.hospital = section5_12.hospital 
                                               AND section4.TNR = '$TNR' AND section4.hospital = '$hospital'");

                   if(mysql_num_rows($temp5_12_q) > 0){
                        $temp5_12_a = mysql_fetch_assoc($temp5_12_q);
                        $temp5_12_time = $temp5_12_a["time_diff"];
                        $temp5_12_time = substr($temp5_12_time, 0, sizeof($temp5_12_time)-4);
                        mysql_query("UPDATE section5_12 SET after_birth = '$temp5_12_time' WHERE TNR = '$TNR' AND hospital = '$hospital' ");
                   }
             }
          }
           
           //section5_9
           //clear BPD
            $query = mysql_query("select * from section5_9 where TNR = '".$TNR."' and hospital = '".$hospital."'");
            mysql_query("delete from section5_9 where TNR = '".$TNR."' and hospital = '".$hospital."'");
            $array = ["death","death_date","death_day","death_day2","death_reason","PNA_start_date","PNA_end_date","PNA_diagnosis","PNA_nasal","PMA_start_date","PMA_end_date","PMA_diagnosis","PMA_intubation","PMA_CPAP","PMA_NC_1","PMA_NC_2","PMA_O2","old_definition","old_definition_severe","new_definition","new_definition_severe"];
            for($i=0;$i<mysql_num_rows($query);$i++){
              $temp5_9 = mysql_fetch_assoc($query);
              for($j = 0;$j<sizeof($array);$j++){
                 $temp5_9[$array[$j]] = "";
              }
              ob_to_database("section5_9",$temp5_9); 
            } 
            mysql_query("update section_progress set section5_9 = 'not done' where TNR = '".$TNR."' and hospital = '".$hospital."'");
        }

        if($tempx["gestational_age_week"]!=$temp[5]||$tempx["gestational_age_day"]!=$temp[6]){
           echo json_encode("change G / A");
            //section5_9
           //clear BPD
           $query = mysql_query("select * from section5_9 where TNR = '".$TNR."' and hospital = '".$hospital."'");
            mysql_query("delete from section5_9 where TNR = '".$TNR."' and hospital = '".$hospital."'");
            $array = ["death","death_date","death_day","death_day2","death_reason","PNA_start_date","PNA_end_date","PNA_diagnosis","PNA_nasal","PMA_start_date","PMA_end_date","PMA_diagnosis","PMA_intubation","PMA_CPAP","PMA_NC_1","PMA_NC_2","PMA_O2","old_definition","old_definition_severe","new_definition","new_definition_severe"];
            for($i=0;$i<mysql_num_rows($query);$i++){
              $temp5_9 = mysql_fetch_assoc($query);
              for($j = 0;$j<sizeof($array);$j++){
                 $temp5_9[$array[$j]] = "";
              }
              ob_to_database("section5_9",$temp5_9); 
            }
 mysql_query("update section_progress set section5_9 = 'not done' where TNR = '".$TNR."' and hospital = '".$hospital."'");
        }
     }
  }
  mysql_query("delete from section".$table." where TNR = '".$TNR."' and hospital = '".$hospital."'");
  add_to_database("section".$table,$temp);
 // array_push($temp,$table);
  //echo json_encode($temp); 
?>