<?php

 include('header.php');
$TNR= $_POST["TNR"];
$type= $_POST["type"];
if($type=="30d")
{
$query = mysql_query("SELECT * FROM Noti_30D WHERE TNR= '$TNR' ");
 $temp = mysql_fetch_assoc($query);
}
if($type=="15d")
{
$query = mysql_query("SELECT * FROM Noti_15D WHERE TNR= '$TNR' ");
 $temp = mysql_fetch_assoc($query);
}
 
  echo($temp[noti_id]);
$ch = curl_init();
$httpHeader = array(
      'Authorization: Basic YmQ2NzUzM2ItNTIzNy00ZjY1LWIwNmQtYzRjYWYxNGEyNDRl'
    );
$url = "https://onesignal.com/api/v1/notifications/".$temp[noti_id]."?app_id=f342b998-55f0-4995-8b7d-e43012ff0c31";

$options = array (
  CURLOPT_URL => $url,
  CURLOPT_HTTPHEADER => $httpHeader,
  CURLOPT_RETURNTRANSFER => TRUE,
  CURLOPT_CUSTOMREQUEST => "DELETE",
  CURLOPT_SSL_VERIFYPEER => FALSE
);
curl_setopt_array($ch, $options);
$response = curl_exec($ch);
curl_close($ch);
 

?>