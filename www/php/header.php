<?php
	header("Access-Control-Allow-Origin: *");

	$database = "thainy";
	$host = 'localhost';
	$username = 'happyvernity';
	$password = '@vernity';

	// Create connection using MySQLi
	$mysqli = new mysqli($host, $username, $password, $database);

	// Check connection
	if ($mysqli->connect_error) {
		die('Could not connect: ' . $mysqli->connect_error);
	}

	// Set charset
	$mysqli->set_charset("utf8");
        function diffDate($start,$stop){
          $date1=date_create($start);
          $date2=date_create($stop);
          $diff=date_diff($date1,$date2);
          return $diff->format("%a");
        } 
        function add_to_database($table,$data,$run){
		global $mysqli;
		$password=-1;
		$query = $mysqli->query("select * from ".$table);
		$qtext = "insert into ".$table." (";
		$fields = $query->fetch_fields();
		for($i = 0;$i<count($fields);$i++){
			$qtext .= $fields[$i]->name;
			if($fields[$i]->name=="password"){
				$password = $i;
			}
			if($i!=count($fields)-1){
				$qtext .= ",";
			}
		}
		$qtext .= ") values (";
		for($i = 0 ; $i<sizeOf($data);$i++){
			if($password==$i){
				$data[$i] = md5($data[$i]);
			}
			if(isset($run) && $i == 0){
				$qtext .= "NULL,";
			}

			$qtext .= "'".$mysqli->real_escape_string($data[$i])."'";

			if($i!= sizeOf($data)-1){
				$qtext .= ",";
			}
		}
		$qtext .= ");";

		$mysqli->query($qtext);
		return $qtext;
	}
        function generateTNR($regist_date,$hospital){
            global $mysqli;
            if(intval(explode("-",$regist_date)[1]) < 10){
                $month = "0".explode("-",$regist_date)[1];
            }else{
                $month = explode("-",$regist_date)[1];
            }
            $year = explode("-",$regist_date)[0];
            $query = $mysqli->query("select * from patient where TNR LIKE '".$year.$month."%'");
            $query2 = $mysqli->query("select * from hospital where hospital_name = '".$mysqli->real_escape_string($hospital)."'");
            $temp = $query->fetch_assoc();
            $temp2 = $query2->fetch_assoc();
            $number = $query->num_rows+1;
            $num = $number;

            if($num < 10000){
               $number = "0".$number;
            }
            if($num < 1000){
               $number = "0".$number;
            }
            if($num < 100){
               $number = "0".$number;
            }
            if($num<10){
               $number = "0".$number;
            }

            $TNR = $year.$month.$number;
            return $TNR;
        }

/*backup*/
/*
  function generateTNR($regist_date,$hospital){
            $query = mysql_query("select * from patient where hospital = '".$hospital."'");
            $query2 = mysql_query("select * from hospital where hospital_name = '".$hospital."'");
            $temp = mysql_fetch_assoc($query);
            $temp2 =  mysql_fetch_assoc($query2);
            $number = mysql_num_rows($query);
            $num = $number;
            if($num < 10000){
               $number = "0".$number;
            }
            if($num < 1000){
               $number = "0".$number;
            }
            if($num < 100){
               $number = "0".$number;
            }
            if($num<10){
               $number = "0".$number;
            }
            $TNR = explode("-",$regist_date)[0]. $temp2["hospital_number"].$number;
            return $TNR;
        }
*/
/*backup*/

        function get_em($sql){
           global $mysqli;
           $query = $mysqli->query($sql);
           $array = Array();
           while($row = $query->fetch_assoc()){
             array_push($array, $row);
           }
           return $array;
        }
        function only_one_ex($data){
			$ob = Array();
			for($i = 0 ;$i<sizeOf($data);$i++){
				if(sizeOf($data[$i])==1&&$data[$i][0]!="checkbox"){
                                        if(is_array($data[$i])){
					      array_push($ob,$data[$i][0]);
                                              //echo "!check array";
                                        }else{
                                              array_push($ob,$data[$i]);
                                              // echo "!check";
                                       }
                                      // echo "!check";
				}
                                else{
                                        if(!isset($data[$i][1])){
                                              array_push($ob,'');
                                        }else{
                                              array_push($ob,$data[$i][1]);
                                        } 
                                      //  echo "check";
                                }
			}
			return $ob;
	}
        function ob_to_database($table,$ob,$run){
             global $mysqli;
             $password=-1;
             $column = Array();
		$query = $mysqli->query("select * from ".$table);
		$qtext = "insert into ".$table." (";
		$fields = $query->fetch_fields();
		for($i = 0;$i<count($fields);$i++){
			$qtext .= $fields[$i]->name;
                        array_push($column,$fields[$i]->name);
			if($fields[$i]->name=="password"){
				$password = $i;
			}
			if($i!=count($fields)-1){
				$qtext .= ",";
			}
		}
		$qtext .= ") values (";
		for($i = 0 ; $i<sizeOf($column);$i++){

                        if($column[$i]=="password"){
                           $ob[$column[$i]] = md5($ob[$column[$i]]);
                        }
                        if(isset($run) && $column[$i] == "id"){
			  $qtext .= "NULL";
			}else{
			  $qtext .= "'".$mysqli->real_escape_string($ob[$column[$i]])."'";
                        }
			if($i!= sizeOf($column)-1){
				$qtext .= ",";
			}
		}
		$qtext .= ");";

		$mysqli->query($qtext);
		return $qtext;
        }
?>