<?php
   include("header.php");
    $token= $_POST["token"];
     $user_id= $_POST["user_id"];
     $hos_id= $_POST["hos_id"];
      $p_id= $_POST["p_id"];

    // 
   $query = mysql_query("SELECT * FROM user_notify WHERE username= '$p_id'  ");
     
  if($query == 0 || mysql_num_rows($query) == 0){
    mysql_query("INSERT INTO user_notify(username,hos_id,user_id) values ('$p_id','$hos_id','$user_id') ");
    echo("1");
  }else{
   $query2 = mysql_query("SELECT username FROM user_token WHERE token= '$token'  ");
    $temp = mysql_fetch_assoc($query2);
    $u_noti = $temp['username'];
    $query3 = mysql_query("SELECT * FROM user_notify WHERE username = '$u_noti' ");   
    $temp2 = mysql_fetch_assoc($query3);
   echo json_encode($temp2);
 }
  
  
  // $query2 = mysql_query("UPDATE * SET user_id  = '$user_id' where username = '$temp[username]' ");

?>