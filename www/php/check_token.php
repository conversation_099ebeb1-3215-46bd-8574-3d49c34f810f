<?php
  include('header.php');
  
  session_start();
  $token = $_POST["token"];
  $query = mysql_query("SELECT u.username, u.hospital, token, pin, status FROM user_token ut 
                          INNER JOIN user u ON ut.username = u.username
                          LEFT JOIN pin p ON ut.username = p.username 
                          WHERE token = '".$token."'
                          AND status != 'reject' ");
 
  if(mysql_num_rows($query)==0){
    echo json_encode(0);
  }else{
    $temp = mysql_fetch_assoc($query);
    $_SESSION["username"] = $temp["username"];
    $_SESSION["hospital"] = $temp["hospital"];
    echo json_encode(array("status" => 1, "pin" => $temp["pin"], "user_status" => $temp["status"]));
  }
  
?>