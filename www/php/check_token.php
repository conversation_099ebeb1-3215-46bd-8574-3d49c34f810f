<?php
  include('header.php');

  session_start();
  $token = $_POST["token"];

  // Use prepared statement to prevent SQL injection
  $stmt = $mysqli->prepare("SELECT u.username, u.hospital, token, pin, status FROM user_token ut
                          INNER JOIN user u ON ut.username = u.username
                          LEFT JOIN pin p ON ut.username = p.username
                          WHERE token = ?
                          AND status != 'reject'");
  $stmt->bind_param("s", $token);
  $stmt->execute();
  $result = $stmt->get_result();

  if($result->num_rows == 0){
    echo json_encode(0);
  }else{
    $temp = $result->fetch_assoc();
    $_SESSION["username"] = $temp["username"];
    $_SESSION["hospital"] = $temp["hospital"];
    echo json_encode(array("status" => 1, "pin" => $temp["pin"], "user_status" => $temp["status"]));
  }

  $stmt->close();
?>