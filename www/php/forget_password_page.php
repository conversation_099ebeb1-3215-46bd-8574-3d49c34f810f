<head>
  <meta name="format-detection" content="telephone=no">
  <meta name="msapplication-tap-highlight" content="no">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">


  <script type="text/javascript" src="../js/jquery-3.1.1.min.js"></script>
  <script type="text/javascript" src="../js/jquery-ui.min.js"></script>
  <script type="text/javascript" src="../js/jquery.easing.1.3.min.js"></script>
  <script type="text/javascript" src="../js/jquery.transit.min.js"></script>
  <title>Forget Password</title>

</head>

<body>

  <div stylr="position:fixed;"><span style="font-size:2em;color:black;">เปลี่ยนรหัสผ่านใหม่</span></div>
  <div style=" width:100%; margin-top:60px; ">

    <div style=" width:100%; position:fixed; left:50%; transform: translate(-30%, 0); ">
      <p style="font-size:1em; ">รหัสผ่านใหม่</p>
      <input style="width:50%;border:0px;font-size:16px;background-color:#F2F2F2;border-radius:8px;height:48;" type="password"
        id="password"></div>

  </div style="position:absolute; left:0;right:0;">
  <div>
    <div style=" width:100%; margin-top:60px;">


      <div style=" width:100%; position:fixed; left:50%; transform: translate(-30%, 0); margin-top:100px; ">
        <p style="font-size:1em;">ยืนยันรหัสผ่านใหม่</p>
        <input style="width:50%;border:0px;font-size:16px;background-color:#F2F2F2;border-radius:8px;height:48;" type="password"
          id="re_password">

      </div>

    </div>

    <div style=" width:100%; margin-top:200px; position:fixed; left:50%; transform: translate(-30%, 0);  ">
      <p id="wrong1" style="color:red;font-size:0.9em; ">ต้องประกอบด้วยตัวเลข</p>
      <p id="wrong2" style="color:red;font-size:0.9em; ">ต้องประกอบด้วยตัวอักษรพิมพ์ใหญ่และพิมพ์เล็ก</p>
      <p id="wrong3" style="color:red;font-size:0.9em; ">ต้องประกอบด้วยอักขระอย่างน้อยแปดตัว</p>

      <button style="width:50%; background-color:#37BCF0; border:solid 1px; border-radius:8px;height:48; color:white; font-size:0.9em; "
        id="button">
        เปลี่ยนรหัสผ่าน
      </button>


    </div>
  </div>

  <script>
    document.getElementById("button").onclick = function () {
      var password = document.getElementById("password").value;
      var re_password = document.getElementById("re_password").value;
      var x = document.getElementById("wrong1").innerHTML;
      var y = document.getElementById("wrong2").innerHTML;
      var z = document.getElementById("wrong3").innerHTML;
      if (x != "" || y != "" || z != "") {
        alert("กรุณากรอกรหัสผ่านให้ตรงตามเงื่อนไข");
      }
      else if (password != re_password) {
        alert("รหัสผ่านไม่ตรงกัน");
      } else {
        $.ajax({
          type: "POST",
          url: "https://techvernity.com/thainy/php/reset_password.php",
          data:{
            token: "<?= $_GET["token"] ?>",
            password: document.getElementById("password").value
          },
          success: function (data) {
            //alert(data);
            var result = JSON.parse(data);
            if (result.status == 1) {
              alert("เปลี่ยนรหัสผ่านสำเร็จ");
            } else {
              alert(result.text);
            }
          },
          error: function (jqXHR, exception) {
            alert("error");

          }
        });
      }
    }
    //alert("test");
    document.getElementById("password").onkeyup = function () {
      var x = document.getElementById("password");
      // alert(x.value);
      if (x.value.match("[0-9]")) {
        //alert("pass1"); 
        document.getElementById("wrong1").innerHTML = "";
      }
      else {
        document.getElementById("wrong1").innerHTML = "ต้องประกอบด้วยตัวเลข";
      }
      if (x.value.match("[a-z]") && x.value.match("[A-Z]")) {
        //alert("pass2");
        document.getElementById("wrong2").innerHTML = "";
      } else {
        document.getElementById("wrong2").innerHTML = "ต้องประกอบด้วยตัวอักษรพิมพ์ใหญ่และพิมพ์เล็ก";
      }
      if (x.value.toString().length >= 8) {
        //alert("pass3");
        document.getElementById("wrong3").innerHTML = "";
      } else {
        document.getElementById("wrong3").innerHTML = "ต้องประกอบด้วยอักขระอย่างน้อยแปดตัว";
      }
    };
  </script>

</body>