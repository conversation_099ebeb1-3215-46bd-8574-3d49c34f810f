<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <link rel="stylesheet" type="text/css" href="css/patientlist.css">
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="home.html" onclick="transition_page_back('home.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">นำออกข้อมูล</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                            <div class="txt-15 margin-btm margin-top">การนำออกข้อมูลใดๆออกจาก ThaiNy จะไม่สามารถนำข้อมูลที่สามารถระบุตัวต้นของผู้ป่วยออกไปได้</div>
                                    <div class="txt-15 margin-btm">ข้อมูลที่นำออกจะถูกส่งไปยังอีเมลที่ผู้ใช้ได้สมัครไว้</div>
                                            <div class="txt-15 margin-btm">รหัสในการเปิดไฟล์คือ วัน/เดือน/ปีเกิด ของผู้นำออก
                                                    ข้อมูล เช่น 7 มีนาคม 2538 รหัสคือ 07032538 เป็นต้น</div>
                        <div id="export_individual" class="list-setting margin-i1tem clearfix">
                            <img src="img/export_personal.svg">
                            <div class="box-adaptive">
                                <div>นำออกข้อมูลรายบุคคล </div>
                                <div class="sd-txt">นำข้อมูลของคนไข้ที่ต้องการออกมา โดย
                                    สามารถเลือกข้อมูลต่างๆของคนไข้ที่ต้องการได้ </div> <img src="img/next3.svg">
                            </div>
                        </div>
                        <div class="list-setting margin-i1tem clearfix">
                            <img src="img/export_hospital.svg">
                            <div class="box-adaptive">
                                <div>นำออกข้อมูลตามโรงพยาบาล </div>
                                <div class="sd-txt">นำข้อมูลของคนไข้ทุกคนตามโรงพยาบาลออกมา โดยสามารถเลือกกลุ่มของข้อมูลที่ต้องการได้</div> <img src="img/next3.svg">
                            </div>
                        </div>
                        <div class="list-setting margin-i1tem clearfix">
                            <img src="img/export_overallphoto.svg">
                            <div class="box-adaptive">
                                <div>นำออกข้อมูลภาพรวม </div>
                                <div class="sd-txt">นำข้อมูลออกของทุกๆโรงพยาบาลในระบบ โดยสามารถเลือกกลุ่มของข้อมูลที่ต้องการได้ </div> <img src="img/next3.svg">
                            </div>
                        </div>

                        <div class="list-setting margin-i1tem clearfix">
                                <img src="img/req.svg">
                                <div class="box-adaptive">
                                    <div>รายการคำร้อง </div>
                                    <div class="number-noty-2">3</div>
                                    <img src="img/next3.svg">
                                </div>
                        </div>
    


                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
    <script src="js/switch.js"></script>
    <script src="js/export_data.js"></script>
    
    
   
</body>

</html>