<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/forgetpassword.css">
    <link rel="stylesheet" type="text/css" href="css/popup.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body>
    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="login.html" onclick="transition_page_back('login.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">ลืมรหัสผ่าน</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="forget-detail">โปรดป้อนอีเมลที่ใช้ในการสมัครใช้งานของท่านเพื่อดำเนินการการตั้งค่ารหัสผ่านใหม่</div>
                    <div class="error-box" style="visibility: hidden;">
                        <div class="error-text"><img class="" src="img/error.svg">
                            บัญชีผู้ใช้หรือรหัสผ่านไม่ถูกต้อง</div>
                    </div>
                    <input class="btn-content" id="input-email" type="email" placeholder="อีเมล">
                    <div class="btn-content login-btn">ยืนยัน</div>
                </div>
            </div>
        </div>
    </section>


    <!-- <footer class="footer">
        <div class="">
            <img src="img/active.svg">
        </div>
        <div class="">
                <img src="img/inactive.svg">
            </div>
    </footer> -->

    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
    <script src="js/forget_password.js"></script>
  
    
    
</body>

</html>