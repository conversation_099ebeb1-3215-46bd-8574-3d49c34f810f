<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">
    

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patientlist.html" onclick="transition_page_down('patientlist.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Registry Criteria</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Admit Ward</div>
                        </div>
                        <div class="section-head-reg margin-btm4">คนไข้ถูก admit เข้า ward ใด?</div>

                        <div class="ward-choice" onclick="select_criteria1('NICU')" id = "NICU">NICU</div>
                        <div class="ward-choice" onclick="select_criteria1('sick')" id = "sick">Sick Newborn</div>
                        <div class="ward-choice" onclick="select_criteria1('other')" id = "other"> อื่นๆ</div>

                        <!-- btn -->
                        <div class="save-btn"onclick="to_next1()">
                            <span class="set_center" >ถัดไป</span>
                        </div>
                        <div class="section-box">
                            <div class="svisited section-active "><span class="set_center">1</span></div>
                            <div class="section-line uncorrect"></div>
                            <div class="svisited section-unactive"><span class="set_center">2</span></div>
                            <div class="section-line uncorrect"></div>
                            <div class="svisited section-unactive"><span class="set_center">3</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
   
    <script src="js/registration.js"></script>
</body>
<script>
        window.onload = function(){
            switch(sessionStorage.flow1){
       case "NICU" :{
        select_criteria1('NICU')
        break;
      }
        case "sick" :{
            select_criteria1('sick')
         
       
          break;
    
        }
        case "other" :{
            select_criteria1('other')
        
          break;
    
        }
     }
     
    }
        </script>
</html>