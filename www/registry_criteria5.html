<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/criteria_result.css">
   

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>

</head>

<body class="cri5">

    <section class="header">
        <div class="content_box">
               <!-- 
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="registry_criteria3.html" onclick="transition_page_back('registry_criteria3.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/backwhite.svg">

                        </section>
                                   <section id="title_header" class="title-main">กลับ</section>-->
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <!-- <section class="title">Registry Criteria</section> -->
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <img class="cri-img " src="img/faillogin.svg">
                        <div class="cri-text1">ไม่ตรงตามเงื่อนไข</div>
                        <div class="cri-text2 color-text-pink">คนไข้ไม่ตรงตามเงื่อนไขในการบันทึกข้อมูล<br/>
                                ลงระบบ Thai National NewBorn Registry
                        </div>

                        <div class="section-head">
                            <div>Admit Ward</div>
                        </div>
                        <div class="fillter-each" id ="arae1">
                            <div><img src="img/correct-section.svg"></div>
                            NICU
                        </div>

                        <div class="section-head">
                            <div>PNA (Postnatal Age)</div>
                        </div>
                        <div class="fillter-each" id ="arae2">
                            <div><img src="img/correct-section.svg"></div>
                            15 Day(s)
                        </div>


                        <div class="section-head">
                            <div>อาการของคนไข้</div>
                        </div>
                        <div class="fillter-each" id ="arae3">
                            <div><img src="img/uncorrect-section.svg"></div>
                            อื่นๆ
                        </div>

                        <!-- btn -->
                        <a href="patientlist.html" onclick="transition_page_down('patientlist.html')">
                        <div class="btn-footer">
                                <div class="btn-content next-ntn color-btn-pink"><span class="set_center">สิ้นสุดการตรวจสอบ </span></div>
                        </div>
                    </a>
                      

                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    <script src="js/main.js"></script>
   
    <script src="js/registration.js"></script>
    <script>
            window.onload =function() {
                if(sessionStorage.flow1.toString() == "sick"){
                    sessionStorage.flow1 = "Sick Newborn";
                }
                
                if(sessionStorage.flow1.toString() == "other"){
                    sessionStorage.flow1 = "อื่นๆ";
                }

                document.getElementById("arae1").innerHTML = sessionStorage.flow1.toString();
                document.getElementById("arae2").innerHTML = sessionStorage.flow2.toString();
                document.getElementById("arae3").innerHTML = sessionStorage.flow3.toString();
                sessionStorage.flow1 = undefined;
                sessionStorage.flow2 = undefined;
                sessionStorage.flow3 = undefined;
            }
            </script>
</body>

</html>