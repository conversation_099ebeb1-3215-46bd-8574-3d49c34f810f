<!DOCTYPE html>
<html>

<head>
        <!-- setting -->
        <title>ThaiNy</title>
        <link rel="shortcut icon" href="img/logo.svg" />

        <!-- viewport -->
        <meta name="format-detection" content="telephone=no">
        <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

        <!-- css -->
        <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
        <link rel="stylesheet" type="text/css" href="css/web.css">
        <link rel="stylesheet" type="text/css" href="css/registration.css">
        <link rel="stylesheet" type="text/css" href="css/popup.css">

        <!-- js -->
        <script src="js/jquery-3.1.1.min.js"></script>
        <script src="js/jquery.easing.1.3.min.js"></script>

</head>

<body>
        <!-- header -->
        <section class="header_web">
                <img class="img_logo" src="">
        </section>


        <!-- popup -->

        <div class="popup-fake">
                <div class="content_box">
                        <div class="ipd-popup">
                                <div class="popup-box color-pink">
                                        <img src="img/faillogin.svg">
                                        <div class="popup-text1">ไม่ถูกต้อง</div>
                                        <div class="popup-text2 color-text-pink">ดูเหมือนว่าอีเมลที่คุณกรอก<br />
                                                ไม่ตรงกับที่อยู่ในระบบ
                                        </div>
                                        <div class="popup-btn">ลองอีกครั้ง</div>
                                </div>

                                <div class="popup-box2 color-green">
                                        <img src="img/complete.svg">
                                        <div class="popup-text1">สำเร็จ</div>
                                        <div class="popup-text2 color-text-green">เราได้ส่งลิงค์การตั้งค่ารหัสผ่านใหม่<br>
                                                ไปยัง “<span id="txt-email"></span>” <br>
                                                เรียบร้อยแล้ว
                                        </div>
                                        <div class="popup-btn">ลองอีกครั้ง</div>
                                </div>

                        </div>
                </div>
        </div>

        <!-- content -->

        <section class="content_login">
                <div class="menuhome-leftbar">
                        <div class="head-home">
                                <img src="img/profile.svg">
                                <div class="name-box">
                                        <div class="name-person" id="fullname">พญ. ฉัตร์ฉายบุษฑิตา เปรมพันธ์พงษ์</div>
                                        <div class="name-hospital" id="hospital">โรงพยาบาลรามาธิบดี</div>
                                </div>
                                <div class="rank-box">
                                        <span class="rank" id="status">หัวหน้า</span>
                                </div>

                        </div>
                        <div class="list-content">

                                <div id="patient_list" class="list-home web margin-i1tem">
                                        <img class="img_list" src="img/list.svg">
                                        <div>รายการผู้ป่วย</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="track_tn" class="list-home web">
                                        <img class="img_list" src="img/search2.svg">
                                        <div>ค้นหาหมายเลข TN</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="export" class="list-home web">
                                        <img class="img_list" src="img/export.svg">
                                        <div>นำออกข้อมูล</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="help" class="list-home web">
                                        <img class="img_list" src="img/help.svg">
                                        <div>ช่วยเหลือและสนับสนุน</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="approve" class="list-home web">
                                        <img class="img_list" src="img/approve2.svg">
                                        <div>อนุมัติบัญชีผู้ใช้</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div id="setting" class="list-home web margin-top24">
                                        <img class="img_list" src="img/setting.svg">
                                        <div>ตั้งค่า</div>
                                        <img class="img_next3" src="img/next3.svg">
                                </div>
                                <div class="absolute">
                                        <div id="logout" class="list-home web margin-top24">
                                                <img class="img_list" src="img/logout.svg">
                                                <div>ออกจากระบบ</div>
                                        </div>
                                </div>

                        </div>

                </div>
                <div class="content-main">
                       <div class="back-box contentmain"><img src="img/backgreen.svg"> <span>กลับ</span></div>
                        <div class="web_head">Registration</div>
                        <div class="content-in">
                                <div class="web_2_head">Infant (patient) information</div>
                                <div class="box2_content clearfix">
                                        <div class="box1">
                                                <div class="head-input">Name<span class="color-red">*</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Name"
                                                        id="name" name = "i1">
                                                <div class="head-input">Infant’s ID Number - <span class="Optional">Optional</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Infant’s ID Number"
                                                        id="ID" name = "i3">
                                                <div class="head-input">Sex<span class="color-red">*</span></div>
                                                <input type = "text" name = "i5" value = "Male" hidden>
                                                <div class="choose-sex">
                                                        <div class="choose-fm" id="M1" onclick="choose('male')"><span
                                                                        class="set_center">Male</span></div>
                                                        <div class="unchoose-fm" id="F1" onclick="choose('female')"><span
                                                                        class="set_center">Female</span></div>
                                                </div>
                                        </div>
                                        <div class="box2">
                                                <div class="head-input">Hospital Number (HN)<span class="color-red">*</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Hospital Number (HN)"
                                                        id="HN" name = "i2">
                                                <div class="head-input">Date of birth<span class="color-red">*</span></div>
                                                <input class="input-register" type="text" placeholder="Date of birth"
                                                        id="date" name= "i4" ontouchend="setting_input('date',this);" data-input="date" >
                                                <div class="head-input">Ethnic</div>
                                                <input class="input-register margin-btm" type="text" placeholder="Ethnic"
                                                        id="ethnic" name = "i6">
                                        </div>
                                </div>
                                <div class="web_2_head margin-top40">Mother information</div>
                                <div class="box2_content clearfix">
                                        <div class="box1">
                                                <div class="head-input">Mother’s name <span class="color-red">*</span>
                                                        <span class="choose_sd">Thai or English ชื่อตาม ID</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Infant’s ID Number"
                                                        id="mothername" name ="i7">
                                                <div class="head-input">Mother’s ID number<span class="color-red">*</span><span
                                                                class="choose_sd"> ไทยใช้เลขบัตรประชาชน
                                                                ต่างชาติใช้เลขพาสปอร์ต</span></div>

                                                <!--  check id card -->
                                                <label class="container">ID card
                                                        <input type="checkbox" id="idcard" name = "i8" value = "ID">
                                                        <span id="boxid" class="checkmark"></span>
                                                </label>
                                                <input id="check-idcard" class="input-register margin-btm" type="text"
                                                        placeholder="ID card" name = "ID_etc" disabled>
                                                <!--  check passport -->
                                                <label class="container">Passport No
                                                        <input type="checkbox" id="idpass" name = "i9" value = "passport">
                                                        <span id="boxpass" class="checkmark"  ></span>
                                                </label>
                                                <input id="check-passport" class="input-register margin-btm" type="text"
                                                        placeholder="Passport No" name = "passport_etc" disabled>
                                        </div>
                                        <div class="box2">
                                                <div class="head-input">Address - <span class="Optional">Optional</span></div>
                                                <textarea placeholder="Address" id="address" name = "i10"></textarea>
                                                <div class="head-input">Telephone number - <span class="Optional">Optional</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Telephone number "
                                                        id="tel" name = "i11">
                                        </div>
                                </div>
                                <div class="web_2_head margin-top40">Contact person</div>
                                <div class="box2_content clearfix">
                                        <div class="box1">
                                                <div class="head-input">Name - <span class="Optional">Optional</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Name"
                                                        id="contactName" name = "i12">
                                                <div class="head-input">Telephone number - <span class="Optional">Optional</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Telephone number"
                                                        id="contactTel" name = "i14">
                                        </div>
                                        <div class="box2">
                                                <div class="head-input">Relationship - <span class="Optional">Optional</span></div>
                                                <input class="input-register margin-btm" type="text" placeholder="Relationship "
                                                        id="contactRelate" name = "i13">
                                                <div class="head-input">Other contact - <span class="Optional">Optional</span>
                                                        <span class="choose_sd">eg. email, line ID</span></div>

                                                <input class="input-register margin-btm" type="text" placeholder="eg. email, line ID"
                                                        id="contactOther" name = "i15">
                                        </div>
                                </div>
                                <div class="save-btn-web" onclick = "save()">
                                        <span class="set_center">Save and Generate TNR No.</span>
                                </div>
                        </div>

                        <input type = "text" name = "username" value = "1">
                        <input type = "text" name = "hospital" value = "vernity">
                        <input type = "checkbox" name  = "password" value = "2" checked >
                        <input type = "checkbox" name  = "password" value = "3" checked >
        </section>



        <!-- <script src="js/main.js"></script> -->
        <script src="js/popup_animate.js"></script>
        <script src="js/web_forgetpassword.js"></script>
        <script src = "js/main.js"></script>
        <script src = "js/web_registration.js"></script>
</body>

</html>