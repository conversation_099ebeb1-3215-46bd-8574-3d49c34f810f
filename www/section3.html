<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patient_display.html" onclick="transition_page_back('patient_display.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " >
                        <!-- <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg"> -->

                    </section>
                </div>
            </section>
                <section class="title">Section 3</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Section 3: Perinatal data </div>
                        </div>
                        <div class="select-head ">Mode of delivery</div>
                        <label class="container_radio">Vaginal
                            <input type="radio"  name = "delivery_mode" value = "Vaginal">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop1" hidden >
                            <label class="container_radio">With instrument
                                <input type="radio"  name = "vaginal" value = "With instrument">
                                <span class="checkmark_radio"></span>
                            </label> <label class="container_radio">Without instrument 
                                <input type="radio"  name = "vaginal" value = "Without instrument ">
                                <span class="checkmark_radio"></span>
                            </label>
                        </div>
                        <label class="container_radio">Caesarean section
                            <input type="radio"  name = "delivery_mode" value = "Caesarean section">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop_new" hidden >
                                <div class="section-head-reg">Intication of caesarean (optional)</div>
                                <input class="input-section margin-btm" type="text" placeholder="Please specify" name = "intication">
                                
                            </div>
                        <!-- ------------------------ -->
                        <div class="select-head margin-top">Apgar score</div>
                        <div class="choose_sd">ถ้าไม่ทราบเลือก N/A</div>
                        <div class="column-sep">
                            <div class="head-input">1 min</div>
                            <input class="input-section margin-btm" type="number" placeholder="คะแนน" name="1_min_score">
                            <label class="container">N/A
                                <input type="checkbox" name="1_min_NA" value="N/A">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <div class="column-sep">
                            <div class="head-input">5 min</div>
                            <input class="input-section margin-btm" type="number" placeholder="คะแนน" name="5_min_score">
                            <label class="container">N/A
                                <input type="checkbox" name="5_min_NA" value="N/A">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <div class="column-sep column-sep-last">
                            <div class="head-input">10 min</div>
                            <input class="input-section margin-btm" type="number" placeholder="คะแนน" name="10_min_score">
                            <label class="container">N/A
                                <input type="checkbox" name="10_min_NA" value="N/A">
                                <span class="checkmark"></span>
                            </label>
                        </div>

                        <!-- ------------------------ -->
                        <div class="select-head margin-top12">Resuscitation<div id="info1" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                        <div class="choose_sd">ถ้าไม่ทราบเลือก N/A</div>
                        <label class="container_radio">No
                            <input type="radio"  name = "resuscitation" value = "No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Yes
                            <input type="radio"  name = "resuscitation" value = "Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop2" hidden> 
                            <label class="container">CPAP
                                <input type="checkbox"  name = "CPAP" value = "CPAP">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">PPV
                                <input type="checkbox"  name = "PPV" value = "PPV">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Intubation
                                <input type="checkbox"  name = "intubation" value = "Intubation">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Chest compression
                                <input type="checkbox"  name = "chest_compression" value = "Chest compression">
                                <span class="checkmark"></span>
                            </label>
                            <label class="container">Epinephrine
                                <input type="checkbox"  name = "epinephrine" value ="Epinephrine">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <label class="container_radio">N/A
                            <input type="radio"  name = "resuscitation" value = "N/A"> 
                            <span class="checkmark_radio"></span>
                        </label>


                        <!-- ------------------------ -->
                        <div class="select-head  margin-top">Cord blood / 1<sup>st</sup> hour blood gas</div>
                        <div class="choose_sd">ถ้าไม่ทราบเลือก N/A</div>

                        <label class="container_radio">Not done
                            <input type="radio"  name = "cord_blood" value = "Not done">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Available
                            <input type="radio"  name = "cord_blood" value = "Available">
                            <span class="checkmark_radio"></span>
                        </label>

                        <div class="inside-box" id = "pop3"  hidden>
                            <div class="section-head-reg">pH<div id="info2" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                            <input class="input-section margin-btm" type="text" placeholder="กรุณากรอก pH" name = "cord_blood_pH">

                            <div class="section-head-reg">pCO<sub>2</sub> (optional)</div>
                            <input class="input-section margin-btm" type="number" placeholder="กรุณากรอก pCO2" name = "cord_blood_pCO2">

                            <div class="section-head-reg">HCO<sub>2</sub> (optional)</div>
                            <input class="input-section margin-btm" type="number" placeholder="กรุณากรอก HCO2" name= "cord_blood_HCO2">

                            <div class="section-head-reg">BE (optional)</div>
                            <input class="input-section margin-btm" type="number" placeholder="กรุณากรอก BE" name = "cord_blood_BE">
                        </div>
                      
                        <!-- ------------------------ -->
                        <div class="section-head-noline margin-top">
                            <div>Delayed cord clamping / Milking</div><img id="info3" src="img/info.svg">
                        </div>
                        <label class="container_radio">No
                            <input type="radio"  name = "delayed_cord_clamping" value  = "No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Yes 
                            <input type="radio"  name = "delayed_cord_clamping" value = "Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">N/A
                            <input type="radio"  name = "delayed_cord_clamping" value = "N/A">
                            <span class="checkmark_radio"></span>
                        </label>




                        <!-- btn -->
                        <div class="save-btn" onclick = "save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
   
    
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section3.js"></script>
</body>

</html>