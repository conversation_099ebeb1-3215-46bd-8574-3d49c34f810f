<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="section5.html" onclick="transition_page_back('section5.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
                    </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 5</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Central line</div><img id="info1" src="img/info.svg">
                        </div>
                        <label class="container_radio">Yes
                            <input type="radio" name="central_line" value="Yes">
                            <span class="checkmark_radio"></span>
                        </label>
                        <!-- UAC -->
                        <div id="central_line_box" hidden>
                            <div id="UAC_box" class="inside-box-with-pad">
                                    <label class="container">UAC <span id="UAC_day" class="black-txt2">0</span>  <span class="gray-txt">days</span>
                                            <input type="checkbox" name="UAC" value="UAC">
                                            <span class="checkmark"></span>
                                        </label>
                                    <div id="UAC_list" hidden>
                                        <div name="UAC1" class="inside-in-with-pad">
                                                <div class="">วันเริ่มต้น</div>
                                                <input class="input-section  input-sec5" name="UAC1_1" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="l"  placeholder="เลือกวัน">
                                                <div class="margin-top8  ">วันสิ้นสุด</div>
                                                <input class="input-section  input-sec5" name="UAC1_2" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="r"  placeholder="เลือกวัน">
                                                <div class="circle-red red-sec5" onclick="del(this,'UAC_list')"><img src="img/delete.svg"></div>
                                        </div>
                                        
                                    </div>
                                    <div class="plus-box" hidden>
                                            <div id="plus_UAC" class="plus">
                                                <div class="content_box">
                                                        <img src="img/add.svg">
                                                </div>
                                            </div>
                                        </div>
                                </div>

                                <!-- UVC -->
                                <div id="UvC_box" class="inside-box-with-pad">
                                        <label class="container">UVC <span id="UVC_day" class="black-txt2">0</span>  <span class="gray-txt">days</span>
                                                <input type="checkbox" name="UVC" value="UVC">
                                                <span class="checkmark"></span>
                                            </label>
                                            <div id="UVC_list" hidden>
                                                <div name="UVC1" class="inside-in-with-pad">
                                                        <div class="">วันเริ่มต้น</div>
                                                        <input class="input-section  input-sec5" name="UVC1_1" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="l"  placeholder="เลือกวัน">
                                                        <div class="margin-top8  ">วันสิ้นสุด</div>
                                                        <input class="input-section  input-sec5" name="UVC1_2" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="r"  placeholder="เลือกวัน">
                                                        <div class="circle-red red-sec5" onclick="del(this,'UVC_list')"><img src="img/delete.svg"></div>
                                                </div>
                                            </div>
                                        <div class="plus-box" hidden>
                                                <div id="plus_UVC" class="plus">
                                                    <div class="content_box">
                                                            <img src="img/add.svg">
                                                    </div>
                                                </div>
                                            </div>
                                    </div>


                                    <!-- PICC Line  -->
                                    <div id="PICC_line_box" class="inside-box-with-pad">
                                            <label class="container">PICC Line  <span id="PICC_line_day" class="black-txt2">0</span>  <span class="gray-txt">days</span>
                                                    <input type="checkbox" name="PICC_line" value="PICC_line">
                                                    <span class="checkmark"></span>
                                                </label>
                                                <div id="PICC_line_list" hidden>
                                                    <div name="PICC_line1" class="inside-in-with-pad">
                                                            <div class="">วันเริ่มต้น</div>
                                                            <input class="input-section input-sec5" name="PICC_line1_1" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="l"  placeholder="เลือกวัน">
                                                            <div class="margin-top8  ">วันสิ้นสุด</div>
                                                            <input class="input-section input-sec5" name="PICC_line1_2" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="r"  placeholder="เลือกวัน">
                                                            <div class="circle-red red-sec5" onclick="del(this,'PICC_line_list')"><img src="img/delete.svg"></div>
                                                    </div>
                                                </div>
                                            <div class="plus-box" hidden>
                                                    <div id="plus_PICC_line" class="plus">
                                                        <div class="content_box">
                                                                <img src="img/add.svg">
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>


                                        <!-- Other central line -->
                                        <div id="other_central_line_box" class="inside-box-with-pad">
                                                <div class="container">
                                                    <label for="other_central_line">Other central line</label> <div  id="info2" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div> <span id="other_central_line_day" class="black-txt2" style="margin-left: 27px;">0</span>  <span class="gray-txt">days</span>
                                                    <label for="other_central_line">   
                                                            <input id="other_central_line" type="checkbox" name="other_central_line" value="other_central_line">
                                                            <span class="checkmark"></span>
                                                        </label>
                                                </div>
                                                    <div id="other_central_line_list" hidden>
                                                        <div name="other_central_line1" class="inside-in-with-pad">
                                                                <div class="">วันเริ่มต้น</div>
                                                                <input class="input-section  input-sec5" name="other_central_line1_1" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="l"  placeholder="เลือกวัน">
                                                                <div class="margin-top8  ">วันสิ้นสุด</div>
                                                                <input class="input-section  input-sec5" name="other_central_line1_2" type="text" ontouchend="setting_input('date',this);" data-input="date" data-validate="r"  placeholder="เลือกวัน">
                                                                <div class="circle-red red-sec5" onclick="del(this,'other_central_line_list')"><img src="img/delete.svg"></div>
                                                        </div>
                                                    </div>
                                                <div class="plus-box" hidden>
                                                        <div id="plus_other_central_line" class="plus">
                                                            <div class="content_box">
                                                                    <img src="img/add.svg">
                                                            </div>
                                                        </div>
                                                    </div>
                                            </div>
                                    </div>            

                        <label class="container_radio">No
                            <input type="radio" name="central_line" value="No">
                            <span class="checkmark_radio"></span>
                        </label>
                        <!-- btn -->
                        <div class="save-btn" onclick="save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
 
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section5_10.js"></script>
</body>

</html>