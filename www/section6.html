<!DOCTYPE html>
<html>

<head>
    <!-- setting -->
    <title>ThaiNy</title>
    <link rel="shortcut icon" href="img/logo.svg" />

    <!-- viewport -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="user-scalable=no, initial-scale=1, maximum-scale=1, minimum-scale=1, width=device-width, viewport-fit=cover">

    <!-- css -->
    <!-- <link rel="stylesheet" type="text/css" href="css/registration.css"> -->
    <link rel="stylesheet" type="text/css" href="css/stylesheet.css">
    <link rel="stylesheet" type="text/css" href="css/section.css">

    <!-- js -->
    <script src="js/jquery-3.1.1.min.js"></script>
    <script src="js/jquery.easing.1.3.min.js"></script>
    <script type="text/javascript" src="js/jquery.transit.min.js"></script>
</head>

<body>

    <section class="header_large_title">
        <div class="content_box">
            <section class="bar_single_line">
                <div class="content_box">
                    <a href="patient_display.html" onclick="transition_page_back('patient_display.html')">
                        <section id="back_to_detail_children" class="back_section">
                            <img class="back_btn" src="img/back.svg">

                        </section>
                                       <section id="title_header" class="title-main">กลับ</section>
 </a>
                    <section class="option_section " hidden>
                        <img class="option_icon" src="img/icon1.svg">
                        <img class="option_icon" src="img/icon2.svg">

                    </section>
                </div>
            </section>
                <section class="title">Section 6</section>
            <!-- <div class="search-box">
                <img src="img/search.svg">
                <input type="text" placeholder="ค้นหาผู้ป่วย">
            </div> -->
        </div>
    </section>

    <section class="main_section_double">
        <div class="content_box">
            <div class="frame_view_padding">
                <div class="content_box">
                    <div class="list-box">
                        <div class="section-head">
                            <div>Section 6: Short-term outcomes</div><img id = "info1" src="img/info.svg">
                        </div>
                        <div class = "choose_sd" style = "color:red;"> ถ้า section 1 เลือก Refer to TNR member hospital ไม่ต้องทำ section 6</div>
                        <div class="select-head ">Discharge status</div>
                        <label class="container_radio">Alive
                            <input type="radio" name="discharge_status" value = "Alive">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop1" hidden>
                                <div class="info_box">
                                        <label class="container_radio">Discharge home
                                            <input type="radio" name="discharge_or_transfer" value = "Discharge home">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                        <div id = "info2" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                                    </div>
                           
                            <div class="inside-in" id = "pop2" hidden>
                                <div class="section-head-reg2">Date of discharge</div>
                                <input class="input-section " type="text" ontouchend="setting_input('date',this);" data-input="date"  placeholder="Date of Admission in our center" name = "discharge_date">
                                <div class="section-head-reg">At age (day(s))</div>
                                <div class = "choose_sd">คำนวณอัตโนมัติ</div>
                                <input class="input-section " type="text" placeholder="N/A Day(s)" name = "home_at_age" disabled>
                                <div class="section-head-reg">Hospital days (day(s))</div>
                                <div class = "choose_sd">คำนวณอัตโนมัติ</div>
                                <input class="input-section  margin-btm" type="text" placeholder="N/A Day(s)" name = "home_hospital_day" disabled>

                                
                                <div class="info_box">
                                        <label class="container">Without accessary
                                            <input type="checkbox" name="without_accessary" value = "Without accessary">
                                            <span class="checkmark"></span>
                                        </label>
                                        <div id = "info3" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                                    </div>

                             
                                <label class="container">With feeding tube
                                    (OG / NG / Gastrostomy)
                                    <input type="checkbox" name="with_feeding_tube" value = "With feeding tube (OG / NG / Gastrostomy)">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">With Home oxygen
                                    (O<sub>2</sub> supplement / positive pressure)
                                    <input type="checkbox" name="with_oxygen" value = "With Home oxygen  (O2 supplement / positive pressure)">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">With tracheostomy
                                    <input type="checkbox" name="with_tracheostomy" value = "With tracheostomy">
                                    <span class="checkmark"></span>
                                </label>
                            </div>

                            <div class="info_box">
                                    <label class="container_radio">Transfer to Non-member hospital
                                            <input type="radio" name="discharge_or_transfer" value = "Transfer to Non-member hospital">
                                            <span class="checkmark_radio"></span>
                                        </label>
                                    <div id = "info4" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                                </div>
                          
                            <div class="inside-box2" id = "popx" hidden>
                                <div class="section-head-reg2">Hospital name </div>
                                <input class="input-section " type="text" placeholder="Hospital name" name = "transfer_hospital_name">
                                <div class="section-head-reg2">Transfer date </div>
                                <input class="input-section " type="text" ontouchend="setting_input('date',this);" data-input="date"  placeholder="Transfer date" name = "transfer_date">
                                <div class="section-head-reg">At age (day(s))</div>
                                <div class = "choose_sd">คำนวณอัตโนมัติ</div>
                                <input class="input-section " type="text" placeholder="N/A Day(s)" name = "transfer_at_age" disabled>
                                <div class="section-head-reg">Hospital days (day(s))</div>
                                <div class = "choose_sd">คำนวณอัตโนมัติ</div>
                                <input class="input-section " type="text" placeholder="N/A Day(s)" name = "transfer_hospital_day" disabled>
                                
                                
                                <div class="section-head"> <div>Post transfer disposition</div><img id = "info5" src="img/info.svg"></div>
                                <label class="container_radio">Discharge home
                                    <input type="radio" name="post_transfer_disposition" value = "discharge_home">
                                    <span class="checkmark_radio"></span>
                                    
                                </label>
                                <div class = "inside-in" id ="popx_1" hidden>
                                        <div class="section-head-reg2">Date of discharge</div>
                                        <input class="input-section margin-btm" type="text" ontouchend="setting_input('date',this);" data-input="date"   name = "post_discharge_home_date">

                                        
                            <div class="info_box">
                                    <label class="container">Without accessary 
                                        <input type="checkbox" name="post_without_accessary" value = "Without accessary">
                                        <span class="checkmark"></span>
                                    </label>
                                    <div id = "info6" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div>
                                </div>
                                     
                                        <label class="container">With feeding tube
                                            (OG / NG / Gastrostomy)
                                            <input type="checkbox" name="post_with_feeding_tube" value = "With feeding tube">
                                            <span class="checkmark"></span>
                                        </label>
                                        <label class="container">With Home oxygen
                                            (O<sub>2</sub> supplement / positive pressure)
                                            <input type="checkbox" name="post_with_home_oxygen" value = "With Home oxygen">
                                            <span class="checkmark"></span>
                                        </label>
                                        <label class="container">With tracheostomy
                                            <input type="checkbox" name="post_with_tracheostomy" value = "With tracheostomy">
                                            <span class="checkmark"></span>
                                        </label>

                                </div>
                                <label class="container_radio">Transfer again to another hospital
                                    <input type="radio" name="post_transfer_disposition" value = "transfer_again">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div class = "inside-in" id ="popx_2" hidden>
                                        <div class="section-head-reg2">Transfer date </div>
                                        <input class="input-section margin-btm" type="text" ontouchend="setting_input('date',this);" data-input="date"   name = "transfer_again_date">
                                </div>
                                <label class="container_radio">Readmit to your hospital
                                    <input type="radio" name="post_transfer_disposition" value = "readmit">
                                    <span class="checkmark_radio"></span>
                                </label>
                                <div class = "inside-in" id ="popx_3" hidden>
                                        <div class="section-head-reg2">Readmit date </div>
                                        <input class="input-section margin-btm" type="text" ontouchend="setting_input('date',this);" data-input="date"   name = "readmit_date">
                                </div>

                                <label class="container_radio">Still in ward (at 1 year old)
                                    <input type="radio" name="post_transfer_disposition" value = "still_in_ward">
                                    <span class="checkmark_radio"></span>
                                </label>
                               
                                <label class="container_radio">Death
                                    <input type="radio" name="post_transfer_disposition" value = "death">
                                    <span class="checkmark_radio"></span>
                                </label>
                                
                            </div>
                        </div>
                        <label class="container_radio">Death
                            <input type="radio" name="discharge_status" value = "Dead">
                            <span class="checkmark_radio"></span>
                        </label>
                        <div class="inside-box" id = "pop9" hidden>

                            <div class="section-head-reg2">Death date </div>
                            <input class="input-section " type="text" ontouchend="setting_input('date',this);" data-input="date"  placeholder="Date of Admission in our center" name = "dead_date">
                            <div class="section-head-reg">At age (day(s))</div>
                            <div class = "choose_sd">คำนวณอัตโนมัติ</div>
                            <input class="input-section " type="text" placeholder="N/A Day(s)" name = "dead_age" disabled>
                            <div class="section-head-reg">Hospital days (day(s))</div>
                            <div class = "choose_sd">คำนวณอัตโนมัติ</div>
                            <input class="input-section " type="text" placeholder="N/A Day(s)" name = "dead_hospital_day" disabled>
                            <div class="section-head-reg">Cause of death</div>
                            <div class="choose_sd">เลือกได้มากกว่า 1 ข้อ</div>

                            <div class="inside-in">
                                <label class="container">Lethal congenital malformation
                                    (severe or lethal malformation that
                                    contribute to death)
                                    <input type="checkbox" name="lethal_congenital" value = "Lethal congenital malformation (severe or lethal malformation that contribute to death)">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Immaturity
                                    <input type="checkbox" name="immaturity" value = "Immaturity">
                                    <span class="checkmark"></span>
                                </label>
                                
                                <div class="inside-in-in" id = "pop10" hidden>
                                        <div class="choose_sd padding-left">เลือกได้มากกว่า 1 ข้อ</div>
                                    <label class="container">Severe IVH
                                        <input type="checkbox" name="severe" value = "Severe">
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container" hidden>
                                        <input type="checkbox" name="IVH" value = "IVH">
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">Pulmonary hemorrhage
                                        <input type="checkbox" name="pulmonary_hemorrhage" value = "Pulmonary hemorrhage">
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">NEC
                                        <input type="checkbox" name="NEC" value = "NEC">
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">PIE or airleak syndrome
                                        <input type="checkbox" name="PIE_or_airleak_syndrome" value ="PIE or airleak syndrome"> 
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">BPD
                                        <input type="checkbox" name="BPD" value = "BPD">
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">Extreme prematurity
                                        <input type="checkbox" name="extrame_prematurity" value = "Extreme prematurity" >
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">Severe RDS
                                        <input type="checkbox" name="severe_RDS" value = "Severe RDS">
                                        <span class="checkmark"></span>
                                    </label>
                                    <label class="container">Other
                                        <input type="checkbox" name="immaturity_other_main" value = "Other">
                                        <span class="checkmark"></span>
                                    </label>
                                    <input class="input-section" type="text" placeholder="Please specify" name = "immaturity_other" hidden>

                                </div>
                                <label class="container">Severe asphyxia
                                    <input type="checkbox" name="severe_asphyxia" value = "Severe asphyxia">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">PPHN
                                    <input type="checkbox" name="PPHN" value = "PPHN">
                                    <span class="checkmark"></span>
                                </label>
                                <label class="container">Infection
                                    <input type="checkbox" name="infection" value = "Infection">
                                    <span class="checkmark"></span>
                                </label>
                                
                                
                                <label class="container">Other
                                    <input type="checkbox" name="cause_other_main" value = "Other2">
                                    <span class="checkmark"></span>
                                </label>
                                <input class="input-section margin-btm" type="text" placeholder="Please specify" name = "cause_other" hidden>
                                <label class="container">Unknown
                                    <input type="checkbox" name="unknow_main" value = "Unknown">
                                    <span class="checkmark"></span>
                                </label>
                                <input class="input-section margin-btm" type="text" placeholder="Please specify" name = "unknow" hidden>
                            </div>

                        </div>


                        <div id = "popx">
                        <div class="section-head-noline margin-top">
                            <div>Growth status at 36 weeks PMA <br><span style = "font-weight: normal;">(PMA date : <span id = "PMA">xxxx</span>)</span></div>
                        </div>
                        <div class="select-head margin-top8">Weight (g)</div>
                        <input class="input-section " type="number" placeholder="Weight " name = "birth_weight">
                        <div class="select-head margin-top">Length (cm)</div>
                        <input class="input-section margin-btm" type="number" placeholder="Length" name = "length">
                        <label class="container">N/A
                            <input type="checkbox" name="length_NA" value="N/A">
                            <span class="checkmark"></span>
                        </label>
                        <div class="select-head margin-top">Head circumference (cm)</div>
                        <input class="input-section margin-btm" type="number" placeholder="Head circumference" name = "head_circumference">
                        <label class="container">N/A
                            <input type="checkbox" name="head_circumference_NA" value="N/A">
                            <span class="checkmark"></span>
                        </label>
                        </div>
                        <div class="select-head margin-top">Growth status (Fenton 2013)<div id="info8" class="box-image-icon"><div class="content_box"><img src="img/info.svg"></div></div></div>
                        <label class="container_radio">SGA (< 10<sup>th</sup> percentile) 
                            <input type="radio" name="discharge_growth_status" value = "SGA (< 10th percentile) ">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">AGA (10-90<sup>th</sup> percentile) 
                            <input type="radio" name="discharge_growth_status" value = "AGA (10-90th percentile) ">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">LGA (> 90<sup>th</sup> percentile) 
                            <input type="radio" name="discharge_growth_status" value = "LGA (> 90th percentile) ">
                            <span class="checkmark_radio"></span>
                        </label>

                        <!-- <div class="select-head margin-top"></div>
                        <div class="inside-box2">
                           <div class="graph-box"></div>
                        </div> -->

                        <div class="select-head margin-top">Discharge Feeding status <img  id = "info7"src="img/info.svg"></div>  
                        <label class="container_radio">Breast milk 100%
                            <input type="radio" name="growth_status" value = "Breast milk 100%">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Mix (Breast milk + Formula)
                            <input type="radio" name="growth_status" value = "Mix (Breast milk + Formula)">
                            <span class="checkmark_radio"></span>
                        </label>
                        <label class="container_radio">Formula 100%
                            <input type="radio" name="growth_status" value = "Formula 100%">
                            <span class="checkmark_radio"></span>
                        </label>






                        <!-- btn -->
                        <div class="save-btn" onclick = "save()">
                            <span class="set_center">บันทึก</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <script src="cordova.js"></script>
    <script src="js/transition.js"></script>
    
    <script src = "js/concurance.js"></script><script src="js/main.js"></script>
    <script src="js/section6.js"></script>
</body>

</html>