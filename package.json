{"name": "tech.vernity.thainy", "displayName": "HelloCordova", "version": "1.0.0", "description": "A sample Apache Cordova application that responds to the deviceready event.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "Apache Cordova Team", "license": "Apache-2.0", "dependencies": {"com.telerik.plugins.nativepagetransitions": "^0.6.5", "cordova-android": "^6.2.3", "cordova-plugin-device": "^2.0.2", "cordova-plugin-fingerprint-aio": "^1.5.0", "cordova-plugin-inappbrowser": "^3.0.0", "cordova-plugin-splashscreen": "^5.0.2", "cordova-plugin-taptic-engine": "^2.1.0", "cordova-plugin-vibration": "^3.1.0", "cordova-plugin-whitelist": "^1.3.3", "onesignal-cordova-plugin": "^2.4.4"}, "cordova": {"plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-fingerprint-aio": {}, "cordova-plugin-taptic-engine": {}, "cordova-plugin-vibration": {}, "cordova-plugin-splashscreen": {}, "onesignal-cordova-plugin": {}, "com.telerik.plugins.nativepagetransitions": {}, "cordova-plugin-inappbrowser": {}}, "platforms": ["android"]}}